package com.caidaocloud.attendance.service.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.wa.mybatis.mapper.EmpCompensatoryQuotaMapper;
import com.caidaocloud.attendance.service.domain.entity.EmpCompensatoryQuotaDo;
import com.caidaocloud.attendance.service.domain.repository.IEmpCompensatoryQuotaRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpCompensatoryQuotaMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryQuotaPo;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.quota.CompensatoryQuotaSearchDto;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/7/27
 */
@Repository
public class EmpCompensatoryQuotaRepository implements IEmpCompensatoryQuotaRepository {
    @Resource
    private WaEmpCompensatoryQuotaMapper waEmpCompensatoryQuotaMapper;
    @Autowired
    private EmpCompensatoryQuotaMapper empCompensatoryQuotaMapper;

    @Override
    public int save(EmpCompensatoryQuotaDo quotaDo) {
        WaEmpCompensatoryQuotaPo po = ObjectConverter.convert(quotaDo, WaEmpCompensatoryQuotaPo.class);
        return waEmpCompensatoryQuotaMapper.insertSelective(po);
    }

    @Override
    public int update(EmpCompensatoryQuotaDo quotaDo) {
        WaEmpCompensatoryQuotaPo po = ObjectConverter.convert(quotaDo, WaEmpCompensatoryQuotaPo.class);
        return waEmpCompensatoryQuotaMapper.updateByPrimaryKeySelective(po);
    }

    @Override
    public PageList<EmpCompensatoryQuotaDo> getEmpCompensatoryQuotaList(CompensatoryQuotaSearchDto dto, String tenantId) {
        val pageBean = PageUtil.getPageBean(dto);
        if (CollectionUtils.isNotEmpty(pageBean.getFilterList())) {
            Iterator<FilterBean> it = pageBean.getFilterList().iterator();
            while (it.hasNext()) {
                FilterBean filterBean = it.next();
                if ("status".equals(filterBean.getField())) {
                    dto.setStatus(Integer.valueOf(filterBean.getMin()));
                    it.remove();
                }
            }
        }
        String filter = pageBean.getFilter();
        if (filter != null) {
            if (filter.contains("orgid")) {
                filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
            }
            if (filter.contains("overtimeDate")) {
                filter = filter.replaceAll("overtimeDate", "overtime_date");
            }
            if (filter.contains("overtimeType")) {
                filter = filter.replaceAll("overtimeType", "overtime_type");
            }
            if (filter.contains("dataSource")) {
                filter = filter.replaceAll("dataSource", "data_source");
            }
        }
        Map<String, Object> params = new HashMap<>();
        params.put("filter", filter);
        params.put("datafilter", dto.getDataScope());
        if (CollectionUtils.isNotEmpty(dto.getEmpIdList())) {
            params.put("empIdList", dto.getEmpIdList());
        }
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString("overtime_date.desc"));
        val list = waEmpCompensatoryQuotaMapper.getEmpCompensatoryQuotaList(myPageBounds,
                dto.getOvertimeType(), dto.getOvertimeDate(), dto.getStatus(), tenantId, System.currentTimeMillis() / 1000, dto.getKeywords(), params);
        return new PageList<>(list.stream().map(po -> {
            val quota = new EmpCompensatoryQuotaDo();
            BeanUtils.copyProperties(po, quota);
            return quota;
        }).collect(Collectors.toList()), list.getPaginator());
    }

    @Override
    public List<EmpCompensatoryQuotaDo> getQuotaList(String tenantId, List<Integer> overtimeDetailIds) {
        if (CollectionUtils.isEmpty(overtimeDetailIds)) {
            return new ArrayList<>();
        }
        List<WaEmpCompensatoryQuotaPo> dataAllList = new ArrayList<>();
        List<List<Integer>> lists = ListTool.split(overtimeDetailIds, 500);
        for (List<Integer> list : lists) {
            QueryWrapper<WaEmpCompensatoryQuotaPo> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tenant_id", tenantId);
            queryWrapper.in("overtime_detail_id", list);
            queryWrapper.eq("deleted", 0);
            List<WaEmpCompensatoryQuotaPo> dataList = waEmpCompensatoryQuotaMapper.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(dataList)) {
                dataAllList.addAll(dataList);
            }
        }
        return ObjectConverter.convertList(dataAllList, EmpCompensatoryQuotaDo.class);
    }

    @Override
    public int save(List<EmpCompensatoryQuotaDo> list) {
        List<WaEmpCompensatoryQuotaPo> poList = ObjectConverter.convertList(list, WaEmpCompensatoryQuotaPo.class);
        return waEmpCompensatoryQuotaMapper.insertBatch(poList);
    }

    @Override
    public EmpCompensatoryQuotaDo getByQuotaId(Long quotaId) {
        WaEmpCompensatoryQuotaPo po = waEmpCompensatoryQuotaMapper.selectByPrimaryKey(quotaId);
        if (null == po || po.getDeleted() == 1) {
            return null;
        }
        return ObjectConverter.convert(po, EmpCompensatoryQuotaDo.class);
    }

    @Override
    public void delete(Long quotaId) {
        waEmpCompensatoryQuotaMapper.logicDelete(quotaId);
    }

    @Override
    public void deleteByIds(String tenantId, List<Long> quotaIds) {
        if (CollectionUtils.isEmpty(quotaIds)) {
            return;
        }
        List<List<Long>> lists = ListTool.split(quotaIds, 500);
        for (List<Long> list : lists) {
            waEmpCompensatoryQuotaMapper.logicDeleteByIds(tenantId, list);
        }
    }

    @Override
    public void batchUpdate(List<WaEmpCompensatoryQuotaPo> models) {
        if (CollectionUtils.isEmpty(models)) {
            return;
        }
        for (WaEmpCompensatoryQuotaPo po : models) {
            waEmpCompensatoryQuotaMapper.updateByPrimaryKeySelective(po);
        }
    }

    @Override
    public List<Map> queryEmpQuotaList(Map params) {
        return waEmpCompensatoryQuotaMapper.queryEmpQuotaList(params);
    }

    @Override
    public List<WaEmpCompensatoryQuotaPo> getQuotaListByIds(String tenantId, List<Long> quotaIds) {
        return waEmpCompensatoryQuotaMapper.queryQuotaListByIds(tenantId, quotaIds);
    }

    @Override
    public int updateByEmpIdAndLeaveType(String tenantId, Long empId, Integer leaveTypeId, EmpCompensatoryQuotaDo quotaDo) {
        QueryWrapper<WaEmpCompensatoryQuotaPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.in("emp_id", empId);
        queryWrapper.in("leave_type_id", leaveTypeId);
        queryWrapper.eq("deleted", 0);
        WaEmpCompensatoryQuotaPo quotaPo = ObjectConverter.convert(quotaDo, WaEmpCompensatoryQuotaPo.class);
        return waEmpCompensatoryQuotaMapper.update(quotaPo, queryWrapper);
    }

    @Override
    public List<WaEmpCompensatoryQuotaPo> getQuotaListByEmpIdAndLeaveType(String tenantId, Long empId, Integer leaveTypeId) {
        QueryWrapper<WaEmpCompensatoryQuotaPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.in("emp_id", empId);
        queryWrapper.in("leave_type_id", leaveTypeId);
        queryWrapper.eq("deleted", 0);
        return waEmpCompensatoryQuotaMapper.selectList(queryWrapper);
    }

    @Override
    public List<EmpCompensatoryQuotaDo> getQuotaListByDate(String tenantId, List<Long> empIds, Long startDate, Long endDate, String dataSource, List<Integer> status) {
        List<WaEmpCompensatoryQuotaPo> dataAllList = new ArrayList<>();
        List<List<Long>> lists = ListTool.split(empIds, 500);
        for (List<Long> list : lists) {
            QueryWrapper<WaEmpCompensatoryQuotaPo> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tenant_id", tenantId);
            queryWrapper.eq("deleted", 0);
            queryWrapper.in("emp_id", list);
            queryWrapper.ge("overtime_date",startDate);
            queryWrapper.le("overtime_date",endDate);
            if (StringUtil.isNotBlank(dataSource)) {
                queryWrapper.eq("data_source", dataSource);
            }
            if (CollectionUtils.isNotEmpty(status)) {
                queryWrapper.in("status", status);
            }
            List<WaEmpCompensatoryQuotaPo> empQuotaList = waEmpCompensatoryQuotaMapper.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(empQuotaList)) {
                dataAllList.addAll(empQuotaList);
            }
        }
        return ObjectConverter.convertList(dataAllList, EmpCompensatoryQuotaDo.class);
    }

    @Override
    public WaEmpCompensatoryQuotaPo getByOverTimeDate(String tenantId, Long empId, Long overTimeDate) {
        QueryWrapper<WaEmpCompensatoryQuotaPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.eq("deleted", 0);
        queryWrapper.eq("emp_id", empId);
        queryWrapper.eq("overtime_date",overTimeDate);
        List<WaEmpCompensatoryQuotaPo> list = waEmpCompensatoryQuotaMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<EmpCompensatoryQuotaDo> getEmpQuotas(EmpCompensatoryQuotaDo dto) {
        QueryWrapper<WaEmpCompensatoryQuotaPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", dto.getTenantId());
        queryWrapper.eq("deleted", 0);
        if (null != dto.getStatus()) {
            queryWrapper.eq("status", dto.getStatus());
        }
        if (null != dto.getEmpId()) {
            queryWrapper.eq("emp_id", dto.getEmpId());
        }
        if (null != dto.getStartDate()) {
            queryWrapper.eq("start_date", dto.getStartDate());
        }
        if (null != dto.getLastDate()) {
            queryWrapper.eq("last_date", dto.getLastDate());
        }
        if (null != dto.getDataSource()) {
            queryWrapper.eq("data_source", dto.getDataSource());
        }
        if (null != dto.getQuotaId()) {
            queryWrapper.ne("quota_id", dto.getQuotaId());
        }
        return ObjectConverter.convertList(waEmpCompensatoryQuotaMapper.selectList(queryWrapper), EmpCompensatoryQuotaDo.class);
    }

    @Override
    public int updateWaEmpCompensatoryQuota(Long userId, Long currentTime, List<Long> idList, Integer status) {
        return empCompensatoryQuotaMapper.updateWaEmpCompensatoryQuota(userId, currentTime, idList, status);
    }

    @Override
    public List<WaEmpCompensatoryQuotaPo> getApplyCompensatoryQuotaList(String tenantId, List<Long> quotaIds, Integer status, Long currentTime) {
        return waEmpCompensatoryQuotaMapper.queryApplyCompensatoryQuotaList(tenantId, quotaIds, status, currentTime);
    }

    @Override
    public List<WaEmpCompensatoryQuotaPo> getEmpCompensatoryQuotaList(List<Long> quotaIds) {
        if (null == quotaIds || quotaIds.size() == 0) {
            return new ArrayList<>();
        }
        return waEmpCompensatoryQuotaMapper.queryEmpCompensatoryQuotaList(quotaIds);
    }

    @Override
    public List<WaEmpCompensatoryQuotaPo> groupCompensatoryQuotaByEmpIdsAndTime(String tenantId, List<String> empIds, long startTime, long endTime) {
        return waEmpCompensatoryQuotaMapper.groupCompensatoryQuotaByEmpIdsAndTime(tenantId, empIds.stream().map(it->Long.valueOf(it)).collect(Collectors.toList()), startTime, endTime);
    }

    @Override
    public List<WaEmpCompensatoryQuotaPo> groupManualCompensatoryQuotaByEmpIds(String tenantId, List<String> empIds) {
        return waEmpCompensatoryQuotaMapper.groupManualCompensatoryQuotaByEmpIds(tenantId, empIds.stream().map(it->Long.valueOf(it)).collect(Collectors.toList()), System.currentTimeMillis()/1000);
    }
}
