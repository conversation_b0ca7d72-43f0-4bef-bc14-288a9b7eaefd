package com.caidaocloud.attendance.service.wfm.interfaces.facade;

import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.service.wfm.application.dto.WfmCalendarDateDto;
import com.caidaocloud.attendance.service.wfm.application.dto.WfmHolidayDto;
import com.caidaocloud.attendance.service.wfm.application.service.WfmHolidayService;
import com.caidaocloud.attendance.service.wfm.domain.entity.WfmHolidayCalendarDo;
import com.caidaocloud.attendance.service.wfm.interfaces.vo.WfmHolidayVo;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/attendance/wfm/holiday/v1")
@Api(value = "/api/attendance/wfm/holiday/v1", description = "工时管理-特殊日期")
public class WfmHolidayController {
    @Autowired
    private WfmHolidayService wfmHolidayService;

    @ApiOperation(value = "日期类型下拉列表")
    @GetMapping(value = "/getDateTypeOptions")
    public Result<ItemsResult<KeyValue>> getBaseDateTypeList() {
        List<KeyValue> items = new ArrayList<>();
        for (DateTypeEnum dateType : DateTypeEnum.values()) {
            /*if (DateTypeEnum.DATE_TYP_1 == dateType || DateTypeEnum.DATE_TYP_2 == dateType) {
                continue;
            }*/
            items.add(new KeyValue(DateTypeEnum.getName(dateType.getIndex()), dateType.getIndex()));
        }
        return ResponseWrap.wrapResult(new ItemsResult<>(items));
    }

    @PostMapping(value = "/save")
    @ApiOperation(value = "新增或修改特殊日期")
    public Result<Boolean> saveHolidayInfo(@RequestBody WfmHolidayDto dto) {
        wfmHolidayService.save(dto);
        return ResponseWrap.wrapResult(Boolean.TRUE);
    }

    @ApiOperation("查询特殊日期列表")
    @PostMapping("/list")
    public Result<ItemsResult<WfmHolidayVo>> getHolidayList() {
        List<WfmHolidayVo> items = ObjectConverter.convertList(wfmHolidayService.getList(), WfmHolidayVo.class);
        return Result.ok(new ItemsResult<>(items));
    }

    @ApiOperation("查询特殊日期详情")
    @GetMapping("/detail")
    public Result<WfmHolidayVo> getHolidayInfo(@RequestParam("id") Long id) {
        WfmHolidayCalendarDo calendarDo = wfmHolidayService.getById(id);
        if (null == calendarDo) {
            return Result.ok(new WfmHolidayVo());
        }
        WfmHolidayVo vo = ObjectConverter.convert(calendarDo, WfmHolidayVo.class);
        vo.setI18nCalendarName(calendarDo.getI18nCalendarNameMap());
        return Result.ok(vo);
    }

    @DeleteMapping(value = "/delete")
    @ApiOperation(value = "删除特殊日期")
    public Result<Boolean> deleteHolidayById(@RequestParam("id") Long id) {
        wfmHolidayService.deleteById(id);
        return Result.ok(Boolean.TRUE);
    }

    @ApiOperation("根据时间范围查询特殊日期")
    @GetMapping(value = "/listDate")
    public Result<List<WfmCalendarDateDto>> listDate(@RequestParam("start") Long start,
                                                     @RequestParam("end") Long end) {
        return Result.ok(wfmHolidayService.getCalendarDateList(UserContext.getTenantId(), start, end));
    }
}
