package com.caidaocloud.attendance.service.infrastructure.repository.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.entity.AttEmpGroupDo;
import com.caidaocloud.attendance.service.domain.repository.IEmpGroupRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.AttEmpGroupMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpGroupPo;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.group.AttEmpGroupDto;
import com.caidaocloud.attendance.service.interfaces.dto.group.AttEmpGroupReqDto;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.github.miemiedev.mybatis.paginator.domain.Paginator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class EmpGroupRepository implements IEmpGroupRepository {

    @Resource
    private AttEmpGroupMapper attEmpGroupMapper;

    @Override
    public AttendancePageResult<AttEmpGroupDo> getWaEmpGroupList(AttEmpGroupReqDto dto) {
        Map params = new HashMap();
        PageBean pageBean = PageUtil.getPageBean(dto);
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        params.put("filter", filter);
        params.put("datafilter", dto.getDataScope());
        if (StringUtils.isNotBlank(dto.getKeywords())) {
            params.put("keywords", "('"+org.apache.commons.lang3.StringUtils.join(dto.getKeywords().split(" "), "','")+"')");
        }
        params.put("belongOrgId", dto.getBelongOrgId());
        if (StringUtils.isNotBlank(dto.getEffectiveStatus())) {
            params.put("nowTime", DateUtil.getOnlyDate());
            params.put("effectiveStatus", dto.getEffectiveStatusFilterValue());
        }
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        PageList<WaEmpGroupPo> waEmpGroupList = attEmpGroupMapper.getWaEmpGroupList(myPageBounds, params);
        if (CollectionUtils.isNotEmpty(waEmpGroupList)) {
            List<AttEmpGroupDo> list = JSON.parseArray(JSON.toJSONString(waEmpGroupList), AttEmpGroupDo.class);
            Paginator paginator = waEmpGroupList.getPaginator();
            return new AttendancePageResult<>(list, dto.getPageNo(), dto.getPageSize(), paginator.getTotalCount());
        }
        return new AttendancePageResult<>(new ArrayList<>(), dto.getPageNo(), dto.getPageSize(), 0);
    }

    @Override
    public List<AttEmpGroupDo> getWaEmpGroupListByPeriod(AttEmpGroupDto dto) {
        Map map = new HashMap();
        map.put("empId", null != dto.getEmpInfo() ? dto.getEmpInfo().getEmpId() : dto.getEmpId());
        map.put("startTime", dto.getStartTime());
        map.put("endTime", dto.getEndTime());
        map.put("empGroupId", dto.getEmpGroupId());
        map.put("belongOrgId", dto.getBelongOrgId());
        map.put("waGroupId", dto.getWaGroupId());
        List<WaEmpGroupPo> list = attEmpGroupMapper.queryWaEmpGroupByPeriod(map);
        List<AttEmpGroupDo> attEmpGroupDos = ObjectConverter.convertList(list, AttEmpGroupDo.class);
        return attEmpGroupDos;
    }

    @Override
    public AttEmpGroupDo getWaEmpGroupById(Integer empGroupId) {
        QueryWrapper<WaEmpGroupPo> wrapper = new QueryWrapper<>();
        wrapper.eq("emp_group_id", empGroupId);
        WaEmpGroupPo waEmpGroupPo = attEmpGroupMapper.selectOne(wrapper);
        AttEmpGroupDo groupDo = ObjectConverter.convert(waEmpGroupPo, AttEmpGroupDo.class);
        return groupDo;
    }

    @Override
    public void deleteWaEmpGroup(List<Integer> ids) {
        QueryWrapper<WaEmpGroupPo> wrapper = new QueryWrapper<>();
        wrapper.in("emp_group_id", ids);
        attEmpGroupMapper.delete(wrapper);
    }

    @Override
    public void saveWaEmpGroup(WaEmpGroupPo waEmpGroupPo) {
        attEmpGroupMapper.insert(waEmpGroupPo);
    }

    @Override
    public void updateWaEmpGroup(WaEmpGroupPo waEmpGroupPo) {
        QueryWrapper<WaEmpGroupPo> wrapper = new QueryWrapper<>();
        wrapper.eq("emp_group_id", waEmpGroupPo.getEmpGroupId());
        attEmpGroupMapper.update(waEmpGroupPo, wrapper);
    }

    @Override
    public List<WaEmpGroupPo> getWaEmpGroupByIds(List<Integer> empGroupIds) {
        return attEmpGroupMapper.selectBatchIds(empGroupIds);
    }

    @Override
    public List<WaEmpGroupPo> getEmpGroup(String belongOrgId, Long empId, Long currentTime) {
        Map map = new HashMap();
        map.put("belongOrgId", belongOrgId);
        map.put("empId", empId);
        map.put("currentTime", currentTime);
        return attEmpGroupMapper.queryEmpGroupByPeriod(map);
    }

    @Override
    public List<WaEmpGroupPo> getEmpGroupByEmpIds(String belongOrgId, List<Long> empIds, Long currentTime) {
        Map map = new HashMap();
        map.put("belongOrgId", belongOrgId);
        map.put("empIds", empIds);
        map.put("currentTime", currentTime);
        return attEmpGroupMapper.queryEmpGroupByPeriod(map);
    }

    @Override
    public List<WaEmpGroupPo> getEmpGroupByEmpIds(String tenantId, List<Long> empIds, Long startDate, Long endDate) {
        String anyEmpIds = "'{" + StringUtils.join(empIds, ",").concat("}'");
        return attEmpGroupMapper.queryEmpGroupByPeriodAndEmpIds(tenantId, anyEmpIds, startDate, endDate);
    }
}
