<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpInfoPoMapper">
    <resultMap id="BaseResultMap" type="com.caidao1.auth.mybatis.model.SysEmpInfo">
        <id column="empid" jdbcType="BIGINT" property="empid"/>
        <result column="belong_org_id" jdbcType="VARCHAR" property="belongOrgId"/>
        <result column="orgid" jdbcType="INTEGER" property="orgid"/>
        <result column="post_id" jdbcType="INTEGER" property="postId"/>
        <result column="emp_name" jdbcType="VARCHAR" property="empName"/>
        <result column="eng_name" jdbcType="VARCHAR" property="engName"/>
        <result column="gender" jdbcType="BIGINT" property="gender"/>
        <result column="corpid" jdbcType="BIGINT" property="corpid"/>
        <result column="payroll_stats" jdbcType="INTEGER" property="payrollStats"/>
        <result column="stats" jdbcType="INTEGER" property="stats"/>
        <result column="workno" jdbcType="VARCHAR" property="workno"/>
        <result column="photo_url" jdbcType="VARCHAR" property="photoUrl"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="office_tel" jdbcType="VARCHAR" property="officeTel"/>
        <result column="crtuser" jdbcType="BIGINT" property="crtuser"/>
        <result column="crttime" jdbcType="BIGINT" property="crttime"/>
        <result column="upduser" jdbcType="BIGINT" property="upduser"/>
        <result column="updtime" jdbcType="BIGINT" property="updtime"/>
        <result column="isexsit_account" jdbcType="BIT" property="isexsitAccount"/>
        <result column="employ_type" jdbcType="BIGINT" property="employType"/>
        <result column="hire_date" jdbcType="BIGINT" property="hireDate"/>
        <result column="first_work_date" jdbcType="BIGINT" property="firstWorkDate"/>
        <result column="fixed_num" jdbcType="VARCHAR" property="fixedNum"/>
        <result column="job_grade" jdbcType="INTEGER" property="jobGrade"/>
        <result column="ee_type" jdbcType="INTEGER" property="eeType"/>
        <result column="job_title" jdbcType="INTEGER" property="jobTitle"/>
        <result column="prodead_line" jdbcType="BIGINT" property="prodeadLine"/>
        <result column="worktime_type" jdbcType="INTEGER" property="worktimeType"/>
        <result column="work_type" jdbcType="INTEGER" property="workType"/>
        <result column="termination_date" jdbcType="BIGINT" property="terminationDate"/>
        <result column="termination_type" jdbcType="INTEGER" property="terminationType"/>
        <result column="termination_reason" jdbcType="INTEGER" property="terminationReason"/>
        <result column="termination_reason_desc" jdbcType="VARCHAR" property="terminationReasonDesc"/>
        <result column="used_name" jdbcType="VARCHAR" property="usedName"/>
        <result column="labor_dispatch" jdbcType="VARCHAR" property="laborDispatch"/>
        <result column="tm_type" jdbcType="INTEGER" property="tmType"/>
        <result column="cost_items" jdbcType="OTHER" property="costItems"/>
        <result column="job_level" jdbcType="INTEGER" property="jobLevel"/>
        <result column="ext_custom_col" jdbcType="OTHER" property="extCustomCol"/>
        <result column="store_id" jdbcType="INTEGER" property="storeId"/>
        <result column="friends" jdbcType="ARRAY" property="friends"/>
        <result column="site_id" jdbcType="INTEGER" property="siteId"/>
        <result column="belongcorp_id" jdbcType="INTEGER" property="belongcorpId"/>
        <result column="leader_empid" jdbcType="BIGINT" property="leaderEmpid"/>
        <result column="career_seq" jdbcType="INTEGER" property="careerSeq"/>
        <result column="belong_field" jdbcType="INTEGER" property="belongField"/>
        <result column="is_mgt" jdbcType="INTEGER" property="isMgt"/>
        <result column="is_gj" jdbcType="INTEGER" property="isGj"/>
        <result column="job_duty" jdbcType="VARCHAR" property="jobDuty"/>
        <result column="referee_emp_id" jdbcType="INTEGER" property="refereeEmpId"/>
        <result column="struct_id" jdbcType="INTEGER" property="structId"/>
        <result column="siteids" jdbcType="VARCHAR" property="siteids"/>
        <result column="orders" jdbcType="VARCHAR" property="orders"/>
        <result column="job_age_deduct" jdbcType="REAL" property="jobAgeDeduct"/>
        <result column="hire_type" jdbcType="INTEGER" property="hireType"/>
        <result column="is_disability" jdbcType="INTEGER" property="isDisability"/>
        <result column="is_martyrs_family" jdbcType="INTEGER" property="isMartyrsFamily"/>
        <result column="is_lonely_elder" jdbcType="INTEGER" property="isLonelyElder"/>
        <result column="disability_card_no" jdbcType="VARCHAR" property="disabilityCardNo"/>
        <result column="martyrs_family_card_no" jdbcType="VARCHAR" property="martyrsFamilyCardNo"/>
        <result column="first_hire_date" jdbcType="BIGINT" property="firstHireDate"/>
        <result column="social_pay_months" jdbcType="INTEGER" property="socialPayMonths"/>
    </resultMap>
    <select id="selectGroupEmpIds" resultType="long">
        SELECT distinct ei.empid
        FROM sys_emp_info ei
        LEFT JOIN sys_emp_privacy ep on ei.empid = ep.empid
        LEFT JOIN wa_emp_leave wel on wel.empid = ei.empid
        WHERE ei.deleted=0
        <if test="corpId != null">
            AND ei.corpid=#{corpId}
        </if>
        <if test="tenantId != null">
            AND ei.belong_org_id=#{tenantId}
        </if>
        <if test="leaveId != null">
            AND wel.leave_id=#{leaveId}
        </if>
        <if test="empId != null">
            AND ei.empid=#{empId}
        </if>
        <if test="leaveTypeId != null">
            AND wel.leave_type_id=#{leaveTypeId}
        </if>
        <if test="groupExp != null and groupExp != ''">
            AND (${groupExp})
        </if>
    </select>
    <select id="selectLeaveIdsByGroupExp" resultType="integer">
        SELECT wel.leave_id
        FROM sys_emp_info ei
        JOIN wa_emp_leave wel on wel.empid = ei.empid
        LEFT JOIN sys_emp_privacy ep on ei.empid = ep.empid
        WHERE ei.deleted=0
        <if test="corpId != null">
            AND ei.corpid=#{corpId}
        </if>
        <if test="tenantId != null">
            AND ei.belong_org_id=#{tenantId}
        </if>
        <if test="leaveId != null">
            AND wel.leave_id=#{leaveId}
        </if>
        <if test="empId != null">
            AND ei.empid=#{empId}
        </if>
        <if test="leaveTypeId != null">
            AND wel.leave_type_id=#{leaveTypeId}
        </if>
        <if test="groupExp != null and groupExp != ''">
            AND (${groupExp})
        </if>
    </select>
    <select id="getGroupEmpIds" resultType="long">
        SELECT
        ei.empid
        FROM
        sys_emp_info ei
        LEFT JOIN sys_emp_privacy ep on ei.empid = ep.empid
        WHERE ei.corpid = #{corpId} and ei.deleted=0
        <if test="groupExp != null and groupExp != ''">
            AND (${groupExp})
        </if>
    </select>
    <select id="getQuotaRuleEmpList" resultType="java.util.Map">
        SELECT
        ei.empid,ei.hire_date as "hireDate", ei.prodead_line as "prodeadLine",ep.marriage, ei.termination_date as
        "terminationDate",ei.stats as "empStatus"
        <if test="midDate != null">
            , ${midDate} as "midDate"
        </if>
        <if test="firstDate != null">
            , ${firstDate} as "firstDate"
        </if>
        <if test="dateStr != null">
            <choose>
                <when test="dateStr.contains('+86400')">
                    , ${dateStr} - 86400 as "criticalPoint"
                </when>
                <otherwise>
                    , ${dateStr} as "criticalPoint"
                </otherwise>
            </choose>
        </if>
        FROM
        sys_emp_info ei
        JOIN wa_emp_group_view eg ON ei.empid = eg.empid
        JOIN wa_group wg ON eg.wa_group_id = wg.wa_group_id
        LEFT JOIN sys_emp_privacy ep ON ep.empid = ei.empid
        <where>
            ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR} AND ei.deleted = 0 AND #{leaveTypeId} =
            ANY(wg.leave_type_ids)
            <if test="groupExp != null and groupExp != ''">
                ${groupExp}
            </if>
            <if test="gender != null">
                AND ei.gender = #{gender}
            </if>
            <if test="empids!=null and empids.size>0">
                AND ei.empid in
                <foreach collection="empids" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
        </where>
    </select>
    <select id="getEmpIdListByWaGroup" parameterType="map" resultType="long">
        <choose>
            <when test="isDefault">
                <![CDATA[
                select empid
                from sys_emp_info emp
                where
                not  exists (
                select g.empid from wa_emp_group g INNER JOIN sys_emp_info e on g.empid=e.empid where e.belong_org_id=#{belongid}
                and g.start_time <=  #{endDate} and g.end_time >= #{startDate}
                and g.empid = emp.empid and g.wa_group_id <> #{waGroupId}
                )
                and emp.belong_org_id=#{belongid}
                and ((emp.stats <> 1 and #{currentDate} >= emp.hire_date) or (emp.stats = 1 and #{startDate} <= emp.termination_date))
                ]]>
                <if test="datafilter != null and datafilter != ''">
                    ${datafilter}
                </if>
            </when>
            <otherwise>
                select
                g.empid
                from wa_emp_group g
                INNER JOIN sys_emp_info emp on g.empid = emp.empid where wa_group_id = #{waGroupId} and
                emp.belong_org_id=#{belongid} and emp.deleted = 0
                <![CDATA[
                and g.start_time <=  #{endDate} and g.end_time >= #{startDate}
                and ((emp.stats <> 1 and #{currentDate} >= emp.hire_date) or (emp.stats = 1 and  #{startDate} <= emp.termination_date))
                ]]>
                <if test="datafilter != null and datafilter != ''">
                    ${datafilter}
                </if>
            </otherwise>
        </choose>
    </select>
    <select id="getEmpInfoListByGroupExp" resultType="java.util.Map">
        SELECT
        ei.empid,ei.hire_date as "hireDate", ei.prodead_line as "prodeadLine", ei.termination_date as "terminationDate"
        <if test="midDate != null">
            , ${midDate} as "midDate"
        </if>
        <if test="firstDate != null">
            , ${firstDate} as "firstDate"
        </if>
        FROM
        sys_emp_info ei
        LEFT JOIN sys_emp_privacy ep ON ep.empid = ei.empid
        <where>
            ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR} AND ei.deleted = 0
            <if test="groupExp != null and groupExp != ''">
                ${groupExp}
            </if>
            <if test="gender != null">
                AND ei.gender = #{gender}
            </if>
        </where>
    </select>
    <select id="getEmpInfoListForCalendar2" resultType="java.util.Map">
        SELECT
        ei.emp_name as "name",ei.hire_date as "hireDate",
        ei.workno,
        ei.empid,
        '-' as "week"
        FROM
        sys_emp_info ei
        <where>
            ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR} and ei.deleted = 0
            and ei.empid not in
            (SELECT distinct es.empid
            FROM wa_emp_shift es
            JOIN wa_worktime ww ON ww.work_calendar_id = es.work_calendar_id
            WHERE ww.worktime_type in (2, 3)
            and ww.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
            <if test="startDate != null and endDate != null">
                AND #{startDate} <![CDATA[<=]]> es.end_time
                AND #{endDate} <![CDATA[>=]]> es.start_time
            </if>
            )
            <if test="datafilter != null and datafilter != ''">
                ${datafilter}
            </if>
            <if test="startDate != null">
                <![CDATA[and (ei.stats <> 1 or (ei.stats = 1 and ei.termination_date >= #{startDate}))]]>
            </if>
            <if test="orgId != null and orgId != ''">
                and ei.orgid in (select * from getsuborgstr('{${orgId}}'))
            </if>
            <if test="keywords != null and keywords != ''">
                and (ei.workno like concat('%', #{keywords}, '%') or ei.emp_name like concat('%', #{keywords}, '%'))
            </if>
        </where>
    </select>

    <select id="getEmpInfoListForCalendar" resultType="java.util.Map">
        WITH has_calendar_empids AS (
        SELECT DISTINCT es.empid
        FROM wa_emp_shift es
        JOIN wa_worktime ww ON es.work_calendar_id=ww.work_calendar_id AND ww.worktime_type=1 AND ww.belong_orgid=#{belongOrgId,jdbcType=VARCHAR}
        <where>
            <if test="startDate != null and endDate != null">
                AND es.start_time <![CDATA[<=]]> #{endDate} AND es.end_time <![CDATA[>=]]> #{startDate}
            </if>
        </where>
        ),
        no_calendar_empids AS (
        SELECT ei.empid
        FROM sys_emp_info ei
        LEFT JOIN wa_emp_shift es ON ei.empid = es.empid
        <where>
            AND ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR} AND ei.deleted = 0
            <if test="startDate != null">
                <![CDATA[AND (ei.stats<>1 OR (ei.stats=1 AND ei.termination_date>=#{startDate})) AND es.empid IS NULL]]>
            </if>
        </where>)
        SELECT ei.emp_name AS "name",ei.hire_date AS "hireDate",ei.workno,ei.empid,'-' AS "week"
        FROM sys_emp_info ei
        <where>
            AND ei.belong_org_id=#{belongOrgId,jdbcType=VARCHAR} AND ei.deleted=0
            <if test="datafilter != null and datafilter != ''">
                ${datafilter}
            </if>
            <if test="startDate != null">
                <![CDATA[AND (ei.stats<>1 OR (ei.stats=1 AND ei.termination_date >=#{startDate}))]]>
            </if>
            AND (ei.empid IN (SELECT empid FROM has_calendar_empids) OR ei.empid IN (SELECT empid FROM no_calendar_empids))
            <if test="orgId != null and orgId != ''">
                and ei.orgid in (select * from getsuborgstr('{${orgId}}'))
            </if>
            <if test="keywords != null and keywords != ''">
                and (ei.workno like concat('%', #{keywords}, '%') or ei.emp_name like concat('%', #{keywords}, '%'))
            </if>
        </where>
        ORDER BY ei.empid;
    </select>

    <select id="queryEmpInfoByIds" resultMap="BaseResultMap">
        select * from sys_emp_info
        where belong_org_id = #{belongOrgId,jdbcType=VARCHAR} and deleted = 0
        <if test="empIds != null and empIds.size() > 0">
            and empid in
            <foreach collection="empIds" item="empId" index="index" open="(" close=")" separator=",">
                #{empId}
            </foreach>
        </if>
        order by workno desc
    </select>

    <select id="getEmpList" resultType="com.caidaocloud.attendance.service.domain.entity.SysEmpInfoDo">
        select empid, workno, emp_name
        from sys_emp_info
        where belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
          and deleted = 0
    </select>

    <select id="queryEmpInfoByWorkNos" resultMap="BaseResultMap">
        select * from sys_emp_info
        where belong_org_id = #{belongOrgId,jdbcType=VARCHAR} and deleted = 0
        and workno in
        <foreach collection="workNos" item="workNo" index="index" open="(" close=")" separator=",">
            #{workNo}
        </foreach>
    </select>

    <select id="getEmpIdsByCost" resultType="java.lang.Long">
        select empid
        from attendance.sys_emp_info
        where json_array_element(COALESCE(cost_items::json, '[{}]'::json), 0) ->> 'cost' = #{costId}
          AND belong_org_id = #{belongOrgId}
    </select>

    <select id="queryEmpList" resultType="com.caidaocloud.attendance.service.domain.entity.SysEmpInfoDo">
        select empid, workno, emp_name as empName
        from sys_emp_info
        where belong_org_id = #{tenantId,jdbcType=VARCHAR} and deleted = 0
        <if test="dataFilter != null and dataFilter != ''">
            ${dataFilter}
        </if>
        <if test="keywords != null and keywords != ''">
            and (workno like concat('%', #{keywords}, '%') or emp_name like concat('%', #{keywords}, '%'))
        </if>
    </select>

    <select id="selectEmpDtoList" resultType="com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO">
        select empid as "empId", workno as "workno", emp_name as "name",eng_name as "enName"
        from sys_emp_info
        where belong_org_id = #{tenantId,jdbcType=VARCHAR} and deleted = 0
        and empid in
        <foreach collection="empIdList" item="empId" index="index" open="(" close=")" separator=",">
            #{empId}
        </foreach>
    </select>

    <select id="queryEmpListFilter" resultType="long">
        select distinct sei.empid
        from sys_emp_info sei
        <if test="(shiftGroupIds != null and !shiftGroupIds.isEmpty()) or (dataFilter != null and dataFilter.contains('shift_group_id'))">
            join wa_emp_shift_group esg on esg.emp_id=sei.empid
        </if>
        where belong_org_id = #{tenantId,jdbcType=VARCHAR} and sei.deleted = 0
        <if test="dataFilter != null and dataFilter != ''">
            ${dataFilter}
        </if>
        <if test="keywords != null and keywords != ''">
            and (workno like concat('%', #{keywords}, '%') or emp_name like concat('%', #{keywords}, '%'))
        </if>
        <if test="shiftGroupIds != null and !shiftGroupIds.isEmpty()">
            and shift_group_id in
            <foreach collection="shiftGroupIds" item="shiftGroupId" open="(" close=")" separator=",">
                #{shiftGroupId}
            </foreach>
        </if>
        <if test="orgIds != null">
            and sei.orgid in
            <foreach collection="orgIds" item="orgId" open="(" close=")" separator=",">
                #{orgId}
            </foreach>
        </if>
        <if test="(shiftGroupIds != null and !shiftGroupIds.isEmpty()) or (dataFilter != null and dataFilter.contains('shift_group_id'))">
            <if test="startDate!=null and endDate!=null">
                and start_date <![CDATA[<=]]> #{endDate} and end_date <![CDATA[>=]]> #{startDate}
            </if>
        </if>
    </select>
    <select id="getEmpHireDateList" resultType="hashmap">
        select
        empid as "empId", hire_date as "hireDate"
        FROM sys_emp_info
        WHERE belong_org_id = #{tenantId,jdbcType=VARCHAR} and deleted = 0 and hire_date is not null
        <if test="empIdList != null and empIdList.size() > 0">
            and empid in
            <foreach collection="empIdList" item="empId" index="index" open="(" close=")" separator=",">
                #{empId}
            </foreach>
        </if>
    </select>
    <select id="getEmpHireDateList" resultType="hashmap">
        select
        empid as "empId", hire_date as "hireDate"
        FROM sys_emp_info
        WHERE belong_org_id = #{tenantId,jdbcType=VARCHAR} and deleted = 0 and hire_date is not null
        <if test="empIdList != null and empIdList.size() > 0">
            and empid in
            <foreach collection="empIdList" item="empId" index="index" open="(" close=")" separator=",">
                #{empId}
            </foreach>
        </if>
    </select>
</mapper>