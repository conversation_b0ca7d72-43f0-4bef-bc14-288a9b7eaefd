package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.service.AsynExecListener;
import com.caidao1.commons.service.AsyncExecService;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JsonSerializeHelper;
import com.caidao1.wa.mybatis.model.WaRegisterRecord;
import com.caidao1.wa.service.WaRegisterRecordService;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.dto.clock.ClockAnalyseDto;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.constant.AuthDataScopeCode;
import com.caidaocloud.attendance.service.application.dto.AnalyzeResultCalculateDto;
import com.caidaocloud.attendance.service.application.service.IClockSignService;
import com.caidaocloud.attendance.service.application.service.IRegisterRecordBdkService;
import com.caidaocloud.attendance.service.application.service.IRegisterRecordService;
import com.caidaocloud.attendance.service.application.service.impl.AnalyzeResultCalculateService;
import com.caidaocloud.attendance.service.application.service.impl.ClockAnalyseProgressUpd;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.infrastructure.common.AttendanceEngineMessage;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.vo.BdkRegisterRecordVo;
import com.caidaocloud.attendance.service.interfaces.vo.RegisterRecordAnalyzeVo;
import com.caidaocloud.attendance.service.interfaces.vo.RegisterRecordVo;
import com.caidaocloud.attendance.service.interfaces.vo.register.OutworkRegisterRecordVo;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.annotation.Security;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 考勤记录
 *
 * <AUTHOR>
 * @Date 2021/2/28
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/register/v1")
@Api(value = "/api/attendance/register/v1", description = "考勤记录")
public class RegisterRecordController {
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaRegisterRecordService waRegisterRecordService;
    @Autowired
    private IRegisterRecordService registerRecordService;
    @Autowired
    private IRegisterRecordBdkService registerRecordBdkService;
    @Autowired
    private IClockSignService clockSignService;
    @Autowired
    private AnalyzeResultCalculateService analyzeResultCalculateService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private AsyncExecService asyncService;

    private static final String ASYNC_ANALYSE_MSG_PROCESS = "ASYNC_ANALYSE_MSG_PROCESS_";
    private static final String ASYNC_ANALYSE_LOCK_PREFIX = "ASYNC_ANALYSE_LOCK_";

    @ApiOperation("获取上下班打卡记录分页列表")
    @PostMapping(value = "/list")
    @Security(code = "RegisterRecordList")
    public Result<AttendancePageResult<RegisterRecordVo>> getRegisterRecordPageList(@RequestBody RegisterRecordRequestDto requestDto, HttpServletRequest request) throws Exception {
        requestDto.setTypes(new ArrayList<>(Arrays.asList(1, 2, 4, 5)));
        requestDto.setAnalyze(Boolean.TRUE);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.REGISTER_RECORD_LIST, "sei");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                    .replaceAll("empid", "sei.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        log.info("获取上下班打卡记录分页列表 dataScope = {}", dataScope);
        AttendancePageResult<RegisterRecordDto> pageResult = registerRecordService.getRegisterRecordPageList(requestDto);
        List<RegisterRecordVo> voList = ObjectConverter.convertList(pageResult.getItems(), RegisterRecordVo.class);
        return ResponseWrap.wrapResult(new AttendancePageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @ApiOperation("获取补卡记录分页列表")
    @PostMapping(value = "/getBdkList")
    @Security(code = "RegisterBdkRecordList")
    public Result<AttendancePageResult<BdkRegisterRecordVo>> getBdkList(@RequestBody RegisterRecordRequestDto requestDto, HttpServletRequest request) throws Exception {
        requestDto.setTypes(new ArrayList<>(Arrays.asList(6)));
        requestDto.setAnalyze(Boolean.TRUE);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.REGISTER_BDK_RECORD_LIST, "sei");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                    .replaceAll("empid", "sei.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        log.info("获取补卡记录分页列表 dataScope = {}", dataScope);
        AttendancePageResult<RegisterRecordBdkDto> pageResult = registerRecordBdkService.getRegisterRecordPageList(requestDto);
        List<BdkRegisterRecordVo> voList = ObjectConverter.convertList(pageResult.getItems(), BdkRegisterRecordVo.class);
        return ResponseWrap.wrapResult(new AttendancePageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @ApiOperation("获取GPS、蓝牙、wifi、外勤卡、补打卡记录分页列表")
    @PostMapping(value = "/getAllList")
    @Security(code = "AllRegisterBdkRecordList")
    public Result<AttendancePageResult<RegisterRecordVo>> getAllRegisterRecordPageList(@RequestBody RegisterRecordRequestDto requestDto, HttpServletRequest request) throws Exception {
        requestDto.setIfShowAll(Boolean.TRUE);
        requestDto.setAnalyze(Boolean.TRUE);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.REGISTER_RECORD_LIST, "sei");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                    .replaceAll("empid", "sei.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        log.info("获取GPS、蓝牙、wifi、外勤卡、补打卡记录分页列表 dataScope = {}", dataScope);
        try {
            AttendancePageResult<RegisterRecordDto> pageResult = registerRecordService.getRegisterRecordPageList(requestDto);
            List<RegisterRecordVo> voList = ObjectConverter.convertList(pageResult.getItems(), RegisterRecordVo.class);
            return ResponseWrap.wrapResult(new AttendancePageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
        } catch (Exception e) {
            log.error("获取GPS、蓝牙、wifi、外勤卡、补打卡记录分页列表异常：{}", e.getMessage(), e);
            return Result.fail();
        }
    }

    @ApiOperation("获取每日考勤分页列表")
    @PostMapping(value = "/getAnalyzeRegisterList")
    @Security(code = "RegisterAnalyzeRecordList")
    public Result<AttendancePageResult<RegisterRecordAnalyzeVo>> getAnalyzeRegisterList(@RequestBody RegisterRecordRequestDto requestDto) throws Exception {
        requestDto.setTypes(new ArrayList<>(Arrays.asList(1, 2, 4, 5, 6)));
        requestDto.setShowType(1);

        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.REGISTER_ANALYZE_RECORD_LIST, "sei");
        requestDto.setDataScope(dataScope);
        log.info("获取每日考勤分页列表 dataScope = {}", dataScope);

        AttendancePageResult<RegisterRecordDto> pageResult = registerRecordService.getRegisterRecordPageList(requestDto);
        List<RegisterRecordAnalyzeVo> voList = ObjectConverter.convertList(pageResult.getItems(), RegisterRecordAnalyzeVo.class);
        return ResponseWrap.wrapResult(new AttendancePageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @ApiOperation(value = "修改打卡记录")
    @PostMapping(value = "/updateRegTime")
    public Result<Boolean> updateRegTime(@RequestBody RegisterRecordDto registerRecordDto) throws Exception {
        UserInfo userInfo = sessionService.getUserInfo();
        WaRegisterRecord record = ObjectConverter.convert(registerRecordDto, WaRegisterRecord.class);
        record.setUpdtime(DateUtil.getCurrentTime(true));
        record.setUpduser(userInfo.getUserId());
        waRegisterRecordService.updateRegTime(record);
        return ResponseWrap.wrapResult(Boolean.TRUE);
    }

    @ApiOperation(value = "删除打卡记录校验")
    @GetMapping(value = "/checkWaRegRecord")
    public Result checkWaRegRecord(@RequestParam("id") Integer id) throws Exception {
        UserInfo userInfo = sessionService.getUserInfo();
        return ResponseWrap.wrapResult(waRegisterRecordService.checkWaRegRecord(ConvertHelper.longConvert(ConvertHelper.longConvert(userInfo.getTenantId())), userInfo.getTenantId(), id));
    }

    @ApiOperation(value = "删除打卡记录")
    @DeleteMapping(value = "/delete")
    @LogRecordAnnotation(success = "删除了", category = "删除", menu = "打卡记录-日常打卡")
    public Result<Boolean> delWaRegRecord(@RequestParam("id") Integer id) {
        UserInfo userInfo = sessionService.getUserInfo();
        //数据删除校验
        try {
            boolean flag = waRegisterRecordService.checkWaRegRecord(ConvertHelper.longConvert(ConvertHelper.longConvert(userInfo.getTenantId())), userInfo.getTenantId(), id);
            if (!flag) {
                return Result.fail("打卡记录已统计在本月考勤中，无法删除");
            }
            waRegisterRecordService.delWaRegRecord(ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId(), id);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("RegisterRecordController.delWaRegRecord has exception {}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "撤销员工签到记录")
    @PostMapping(value = "/revokeEmpReg")
    public Result<Boolean> revokeEmpReg(@RequestBody RegisterRecordRevoke dto) {
        try {
            return registerRecordService.revokeEmpReg(dto.getId(), dto.getRevokeReason());
        } catch (Exception e) {
            log.error("RegisterRecordController.revokeEmpReg has exception {}", e.getMessage(), e);
            return Result.fail();
        }
    }

//    @ApiOperation(value = "查询审批详情")
//    @GetMapping(value = "/getRegisterWorkFlowViews")
//    public Result getRegisterWorkFlowViews(@RequestParam("businessKey") String businessKey) throws Exception {
//        Map map = waRegisterRecordService.getRegisterWorkFlowViews(businessKey);
//        return ResponseWrap.wrapResult(map);
//    }

    @ApiOperation("获取外勤记录分页列表")
    @PostMapping(value = "/getOutworkRegisterRecords")
    @Security(code = AuthDataScopeCode.OUTWORK_REGISTER_RECORD_LIST)
    public Result<AttendancePageResult<OutworkRegisterRecordVo>> getOutworkRegisterRecords(@RequestBody RegisterRecordRequestDto requestDto, HttpServletRequest request) {
        try {
            requestDto.setTypes(new ArrayList<>(Arrays.asList(3)));
            requestDto.setAnalyze(Boolean.TRUE);
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.OUTWORK_REGISTER_RECORD_LIST, "sei");
            requestDto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                        .replaceAll("empid", "sei.empid");
                requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
            }
            log.info("获取外勤记录分页列表 dataScope = {}", dataScope);
            AttendancePageResult<RegisterRecordDto> pageResult = registerRecordService.getRegisterRecordPageList(requestDto);
            List<OutworkRegisterRecordVo> voList = ObjectConverter.convertList(pageResult.getItems(), OutworkRegisterRecordVo.class);
            return ResponseWrap.wrapResult(new AttendancePageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
        } catch (Exception e) {
            log.error("RegisterRecordController.getOutworkRegisterRecords has exception {}", e.getMessage(), e);
            return Result.fail();
        }
    }

    @ApiOperation(value = "批量删除外勤打卡记录")
    @PostMapping(value = "/deleteOutworkRecords")
    public Result<Boolean> deleteOutworkRecords(@RequestBody ItemsResult<Integer> dto) {
        try {
            registerRecordService.deleteOutworkRecords(dto.getItems());
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("RegisterRecordController.deleteOutworkRecords has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "删除打卡记录校验")
    @PostMapping(value = "/checkIfUsedBeforeDeleteClockInRecord")
    public Result checkIfUsedBeforeDeleteClockInRecord(@RequestBody ItemsResult<Integer> dto) throws Exception {
        Map map = new HashMap();
        map.put("failCount", registerRecordService.getWaAnalyzeUseRegisterRecordCount(dto.getItems()));
        return ResponseWrap.wrapResult(map);
    }

    @ApiOperation(value = "批量删除打卡记录")
    @PostMapping(value = "/deleteClockInRecord")
    @LogRecordAnnotation(success = "批量删除了{{#num}}数据", category = "批量删除", menu = "打卡记录-日常打卡")
    public Result<Boolean> deleteClockInRecord(@RequestBody ItemsResult<Integer> dto) {
        try {
            registerRecordService.deleteRegisterRecordByIds(dto.getItems());
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("RegisterRecordController.deleteRegisterRecords has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "批量修改打卡记录打卡地点状态")
    @PostMapping(value = "/updateClockSiteStatus")
    @LogRecordAnnotation(success = "批量编辑状态为{{#content}}", category = "批量编辑状态", menu = "打卡记录-日常打卡")
    public Result<Boolean> updateClockSiteStatus(@RequestBody ClockSiteStatusDto dto) {
        try {
            registerRecordService.updateClockSiteStatus(dto.getRecordIds(), dto.getClockSiteStatus());
            LogRecordContext.putVariable("content", dto.getClockSiteStatus() == 0 ? "无效" : "有效");

            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            if (e instanceof CDException) {
                return ResponseWrap.wrapResult(AttendanceCodes.TIMEOUT_NOT_ALLOWED, Boolean.FALSE);
            }
            log.error("RegisterRecordController.updateClockSiteStatus has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "调整打卡数据")
    @PostMapping(value = "/adjustClockIn")
    @LogRecordAnnotation(success = "{{#operate}}了{empName{#empId}}打卡数据", category = "{{#operate}}", menu = "打卡记录-日常打卡")
    public Result<Boolean> adjustClockIn(@RequestBody ClockInAdjustDto dto) {
        try {
            registerRecordService.adjustClockIn(dto);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            if (e instanceof CDException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            log.error("RegisterRecordController.adjustClockIn has exception {}", e.getMessage(), e);
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_201935", WebUtil.getRequest()));
        }
    }

    @ApiOperation(value = "打卡分析")
    @PostMapping("/analyse")
    public Result<Boolean> analyseRegisterRecord(@RequestBody ClockAnalyseDto dto) {
        clockSignService.analyseRegisterRecord(dto.getBelongOrgId(), dto.getEmpIds(), dto.getDate(), dto.getType());
        return Result.ok(true);
    }

    @ApiOperation(value = "批量打卡分析")
    @PostMapping("/analyseByDateRange")
    public Result<BatchClockAnalyseResultDto> analyseByDateRange(@RequestBody BatchClockAnalyseDto dto) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        dto.doInit(userInfo);
        dto.setMultinode(Boolean.TRUE);
        return Result.ok(clockSignService.analyseByDateRange(dto));
    }

    @ApiOperation("批量打卡分析（异步）")
    @PostMapping("/asyncAnalyseByDateRange")
    public Result<Boolean> asyncAnalyseByDateRange(@RequestBody BatchClockAnalyseDto dto) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        dto.doInit(userInfo);
        String progress = dto.getProgress();

        // 参数校验
        Result<Boolean> validationResult = BatchClockAnalyseDto.validateBatchAnalyseParams(dto, progress);
        if (!validationResult.isSuccess()) {
            return validationResult;
        }

        // 分布式锁
        String lockKey = ASYNC_ANALYSE_LOCK_PREFIX + userInfo.getTenantId() + "_" + userInfo.getUserId();
        if (cacheService.containsKey(lockKey)) {
            log.warn("User already has a batch analysis task running, tenantId: {}, userId: {}, progress: {}",
                    userInfo.getTenantId(), userInfo.getUserId(), progress);
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_202843", WebUtil.getRequest()));

        }

        // 限流控制
        int empCount = dto.getEmpIds() != null ? dto.getEmpIds().size() : 0;
        boolean isSmallBatch = (dto.getEmpIds() != null && empCount < 50);
        int maxConcurrentUsers = isSmallBatch ? 50 : 10;
        String rateLimitKey = isSmallBatch ? "ASYNC_ANALYSE_RATE_LIMIT_SMALL" : "ASYNC_ANALYSE_RATE_LIMIT_LARGE";

        Long currentCount = cacheService.increment(rateLimitKey);
        if (currentCount == null) {
            currentCount = 1L;
            cacheService.cacheValue(rateLimitKey, "1", 300);
        }

        if (currentCount > maxConcurrentUsers) {
            cacheService.decrement(rateLimitKey);
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_202844", WebUtil.getRequest()));
        }

        // 设置锁定标识
        int lockTime = 180;
        cacheService.cacheValue(lockKey, "1", lockTime);

        log.info("Start batch check-in analysis task, tenantId: {}, userId: {}, progress: {}, dateRange: {}-{}, empCount: {}",
                userInfo.getTenantId(), userInfo.getUserId(), progress, dto.getStartDate(), dto.getEndDate(),
                dto.getEmpIds() != null ? dto.getEmpIds().size() : "all");

        // 初始化进度
        String progressKey = ClockAnalyseProgressUpd.ASYNC_ANALYSE_PROCESS + progress;
        cacheService.cacheValue(progressKey, String.valueOf(0.0), ClockAnalyseProgressUpd.CACHE_EXPIRE_SECONDS);

        // 分析结果Key
        String msgKey = ASYNC_ANALYSE_MSG_PROCESS + progress;
        try {
            final Long startTime = System.currentTimeMillis();
            Locale locale = ResponseWrap.getLocale();

            asyncService.execCallback(new AsynExecListener() {
                @Override
                public Map execute() {
                    AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
                    engineMessage.setCount(dto.getEmpIds() != null ? dto.getEmpIds().size() : 0);

                    long executionStartTime = System.currentTimeMillis();
                    try {
                        UserContext.doInitSecurityUserInfo(userInfo.getTenantId(),
                                null != userInfo.getUserId() ? userInfo.getUserId().toString() : null,
                                null != userInfo.getStaffId() ? userInfo.getStaffId().toString() : null,
                                null, null, null);
                        ResponseWrap.setThreadLocale(locale);

                        // 更新进度
                        cacheService.cacheValue(progressKey, "0.1", ClockAnalyseProgressUpd.CACHE_EXPIRE_SECONDS);

                        // 开始分析
                        log.info("Execute batch check-in analysis business logic, tenantId: {}, progress: {}", userInfo.getTenantId(), progress);

                        dto.setMultinode(Boolean.TRUE);
                        clockSignService.analyseByDateRange(dto);

                        log.info("Batch check-in analysis completed successfully, tenantId: {}, progress: {}", userInfo.getTenantId(), progress);
                    } catch (Exception e) {
                        log.error("Batch check-in analysis failed, tenantId: {}, progress: {}, error: {}", userInfo.getTenantId(), progress, e.getMessage(), e);
                        engineMessage.setCode(ErrorCodes.UNKNOW_ERROR);
                        if (e instanceof CDException || e instanceof ServerException) {
                            engineMessage.setMessage(e.getMessage());
                        } else {
                            // 分析失败
                            engineMessage.setMessage(ResponseWrap.wrapResult(202835, null).getMsg());
                        }
                    } finally {
                        UserContext.removeSecurityUserInfo();
                        ResponseWrap.clearThreadLocale();
                    }

                    engineMessage.setExecutionTime(System.currentTimeMillis() - executionStartTime);
                    Map<String, Object> params = new HashMap<>();
                    params.put("result", engineMessage);
                    return params;
                }

                @Override
                public void callback(Map params) {
                    long totalTime = System.currentTimeMillis() - startTime;
                    try {
                        log.info("Batch check-in analysis callback started, progress: {}, totalTime: {}ms", progress, totalTime);

                        // 更新进度
                        cacheService.cacheValue(progressKey, String.valueOf(1.0), ClockAnalyseProgressUpd.CACHE_EXPIRE_SECONDS);

                        // 缓存执行结果
                        AttendanceEngineMessage engineMessage = (AttendanceEngineMessage) params.get("result");
                        if (engineMessage != null) {
                            engineMessage.setProcess(1.0);
                            cacheService.cacheValue(msgKey, JsonSerializeHelper.serialize(engineMessage), 300);
                        }

                        log.info("Batch check-in analysis callback completed, progress: {}, result: {}, totalTime: {}ms",
                                progress, engineMessage != null ? engineMessage.getCode() : "null", totalTime);
                    } catch (Exception e) {
                        log.error("Batch check-in analysis callback exception, progress: {}, error: {}", progress, e.getMessage(), e);
                        // 更新进度
                        cacheService.cacheValue(progressKey, String.valueOf(1.0), ClockAnalyseProgressUpd.CACHE_EXPIRE_SECONDS);
                    } finally {
                        cacheService.remove(lockKey);
                        cacheService.decrement(rateLimitKey);
                    }
                }
            });

            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("Failed to start asynchronous batch check-in analysis, progress: {}, error: {}", progress, e.getMessage(), e);
            cacheService.remove(lockKey);
            cacheService.remove(progressKey);
            cacheService.remove(msgKey);
            cacheService.decrement(rateLimitKey);
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_202835", WebUtil.getRequest()));
        }
    }

    @ApiOperation("批量打卡分析（异步）-查询分析进度")
    @RequestMapping(value = "/getProgress", method = RequestMethod.GET)
    public Result getProgress(@RequestParam("progress") String progress) {
        if (StringUtils.isBlank(progress)) {
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_202837", WebUtil.getRequest()));
        }
        if (!progress.matches("^[a-zA-Z0-9_-]{1,50}$")) {
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_202838", WebUtil.getRequest()));
        }

        // 获取进度值
        double rate;
        String progressKey = ClockAnalyseProgressUpd.ASYNC_ANALYSE_PROCESS + progress;
        if (!cacheService.containsKey(progressKey)) {
            log.warn("Progress does not exist when querying progress, progress: {}", progress);
            rate = 1.0;
        } else {
            try {
                String rateStr = cacheService.getValue(progressKey);
                rate = StringUtils.isNotBlank(rateStr) ? Double.parseDouble(rateStr) : 0.0;
            } catch (NumberFormatException e) {
                log.error("Failed to parse progress value, progress: {}, error: {}", progress, e.getMessage());
                rate = 0.0;
            }
        }

        AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
        engineMessage.setProcess(rate);

        // 如果任务完成，获取执行结果并清理缓存
        if (rate >= 1.0) {
            String msgKey = ASYNC_ANALYSE_MSG_PROCESS + progress;
            try {
                String exeResult = cacheService.getValue(msgKey);
                if (StringUtils.isNotBlank(exeResult)) {
                    AttendanceEngineMessage cachedMessage = JsonSerializeHelper.deserialize(exeResult, AttendanceEngineMessage.class);
                    if (cachedMessage != null) {
                        engineMessage = cachedMessage;
                        engineMessage.setProcess(rate);
                    }
                }
            } catch (Exception e) {
                log.error("Failed to deserialize execution result, progress: {}, error: {}", progress, e.getMessage());
            } finally {
                cacheService.remove(progressKey);
                cacheService.remove(msgKey);
                ClockAnalyseProgressUpd.doClearCache(progress, cacheService);
                log.info("Batch check-in analysis progress query completed and cache cleared, progress: {}", progress);
            }
        }

        // 返回结果
        if (engineMessage.getCode() != null && engineMessage.getCode() != 0) {
            return Result.fail(engineMessage.getMessage());
        }
        return Result.ok(engineMessage);
    }

    @ApiOperation(value = "获取一定时间范围内相关员工打卡记录")
    @PostMapping("/getEmpPunchRecordsByTimeRange")
    public Result<Map<String, List<WaRegisterRecordDo>>> getEmpPunchRecordsByTimeRange(@RequestBody AnalyzeResultCalculateDto calculateDto) {
        Map<String, List<WaRegisterRecordDo>> empPunchRecordsByTimeRangeMap = new HashMap<>();
        calculateDto.setJob(false);
        List<WaRegisterRecordDo> allRegisterRecordList = analyzeResultCalculateService.getAllRegisterRecordListNonAttendanceAnalyze(calculateDto);
        for (WaRegisterRecordDo recordDo : allRegisterRecordList) {
            String key = recordDo.getBelongDate() + "_" + recordDo.getEmpid();
            empPunchRecordsByTimeRangeMap.computeIfAbsent(key, k -> new ArrayList<>()).add(recordDo);
        }
        return Result.ok(empPunchRecordsByTimeRangeMap);
    }
}
