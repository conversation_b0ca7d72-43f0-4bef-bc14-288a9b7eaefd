package com.caidaocloud.attendance.service.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.service.application.dto.EmpOvertimeDto;
import com.caidaocloud.attendance.service.domain.entity.WaEmpOvertimeDo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/21
 */
public interface OtRecordMapper {
    WaEmpOvertimeDo getOtDetailById(@Param("corpid") Long corpid, @Param("otId") Long otId);

    List<WaEmpOvertimeDo> getEmpOvertimeListByDayTime(@Param("empid") Long empid,
                                                     @Param("start") Long start, @Param("end") Long end);

    PageList<WaEmpOvertimeDo> getEmpOvertimeList(@Param("myPageBounds") MyPageBounds myPageBounds,
                                                 @Param("belongOrgId") String belongOrgId,
                                                 @Param("empid") Long empid,
                                                 @Param("status") Short status,
                                                 @Param("wfFuncId") Integer wfFuncId,
                                                 @Param("applyTime") Long applyTime, @Param("endApplyTime") Long endApplyTime);

    List<WaEmpOvertimeDo> queryEmpOvertimeListByBelongDate(@Param("empId") Long empId, @Param("startDate") Long startDate, @Param("endDate") Long endDate);

    @Select("<script> " +
            "SELECT weo.ot_id AS \"otId\", weo.crttime AS \"applyTime\", d.start_time AS \"startTime\", d.end_time   AS \"endTime\", d.ot_duration as \"otDuration\", " +
            "weo.date_type AS \"dateType\", " +
            "CASE weo.date_type " +
            "WHEN 1 then '工作日加班' " +
            "WHEN 2 then '休息日加班' " +
            "WHEN 3 then '法定假日加班' " +
            "WHEN 4 then '特殊休日加班' END AS \"dateTypeName\", " +
            "        weo.compensate_type AS \"compensateType\", " +
            "CASE weo.compensate_type WHEN 0 then '不补偿' WHEN 1 then '加班费' WHEN 2 then '调休' END AS \"compensateTypeName\", " +
            "weo.status, " +
            "CASE " +
            "WHEN weo.status = 0 " +
            "THEN '暂存' " +
            "WHEN weo.status = 1 " +
            "THEN '审批中' " +
            "WHEN weo.status = 2 " +
            "THEN '已通过' " +
            "WHEN weo.status = 3 " +
            "THEN '已拒绝' " +
            "WHEN weo.status = 4 " +
            "THEN '已作废' " +
            "WHEN weo.status = 5 " +
            "THEN '已退回' " +
            "WHEN weo.status = 9 " +
            "THEN '已撤销' END AS \"statusName\" ,d.overtime_type_id overtimeTypeId,d.detail_id detailId,weo.reason " +
            "FROM wa_emp_overtime weo " +
            "JOIN (SELECT min(weod.detail_id) detail_id, " +
            "                min(weod.start_time) start_time, " +
            "                max(weod.end_time) end_time, " +
            "                weod.overtime_id, " +
            "                weod.overtime_type_id, " +
            "                COALESCE(SUM(weod.time_duration), 0) AS ot_duration " +
            "                FROM attendance.wa_emp_overtime_detail weod " +
            "                GROUP BY weod.overtime_id,weod.overtime_type_id,weod.transfer_unit) d on d.overtime_id=weo.ot_id " +
            " <where>" +
            "  weo.empid = #{empid} " +
            "  <if test='ew.sqlSegment != null and ew.sqlSegment != \"\"'> " +
            "   and ${ew.sqlSegment} " +
            "  </if> " +
            " </where>" +
            "</script>")
    List<WaEmpOvertimeDo> getEmpOvertimePage(Page page, @Param("empid") Long empid, @Param(Constants.WRAPPER) QueryWrapper queryWrapper);

    int countOvertimeTypeUsed(@Param("tenantId") String tenantId, @Param("overtimeTypeId") Integer overtimeTypeId);

    List<Map> getEmpOverTimeByEmpid(Map<String, Object> params);

    List<WaEmpOvertimeDo> queryEmpOvertimes(@Param("tenantId") String tenantId,
                                            @Param("empId") Long empId,
                                            @Param("startDate") Long startDate,
                                            @Param("endDate") Long endDate,
                                            @Param("overtimeTypeId") Integer overtimeTypeId);

    WaEmpOvertimeDo queryOtRevokeDetailById(@Param("tenantId") String tenantId, @Param("id") Long id);

    @Select("<script> " +
            "SELECT wrr.id revokeId,weo.ot_id AS \"otId\", wrr.create_time AS \"applyTime\", weo.start_time AS \"startTime\", weo.end_time   AS \"endTime\", weo.ot_duration as \"otDuration\", " +
            "weo.date_type AS \"dateType\", " +
            "CASE weo.date_type " +
            "WHEN 1 then '工作日加班' " +
            "WHEN 2 then '休息日加班' " +
            "WHEN 3 then '法定假日加班' " +
            "WHEN 4 then '特殊休日加班' END AS \"dateTypeName\", " +
            "        weo.compensate_type AS \"compensateType\", " +
            "CASE weo.compensate_type WHEN 0 then '不补偿' WHEN 1 then '加班费' WHEN 2 then '调休' END AS \"compensateTypeName\", " +
            "wrr.status, " +
            "CASE " +
            "WHEN wrr.status = 0 " +
            "THEN '暂存' " +
            "WHEN wrr.status = 1 " +
            "THEN '审批中' " +
            "WHEN wrr.status = 2 " +
            "THEN '已通过' " +
            "WHEN wrr.status = 3 " +
            "THEN '已拒绝' " +
            "WHEN wrr.status = 4 " +
            "THEN '已作废' " +
            "WHEN wrr.status = 5 " +
            "THEN '已退回' " +
            "WHEN wrr.status = 9 " +
            "THEN '已撤销' END AS \"statusName\" ,weod.overtime_type_id overtimeTypeId,weod.detail_id detailId, " +
            "weo.reason, wrr.reason revokeReason " +
            "FROM wa_emp_overtime weo " +
            "JOIN wa_emp_overtime_detail weod ON weod.overtime_id=weo.ot_id " +
            "JOIN wa_workflow_revoke wrr ON wrr.entity_id=weo.ot_id " +
            " <where>" +
            "  weo.empid = #{empid} " +
            "  <if test='ew.sqlSegment != null and ew.sqlSegment != \"\"'> " +
            "   and ${ew.sqlSegment} " +
            "  </if> " +
            " </where>" +
            "</script>")
    List<WaEmpOvertimeDo> getEmpOvertimeRevokePage(Page page, @Param("empid") Long empid, @Param(Constants.WRAPPER) QueryWrapper queryWrapper);

    List<WaEmpOvertimeDo> queryEmpOtCompensatoryList(@Param("tenantId") String tenantId, @Param("quotaId") Long quotaId);

    List<EmpOvertimeDto> listOvertimeByEmps(@Param("tenantId")String tenantId,
                                            @Param("startTime")long startTime,
                                            @Param("endTime")long endTime,
                                            @Param("empIds")List<Long> empIds);
}