<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.caidaocloud</groupId>
    <artifactId>caidaocloud-attendance-service</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <modules>
        <module>attendance-core</module>
        <module>attendance-service</module>
    </modules>
    <parent>
        <groupId>com.caidaocloud</groupId>
        <version>1.0.0-SNAPSHOT</version>
        <artifactId>caidaocloud-parent</artifactId>
    </parent>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <shiro.version>1.7.0</shiro.version>
        <caidao_common_model.version>5.0.7-SNAPSHOT</caidao_common_model.version>
        <caidao_common.version>5.0.7-SNAPSHOT</caidao_common.version>
        <caidao_service.version>1.0.31-SNAPSHOT</caidao_service.version>
        <sharding-jdbc.version>2.0.3</sharding-jdbc.version>
        <caidao_report_service.version>2.1.10-SNAPSHOT</caidao_report_service.version>
        <caidao_ui_service.version>2.2.13-SNAPSHOT</caidao_ui_service.version>
        <attendance-core.version>2.0.3-SNAPSHOT</attendance-core.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <version>2.1.2.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>caidao-resource</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>caidaocloud-commons</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-xxljob</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>commons-configuration</groupId>
            <artifactId>commons-configuration</artifactId>
            <version>1.10</version>
        </dependency>
        <!--tomcat嵌入-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>jstl</artifactId>
        </dependency>
        <!--工作流-->
<!--        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-spring-boot-starter-basic</artifactId>
            <version>5.19.0.2</version>
        </dependency>-->
        <dependency>
            <groupId>commons-dbcp</groupId>
            <artifactId>commons-dbcp</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.2.5</version>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.0.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.freemarker</groupId>
                    <artifactId>freemarker</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.0</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
        </dependency>
        <!--redis-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--才到公共-->
        <dependency>
            <groupId>com.caidao1</groupId>
            <artifactId>caidaocloud_common_model</artifactId>
            <version>${caidao_common_model.version}</version>
        </dependency>
        <dependency>
            <groupId>com.caidao1</groupId>
            <artifactId>caidaocloud_common</artifactId>
            <version>${caidao_common.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>quartz</artifactId>
                    <groupId>org.quartz-scheduler</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-rest</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-rest-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>caidaocloud-import</artifactId>
            <version>2.1.8-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.caidao1</groupId>
                    <artifactId>caidaocloud_common_model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.caidao1</groupId>
                    <artifactId>caidaocloud_common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.caidaocloud</groupId>
                    <artifactId>record-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>record-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>caidaocloud-oss-service</artifactId>
            <version>2.4.3-SDK-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.qcloud</groupId>
                    <artifactId>cos_api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jettison</groupId>
                    <artifactId>jettison</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.caidao1</groupId>
            <artifactId>caidao_ui_service</artifactId>
            <version>${caidao_ui_service.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>caidao_common</artifactId>
                    <groupId>com.caidao1</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>caidao_common_model</artifactId>
                    <groupId>com.caidao1</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>caidao_workflow_core</artifactId>
                    <groupId>com.caidao1</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.caidao1</groupId>
            <artifactId>caidao_report_service</artifactId>
            <version>${caidao_report_service.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>caidao_common</artifactId>
                    <groupId>com.caidao1</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>caidao_common_model</artifactId>
                    <groupId>com.caidao1</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>caidao_common_model</artifactId>
                    <groupId>com.caidao1</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--工作流注册-->
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>workflow-sdk</artifactId>
            <version>2.1.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>message-sdk</artifactId>
            <version>1.1.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.xuxueli</groupId>
                    <artifactId>xxl-job-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.11.3</version>
        </dependency>
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.4</version>
            <classifier>jdk15</classifier>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib-nodep</artifactId>
            <version>3.2.4</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.2</version>
        </dependency>
        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
            <version>1.3.2</version>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.3</version>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.3.0</version>
        </dependency>
        <!-- 使用Shiro认证 -->
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring</artifactId>
            <version>${shiro.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-quartz</artifactId>
            <version>${shiro.version}</version>
        </dependency>
        <dependency>
            <groupId>org.crazycake</groupId>
            <artifactId>shiro-redis</artifactId>
            <version>3.2.3</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>4.1.6</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.25</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.1.9</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.aip</groupId>
            <artifactId>java-sdk</artifactId>
            <version>4.7.0</version>
        </dependency>
        <dependency>
            <groupId>io.shardingjdbc</groupId>
            <artifactId>sharding-jdbc-core</artifactId>
            <version>${sharding-jdbc.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.30</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.18</version>
        </dependency>
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.vincentrussell</groupId>
            <artifactId>sql-to-mongo-db-query-converter</artifactId>
            <version>1.6</version>
        </dependency>
        <dependency>
            <groupId>org.mongodb.morphia</groupId>
            <artifactId>morphia</artifactId>
        </dependency>-->
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.12.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.13.4</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.13.4.2</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.13.4</version>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.19</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdbc6</artifactId>
            <version>11.2.0.1.0</version>
        </dependency>
        <!-- AD 域认证-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-ldap</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.8.0</version>
        </dependency>
        <dependency>
            <groupId>com.caidao1</groupId>
            <artifactId>caidao_service_facade</artifactId>
            <version>${caidao_service.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>caidao_common</artifactId>
                    <groupId>com.caidao1</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>caidao_common_model</artifactId>
                    <groupId>com.caidao1</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.caidao1</groupId>
            <artifactId>caidao_service_entity</artifactId>
            <version>${caidao_service.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>caidao_common</artifactId>
                    <groupId>com.caidao1</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>caidao_common_model</artifactId>
                    <groupId>com.caidao1</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.caidao1</groupId>
            <artifactId>caidao_service_httpclient</artifactId>
            <version>${caidao_service.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>caidao_common</artifactId>
                    <groupId>com.caidao1</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>caidao_common_model</artifactId>
                    <groupId>com.caidao1</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.caidao1</groupId>
            <artifactId>caidao_service_config</artifactId>
            <version>${caidao_service.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>caidao_common</artifactId>
                    <groupId>com.caidao1</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>caidao_common_model</artifactId>
                    <groupId>com.caidao1</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.7.0</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.7.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-jsp-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
            <version>${kotlin.stdlib.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk7</artifactId>
            <version>${kotlin.stdlib.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
            <version>${kotlin.stdlib.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin.stdlib.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>