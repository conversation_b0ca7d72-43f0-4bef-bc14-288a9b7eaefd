package com.caidaocloud.attendance.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "wa_clock_plan")
public class WaClockPlanPo {
    private Long id;
    private Long corpId;
    private String belongOrgId;
    private String planName;
    private String clockWay;
    private Integer supplementCount;
    private Boolean isSupplement;
    private String gps;
    private String wifi;
    private String bluetooth;
    private Long creator;
    private Long createTime;
    private Long updater;
    private Long updateTime;
    /**
     * 新增字段
     */
    private String groupExp;
    private String groupNote;
    /**
     * 20210830新增字段
     */
    private Boolean allowFieldClockIn;
    private Boolean fieldClockInNote;
    private Boolean fieldClockInEnclosure;

    /**
     * 20210907新增字段 by aaron.chen
     */
    private Float timeInterval;
    /**
     * 20210923新增字段 by aaron.chen
     */
    private String description;
    /**
     * 20211209新增字段 by aaron.chen
     */
    private Boolean reasonMust;
    private Boolean clockInAllowed;
    /**
     * 20220523新增字段 by aaron.chen 补卡条数每次
     */
    private Integer supplementNumber;

    private Integer reasonWordNum;

    private Boolean enclosureRequired;
    private String i18nPlanName;

    /**
     * 补打卡限制次数规则：1 仅限制员工申请、2 员工申请和考勤申请均限制
     */
    private Integer supplementCountRule;
}