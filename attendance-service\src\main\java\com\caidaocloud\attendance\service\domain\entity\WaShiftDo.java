package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.wa.enums.ShiftTimeBelongTypeBaseEnum;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiOvertimeDto;
import com.caidaocloud.attendance.core.wa.dto.shift.OvertimeRestPeriodsDto;
import com.caidaocloud.attendance.core.wa.dto.shift.RestPeriodDto;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.core.wa.enums.ShiftBelongModuleEnum;
import com.caidaocloud.attendance.service.application.enums.shift.ShiftClockAnalysisRuleTypeEnum;
import com.caidaocloud.attendance.service.domain.repository.IWaShiftRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaShiftPo;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ShiftClockAnalysisRuleDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ShiftPageDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.multi.MultiWorkTimeDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.WebUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.util.PGobject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Data
@Service
public class WaShiftDo {
    //wa_shift_def 表对应字段
    private Integer shiftDefId;

    private Integer dateType;

    private Boolean isNight;

    private String shiftDefName;

    private String shiftDefCode;

    private Integer startTime;

    private Integer endTime;

    private Boolean isNoonRest;

    private Integer noonRestStart;

    private Integer noonRestEnd;

    private Integer restTotalTime;

    private Integer workTotalTime;

    private Integer onDutyStartTime;

    private Integer onDutyEndTime;

    private Integer offDutyStartTime;

    private Integer offDutyEndTime;

    private Integer overtimeStartTime;

    private Integer overtimeEndTime;

    private String belongOrgid;

    private Long crtuser;

    private Long crttime;

    private Boolean isDefault;

    private Boolean isHalfdayTime;

    private Integer halfdayTime;

    private Boolean isFlexibleWork;

    private Integer flexibleOnDutyStartTime;

    private Integer flexibleOnDutyEndTime;

    private Integer flexibleOffDutyStartTime;

    private Integer flexibleOffDutyEndTime;

    private Integer flexibleWorkType;

    private Object restPeriods;

    private Object overtimeRestPeriods;

    private Boolean isAdjustWorkHour;

    private Object adjustWorkHourJson;

    private Boolean isSpecial;

    private Integer specialWorkTime;

    private Boolean isApplyOvertime;

    private Object multiCheckinTimes;

    private Object multiWorkTimes;

    private String restTimeDesc;

    private Long orgid;

    private Long effectStartTime;

    private Long effectEndTime;

    private Integer flexibleWorkRule;

    private BigDecimal flexibleWorkLate;

    private BigDecimal flexibleWorkEarly;

    private String flexibleOffWorkRule;

    private Integer flexibleShiftSwitch;

    private String clockTimeLimit;

    private String midwayClockTime;

    private Integer substituteShift;

    private String i18nShiftDefName;

    private Integer startTimeBelong;

    private Integer endTimeBelong;

    private Integer noonRestStartBelong;

    private Integer noonRestEndBelong;

    private Integer onDutyStartTimeBelong;

    private Integer onDutyEndTimeBelong;

    private Integer offDutyStartTimeBelong;

    private Integer offDutyEndTimeBelong;

    private Integer overtimeStartTimeBelong;

    private Integer overtimeEndTimeBelong;

    private Integer halfdayTimeBelong;

    private Integer halfdayType;

    private String multiOvertime;

    private String belongModule;

    private Boolean temporaryShift;

    /**
     * 打卡分析规则，值为：{
     * "type": "分析类型：BY_OT 按照加班时间，BY_CLOCK_TIME 按照打卡时间，默认按照打卡时间",
     * "timeRange": "取卡时间范围，单位小时"
     * }
     */
    private String clockAnalysisRule;

    //其他业务字段
    private String id;
    private Long corpid;
    private Long empid;
    private Long workDate;
    private Integer calendarDateType;
    /**
     * 班次集合，一天排多个班时会有多条数据
     */
    private List<WaShiftDo> shiftDoList;

    public List<WaShiftDo> doGetShiftDoList() {
        if (CollectionUtils.isNotEmpty(this.shiftDoList)) {
            this.shiftDoList.sort(Comparator.comparing(WaShiftDo::doGetRealStartTime));
            return this.shiftDoList;
        }
        return Lists.newArrayList(this);
    }

    public WaShiftDo doGetFirstShiftDo() {
        Optional<WaShiftDo> shiftDoOpt = doGetShiftDoList().stream().min(Comparator.comparing(WaShiftDo::doGetRealStartTime));
        return shiftDoOpt.orElse(null);
    }

    public WaShiftDo doGetLastShiftDo() {
        Optional<WaShiftDo> shiftDoOpt = doGetShiftDoList().stream().max(Comparator.comparing(WaShiftDo::doGetRealStartTime));
        return shiftDoOpt.orElse(null);
    }

    public WaShiftDef doGetFirstShiftDef() {
        WaShiftDo shiftDo = doGetFirstShiftDo();
        if (null == shiftDo) {
            return null;
        }
        return ObjectConverter.convert(shiftDo, WaShiftDef.class);
    }

    public WaShiftDef doGetLastShiftDef() {
        WaShiftDo shiftDo = doGetLastShiftDo();
        if (null == shiftDo) {
            return null;
        }
        return ObjectConverter.convert(shiftDo, WaShiftDef.class);
    }

    public Integer doGetRealStartTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.startTimeBelong)) {
            return startTime + 1440;
        }
        return startTime;
    }

    public Integer doGetRealEndTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.endTimeBelong)) {
            return endTime + 1440;
        }
        return endTime;
    }

    public Integer doGetRealNoonRestStart() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.noonRestStartBelong)) {
            return noonRestStart + 1440;
        }
        return noonRestStart;
    }

    public Integer doGetRealNoonRestEnd() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.noonRestEndBelong)) {
            return noonRestEnd + 1440;
        }
        return noonRestEnd;
    }

    public Integer doGetRealOnDutyStartTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.onDutyStartTimeBelong)) {
            return onDutyStartTime + 1440;
        }
        return onDutyStartTime;
    }

    public Integer doGetRealOnDutyEndTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.onDutyEndTimeBelong)) {
            return onDutyEndTime + 1440;
        }
        return onDutyEndTime;
    }

    public Integer doGetRealOffDutyStartTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.offDutyStartTimeBelong)) {
            return offDutyStartTime + 1440;
        }
        return offDutyStartTime;
    }

    public Integer doGetRealOffDutyEndTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.offDutyEndTimeBelong)) {
            return offDutyEndTime + 1440;
        }
        return offDutyEndTime;
    }

    public Integer doGetRealOvertimeStartTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.overtimeStartTimeBelong)) {
            return overtimeStartTime + 1440;
        }
        return overtimeStartTime;
    }

    public Integer doGetRealOvertimeEndTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.overtimeEndTimeBelong)) {
            return overtimeEndTime + 1440;
        }
        // 兼容假勤模块的一段班设置数据
        return overtimeStartTime > overtimeEndTime
                ? overtimeEndTime + 1440
                : overtimeEndTime;
    }

    public Integer doGetRealHalfdayTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.halfdayTimeBelong)) {
            return halfdayTime + 1440;
        }
        return halfdayTime;
    }

    public boolean checkClockTimeCrossNight() {
        if (this.onDutyStartTime == null || this.offDutyEndTime == null) {
            return Boolean.FALSE;
        }
        if (this.onDutyStartTimeBelong != null && this.offDutyEndTimeBelong != null) {
            return ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(this.onDutyStartTimeBelong)
                    && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.offDutyEndTimeBelong);
        } else {
            return this.offDutyEndTime <= this.onDutyStartTime;
        }
    }

    public boolean checkOffDutyTimeCrossNight() {
        if (this.offDutyEndTime == null || this.offDutyStartTime == null) {
            return Boolean.FALSE;
        }
        if (this.offDutyEndTimeBelong != null && this.offDutyStartTimeBelong != null) {
            return ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(this.offDutyStartTimeBelong)
                    && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.offDutyEndTimeBelong);
        } else {
            return this.offDutyEndTime < this.offDutyStartTime;
        }
    }

    @Resource
    private IWaShiftRepository waShiftRepository;

    @Resource
    private ISessionService sessionService;

    public void save(WaShiftDo shift) {
        UserInfo userInfo = sessionService.getUserInfo();
        Long corpId = ConvertHelper.longConvert(userInfo.getTenantId());
        WaShiftPo waShiftPo = ObjectConverter.convert(shift, WaShiftPo.class);
        waShiftRepository.save(waShiftPo, String.valueOf(corpId));
    }

    public void saveList(List<WaShiftDo> list, String corpId) {
        List<WaShiftPo> waShiftPoList = ObjectConverter.convertList(list, WaShiftPo.class);
        waShiftRepository.saveList(waShiftPoList, corpId);
    }

    public WaShiftDo getWaShift(String belongOrgid, Long empId, Long workDate, String corpId) {
        WaShiftPo shift = waShiftRepository.selectWaShiftPoByDate(belongOrgid, empId, workDate, corpId);
        return ObjectConverter.convert(shift, WaShiftDo.class);
    }

    public AttendancePageResult<WaShiftDo> getShiftDefList(ShiftPageDto shiftPageDto) {
        return waShiftRepository.getShiftDefList(shiftPageDto);
    }

    public List<WaShiftDo> getListByIds(List<Integer> shiftDefIds) {
        return waShiftRepository.selectListByIds(shiftDefIds);
    }

    public void deleteShift(String belongOrgid, String tenantId) {
        waShiftRepository.deleteShift(belongOrgid, tenantId);
    }

    public WaShiftDo getShiftById(Integer id) {
        WaShiftPo waShiftPo = waShiftRepository.getShiftById(id);
        return ObjectConverter.convert(waShiftPo, WaShiftDo.class);
    }

    public int saveOrUpdateWaShiftDef(WaShiftDef waShiftDef) {
        return waShiftRepository.saveOrUpdateWaShiftDef(waShiftDef);
    }

    public List<WaShiftDo> getWaShiftDefList(String belongOrgId, ShiftBelongModuleEnum belongModule) {
        return waShiftRepository.getWaShiftDefList(belongOrgId, belongModule);
    }

    public boolean checkShiftDefReCode(String tenantId, String defCode, Integer shiftDefId, String belongModule) {
        return waShiftRepository.checkShiftDefReCode(tenantId, defCode, shiftDefId, belongModule);
    }

    public boolean checkShiftDefReName(String tenantId, String defName, Integer shiftDefId, String belongModule) {
        return waShiftRepository.checkShiftDefReName(tenantId, defName, shiftDefId, belongModule);
    }

    public List<WaShiftDo> getWaShiftDefList(String tenantId, List<Integer> shiftIds) {
        if (CollectionUtils.isEmpty(shiftIds)) {
            return new ArrayList<>();
        }
        return waShiftRepository.getWaShiftDefList(tenantId, shiftIds);
    }

    public static List<RestPeriodDto> doGetRestPeriodList(WaShiftDef shiftDef) {
        List<RestPeriodDto> allRestPeriodList = new ArrayList<>();
        if (shiftDef.getNoonRestStart() != null && shiftDef.getNoonRestEnd() != null) {
            RestPeriodDto restPeriodDto = new RestPeriodDto();
            restPeriodDto.setNoonRestStart(shiftDef.getNoonRestStart());
            restPeriodDto.setNoonRestEnd(shiftDef.getNoonRestEnd());
            restPeriodDto.setNoonRestStartBelong(shiftDef.getNoonRestStartBelong());
            restPeriodDto.setNoonRestEndBelong(shiftDef.getNoonRestEndBelong());
            allRestPeriodList.add(restPeriodDto);
        }
        try {
            if (shiftDef.getRestPeriods() != null) {
                PGobject pGobject = (PGobject) shiftDef.getRestPeriods();
                List<RestPeriodDto> restPeriods = new ObjectMapper().readValue(pGobject.getValue(), new TypeReference<List<RestPeriodDto>>() {
                });
                if (CollectionUtils.isNotEmpty(restPeriods)) {
                    allRestPeriodList.addAll(restPeriods);
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_90014", WebUtil.getRequest()));
        }
        return allRestPeriodList;
    }

    public static List<RestPeriodDto> doGetRestPeriodList(WaShiftDo shiftDef) {
        List<RestPeriodDto> allRestPeriodList = new ArrayList<>();
        if (shiftDef.getNoonRestStart() != null && shiftDef.getNoonRestEnd() != null) {
            RestPeriodDto restPeriodDto = new RestPeriodDto();
            restPeriodDto.setNoonRestStart(shiftDef.getNoonRestStart());
            restPeriodDto.setNoonRestEnd(shiftDef.getNoonRestEnd());
            restPeriodDto.setNoonRestStartBelong(shiftDef.getNoonRestStartBelong());
            restPeriodDto.setNoonRestEndBelong(shiftDef.getNoonRestEndBelong());
            allRestPeriodList.add(restPeriodDto);
        }
        try {
            if (shiftDef.getRestPeriods() != null) {
                PGobject pGobject = (PGobject) shiftDef.getRestPeriods();
                List<RestPeriodDto> restPeriods = new ObjectMapper().readValue(pGobject.getValue(), new TypeReference<List<RestPeriodDto>>() {
                });
                if (CollectionUtils.isNotEmpty(restPeriods)) {
                    allRestPeriodList.addAll(restPeriods);
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_90014", WebUtil.getRequest()));
        }
        return allRestPeriodList;
    }

    public static List<RestPeriodDto> doGetRestPeriodList(EmpShiftInfo shiftDef) {
        List<RestPeriodDto> allRestPeriodList = new ArrayList<>();
        if (Optional.ofNullable(shiftDef.getIsNoonRest()).orElse(false) && shiftDef.getNoonRestStart() != null && shiftDef.getNoonRestEnd() != null) {
            RestPeriodDto restPeriodDto = new RestPeriodDto();
            restPeriodDto.setNoonRestStart(shiftDef.getNoonRestStart());
            restPeriodDto.setNoonRestEnd(shiftDef.getNoonRestEnd());
            restPeriodDto.setNoonRestStartBelong(shiftDef.getNoonRestStartBelong());
            restPeriodDto.setNoonRestEndBelong(shiftDef.getNoonRestEndBelong());
            allRestPeriodList.add(restPeriodDto);
        }
        try {
            if (shiftDef.getRestPeriods() != null) {
                PGobject pGobject = (PGobject) shiftDef.getRestPeriods();
                List<RestPeriodDto> restPeriods = new ObjectMapper().readValue(pGobject.getValue(), new TypeReference<List<RestPeriodDto>>() {
                });
                if (CollectionUtils.isNotEmpty(restPeriods)) {
                    allRestPeriodList.addAll(restPeriods);
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_90014", WebUtil.getRequest()));
        }
        return allRestPeriodList;
    }

    public List<OvertimeRestPeriodsDto> getOvertimeRestPeriodList() {
        if (this.overtimeRestPeriods == null) {
            return Lists.newArrayList();
        }
        if (this.overtimeRestPeriods instanceof PGobject) {
            PGobject pGobject = (PGobject) this.overtimeRestPeriods;
            return FastjsonUtil.toArrayList(pGobject.getValue(), OvertimeRestPeriodsDto.class);
        }
        return FastjsonUtil.toArrayList(this.overtimeRestPeriods.toString(), OvertimeRestPeriodsDto.class);
    }

    public static List<OvertimeRestPeriodsDto> getOvertimeRestPeriodList(WaShiftDef shiftDef) {
        if (shiftDef.getOvertimeRestPeriods() == null) {
            return Lists.newArrayList();
        }
        if (shiftDef.getOvertimeRestPeriods() instanceof PGobject) {
            PGobject pGobject = (PGobject) shiftDef.getOvertimeRestPeriods();
            return FastjsonUtil.toArrayList(pGobject.getValue(), OvertimeRestPeriodsDto.class);
        }
        return FastjsonUtil.toArrayList(shiftDef.getOvertimeRestPeriods().toString(), OvertimeRestPeriodsDto.class);
    }

    public static List<String> getShiftMultiWorkTimePeriodList(WaShiftDo shiftDo) {
        if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDo.getDateType())) {
            return Lists.newArrayList();
        }
        Long nowDate = DateUtil.getOnlyDate();
        if (null != shiftDo.getMultiWorkTimes()) {
            PGobject pGobject = (PGobject) shiftDo.getMultiWorkTimes();
            List<MultiWorkTimeDto> multiWorkTimeDtoList = FastjsonUtil.toArrayList(pGobject.getValue(), MultiWorkTimeDto.class);
            multiWorkTimeDtoList.sort(Comparator.comparing(MultiWorkTimeDto::doGetRealStartTime));
            return multiWorkTimeDtoList.stream().map(timeIt -> {
                String start = DateUtil.convertDateTimeToStr(nowDate + (timeIt.getStartTime() * 60), "HH:mm", true);
                String end = DateUtil.convertDateTimeToStr(nowDate + (timeIt.getEndTime() * 60), "HH:mm", true);
                if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(timeIt.getStartTimeBelong())) {
                    start = ShiftTimeBelongTypeEnum.getName(timeIt.getStartTimeBelong()) + start;
                }
                if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(timeIt.getEndTimeBelong())) {
                    end = ShiftTimeBelongTypeEnum.getName(timeIt.getEndTimeBelong()) + end;
                }
                return String.format("%s-%s", start, end);
            }).collect(Collectors.toList());
        } else {
            String start = DateUtil.convertDateTimeToStr(nowDate + (shiftDo.getStartTime() * 60), "HH:mm", true);
            String end = DateUtil.convertDateTimeToStr(nowDate + (shiftDo.getEndTime() * 60), "HH:mm", true);
            if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(shiftDo.getEndTimeBelong()) ||
                    CdWaShiftUtil.checkCrossNight(shiftDo.getStartTime(), shiftDo.getEndTime(), shiftDo.getDateType())) {
                end = ShiftTimeBelongTypeEnum.getName(ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex()) + end;
            }
            return Lists.newArrayList(String.format("%s-%s", start, end));
        }
    }

    public Integer doGetWorkTotalTime() {
        List<WaShiftDo> shiftInfoList = this.doGetShiftDoList();
        if (shiftInfoList == null || shiftInfoList.isEmpty()) {
            return this.workTotalTime;
        }
        return shiftInfoList.stream().mapToInt(info -> Optional.ofNullable(info.getWorkTotalTime()).orElse(0)).sum();
    }

    public String doGetShiftIds() {
        List<WaShiftDo> shiftInfoList = this.doGetShiftDoList();
        if (shiftInfoList == null || shiftInfoList.isEmpty()) {
            return "";
        }
        return shiftInfoList.stream().map(WaShiftDo::getShiftDefId).filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining(","));
    }

    public boolean doCheckMultiShift() {
        return CollectionUtils.isNotEmpty(this.shiftDoList) && this.shiftDoList.size() > 1;
    }

    public List<MultiOvertimeDto> getMultiOvertimeList() {
        if (null != this.getMultiOvertime()) {
            List<MultiOvertimeDto> multiOvertimeDtoList = FastjsonUtil.toArrayList(this.getMultiOvertime(), MultiOvertimeDto.class);
            multiOvertimeDtoList.sort(Comparator.comparing(MultiOvertimeDto::doGetRealOvertimeStartTime));
            return multiOvertimeDtoList;
        } else if (null != this.getOvertimeStartTime() && null != this.getOvertimeEndTime()) {
            MultiOvertimeDto overtimeDto = new MultiOvertimeDto();
            overtimeDto.setOvertimeStartTime(this.getOvertimeStartTime());
            overtimeDto.setOvertimeEndTime(this.getOvertimeEndTime());
            overtimeDto.setOvertimeStartTimeBelong(Optional.ofNullable(this.getOvertimeStartTimeBelong())
                    .orElse(ShiftTimeBelongTypeEnum.TODAY.getIndex()));
            if (null != this.getOvertimeEndTimeBelong()) {
                overtimeDto.setOvertimeEndTimeBelong(this.getOvertimeEndTimeBelong());
            } else {
                overtimeDto.setOvertimeEndTimeBelong(this.getOvertimeStartTime() > this.getOvertimeEndTime()
                        ? ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex()
                        : ShiftTimeBelongTypeEnum.TODAY.getIndex());
            }
            return Lists.newArrayList(overtimeDto);
        }
        return Lists.newArrayList();
    }

    public List<WaShiftDo> getListByStTime(String tenantId, ShiftBelongModuleEnum belongModule,
                                           Boolean temporaryShift, Integer startTime, Integer endTime,
                                           Integer startTimeBelong, Integer endTimeBelong) {
        return waShiftRepository.selectListByStTime(tenantId, belongModule, temporaryShift, startTime, endTime, startTimeBelong, endTimeBelong);
    }

    /**
     * 检查班次是否开启“按照加班时间”分析打卡归属
     *
     * @param shiftDef
     * @return 取卡时间范围，单位小时
     */
    public static Integer ifEnableAnalyseClockByOt(WaShiftDef shiftDef) {
        if (null == shiftDef || DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDef.getDateType())) {
            return null;
        }
        if (StringUtils.isBlank(shiftDef.getClockAnalysisRule())) {
            return null;
        }
        ShiftClockAnalysisRuleDto clockAnalysisRuleDto = FastjsonUtil.toObject(shiftDef.getClockAnalysisRule(),
                ShiftClockAnalysisRuleDto.class);
        if (!ShiftClockAnalysisRuleTypeEnum.BY_OT.getCode().equals(clockAnalysisRuleDto.getType())) {
            return null;
        }
        return clockAnalysisRuleDto.getTimeRange();
    }
}
