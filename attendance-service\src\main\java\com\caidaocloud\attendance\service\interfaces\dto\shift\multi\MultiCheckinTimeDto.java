package com.caidaocloud.attendance.service.interfaces.dto.shift.multi;

import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 多段打卡时间段DTO
 *
 * <AUTHOR>
 * @Date 2025/1/24
 */
@Data
@ApiModel("多段打卡时间段DTO")
public class MultiCheckinTimeDto {
    @ApiModelProperty("最早上班打卡时间(单位分钟),eg:540")
    private Integer onDutyStartTime;
    @ApiModelProperty("最晚上班打卡时间(单位分钟),eg:600")
    private Integer onDutyEndTime;
    @ApiModelProperty("最早下班打卡时间(单位分钟),eg:1200")
    private Integer offDutyStartTime;
    @ApiModelProperty("最晚下班打卡时间(单位分钟),eg:1380")
    private Integer offDutyEndTime;
    @ApiModelProperty("最早上班打卡时间归属标记: 1 当日、2 次日、3 前日")
    private Integer onDutyStartTimeBelong;
    @ApiModelProperty("最晚上班打卡时间归属标记: 1 当日、2 次日")
    private Integer onDutyEndTimeBelong;
    @ApiModelProperty("最早下班打卡时间归属标记: 1 当日、2 次日")
    private Integer offDutyStartTimeBelong;
    @ApiModelProperty("最晚下班打卡时间归属标记: 1 当日、2 次日")
    private Integer offDutyEndTimeBelong;

    public Integer doGetRealOnDutyStartTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.onDutyStartTimeBelong)) {
            return onDutyStartTime + 1440;
        }
        return onDutyStartTime;
    }

    public Integer doGetRealOnDutyEndTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.onDutyEndTimeBelong)) {
            return onDutyEndTime + 1440;
        }
        return onDutyEndTime;
    }

    public Integer doGetRealOffDutyStartTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.offDutyStartTimeBelong)) {
            return offDutyStartTime + 1440;
        }
        return offDutyStartTime;
    }

    public Integer doGetRealOffDutyEndTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.offDutyEndTimeBelong)) {
            return offDutyEndTime + 1440;
        }
        return offDutyEndTime;
    }

    public void doInitOnDutyTime() {
        // TODO 前一日
        if (ShiftTimeBelongTypeEnum.PRE_DAY.getIndex().equals(this.getOnDutyStartTimeBelong())) {
            Integer onDutyStartTime = this.getOnDutyStartTime() - 1440;
            this.setOnDutyStartTime(onDutyStartTime);
            this.setOnDutyStartTimeBelong(ShiftTimeBelongTypeEnum.TODAY.getIndex());
        }
    }

    public void doInitOnDutyTimeForView() {
        int onDutyStartTime = this.getOnDutyStartTime();
        if (onDutyStartTime >= 0) {
            return;
        }
        onDutyStartTime += 1440;
        this.setOnDutyStartTime(onDutyStartTime);
        this.setOnDutyStartTimeBelong(ShiftTimeBelongTypeEnum.PRE_DAY.getIndex());
    }
}
