package com.caidaocloud.attendance.service.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.integrate.entity.dto.RecordDto;
import com.caidao1.wa.mybatis.model.WaAnalyze;
import com.caidao1.wa.mybatis.model.WaParseGroup;
import com.caidao1.wa.mybatis.model.WaRegisterRecord;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpParseGroup;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaRegisterRecordOfPortalPo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaRegisterRecordPo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/23
 */
public interface RegisterRecordMapper extends BaseMapper<WaRegisterRecordPo> {
    List<RecordDto> selectKqjRecord(Map<String, Object> paramsMap);

    PageList<Map> getRegisterPageList(MyPageBounds myPageBounds, Map<String, Object> params);

    PageList<Map> getEffectiveRegisterPageList(MyPageBounds pageBounds, Map<String, Object> params);

    PageList<Map> getEffectiveRegisterRecordPageList(MyPageBounds pageBounds, Map<String, Object> params);

    Map queryRegisterDetailById(@Param("corpId") Long corpId, @Param("registerId") Long registerId);

    Map queryDayAnalyzeDetailById(@Param("analyzeId") Integer analyzeId);

    WaParseGroup selectAttendanceRuleByEmpidAndDate(@Param("belongOrgId") String belongOrgId,
            @Param("empid") Long empid, @Param("date") Long date);

    PageList<Map> selectWaAnalyseListByWaGroup(MyPageBounds pageBounds, Map<String, Object> paramsMap);

    PageList<WaRegisterRecord> getRegisterRecordListByEmpId(MyPageBounds myPageBounds, Map<String, Object> params);

    PageList<WaRegisterRecord> getAllRegisterRecordPageList(@Param("myPageBounds") MyPageBounds myPageBounds,
            @Param("belongOrgId") String belongOrgId,
            @Param("empIds") List<Long> empIds,
            @Param("startDate") Long startDate,
            @Param("endDate") Long endDate,
            @Param("typeList") List<Integer> typeList,
            @Param("ifValid") Integer ifValid,
            @Param("clockSiteStatus") Integer clockSiteStatus,
            @Param("approvalStatusList") List<Integer> approvalStatusList);

    PageList<WaRegisterRecord> getAllRegisterRecordPageListNonAttendanceAnalyze(@Param("myPageBounds") MyPageBounds myPageBounds,
                                                                                @Param("belongOrgId") String belongOrgId,
                                                                                @Param("empIds") List<Long> empIds,
                                                                                @Param("startDate") Long startDate,
                                                                                @Param("endDate") Long endDate,
                                                                                @Param("typeList") List<Integer> typeList,
                                                                                @Param("ifValid") Integer ifValid,
                                                                                @Param("clockSiteStatus") Integer clockSiteStatus,
                                                                                @Param("approvalStatusList") List<Integer> approvalStatusList);

    List<Map> getEmpWorkTimeRecordByDay(@Param("empid") Long empid, @Param("daytime") Long daytime,
            @Param("includeOutReg") boolean includeOutReg);

    List<WaRegisterRecord> getEmpBdkRegisterList(@Param("belongOrgId") String belongOrgId,
            @Param("empIdList") List<Long> empIdList,
            @Param("startDate") Long startDate, @Param("endDate") Long endDate);

    List<WaRegisterRecord> selectEmpBdkRegisterList(@Param("belongOrgId") String belongOrgId,
            @Param("empIdList") List<Long> empIdList,
            @Param("belongDate") Long belongDate);

    List<WaRegisterRecord> getRegisterRecordList(@Param("myPageBounds") MyPageBounds myPageBounds,
            @Param("belongOrgId") String belongOrgId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("empIds") List<Long> empIds,
            @Param("types") List<Integer> types,
            @Param("clockSiteStatus") Integer clockSiteStatus);

    PageList<WaRegisterRecord> getRegisterRecordPageList(@Param("myPageBounds") MyPageBounds myPageBounds,
            @Param("belongOrgId") String belongOrgId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("empIds") List<Long> empIds,
            @Param("types") List<Integer> types,
            @Param("clockSiteStatus") Integer clockSiteStatus);

    List<Long> selectRegEmpIdList(@Param("belongOrgId") String belongOrgId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    List<EmpParseGroup> selectEmpParseGroupListByDate(@Param("belongOrgId") String belongOrgId,
            @Param("empIds") List<Long> empIds,
            @Param("date") Long date);

    List<EmpParseGroup> selectEmpParseGroupListByDateRange(@Param("belongOrgId") String belongOrgId,
            @Param("empIds") List<Long> empIds,
            @Param("startDate") Long startDate,
            @Param("endDate") Long endDate);

    void deleteByIds(@Param("belongOrgId") String belongOrgId, @Param("ids") Collection<Integer> ids);

    void updateClockSiteStatus(@Param("belongOrgId") String belongOrgId, @Param("recordIds") List<Integer> recordIds,
            @Param("clockSiteStatus") Integer clockSiteStatus);

    List<WaRegisterRecord> queryWaRegisterRecordByBdkId(@Param("tenantId") String tenantId,
            @Param("recordId") Long recordId);

    @Select("<script>" +
            "select * from (select t1.record_id, t1.crttime, t1.shift_def_id, t1.shift_def_ids, t2.shift_def_name, t2.i18n_shift_def_name, t1.register_type,t1.reg_date_time,t1.reason,"
            +
            " t1.approval_status, t1.last_approval_time, t1.empid, t1.record_id as bdk_record_id" +
            " from wa_register_record_bdk t1 " +
            " left join wa_shift_def t2 on t1.shift_def_id = t2.shift_def_id where t1.type = 6) t1 " +
            " <where>" +
            "  <if test='ew.sqlSegment != null and ew.sqlSegment != \"\"'> " +
            "   ${ew.sqlSegment} " +
            "  </if> " +
            " </where>" +
            "</script>")
    List<WaRegisterRecordOfPortalPo> getPageOfPortal(Page<WaRegisterRecordOfPortalPo> page,
            @Param(Constants.WRAPPER) QueryWrapper queryWrapper);

    PageList<Map> queryWaAnalyseList(MyPageBounds pageBounds, Map<String, Object> paramsMap);

    List<Long> queryWaAbnormalAnalyseList(@Param("tenantId") String tenantId,
            @Param("startDate") Long startDate,
            @Param("endDate") Long endDate,
            @Param("analyzeResult") Integer analyzeResult,
            @Param("waGroupIds") List<Integer> waGroupIds);

    List<WaRegisterRecord> selectClockRecords(@Param("empId") Long empId,
            @Param("belongDate") Long belongDate,
            @Param("type") Integer type,
            @Param("registerType") Integer registerType,
            @Param("tenantId") String tenantId,
            @Param("sort") String sort);

    PageList<WaAnalyze> queryAnalyseList(MyPageBounds pageBounds, Map<String, Object> paramsMap);

    /**
     * 支持多个考勤分组ID的员工信息查询
     */
    PageList<Map> searchEmpInfoListMultiGroup(MyPageBounds pageBounds, Map<String, Object> paramsMap);
}