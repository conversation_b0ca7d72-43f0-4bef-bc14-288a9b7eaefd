package com.caidaocloud.attendance.service.infrastructure.feign;

import com.caidaocloud.attendance.service.infrastructure.feign.callback.WfmFeignClientFallBack;
import com.caidaocloud.attendance.service.infrastructure.feign.wfm.*;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "${feign.rename.caidaocloud-wfm-service:caidaocloud-wfm-service}",
        fallback = WfmFeignClientFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "wfmProcessFeignClient")
public interface WfmFeignClient {
    /**
     * 查询工序列表
     *
     * @param wfmProcessQueryDto
     * @return
     */
    @PostMapping(value = "/api/wfm/process/management/v1/list")
    Result<List<WfmProcessTreeDetailDto>> getProcessTree(@RequestBody WfmProcessQueryDto wfmProcessQueryDto);

    /**
     * 查询异常工时类型
     */
    @GetMapping("/api/wfm/abnormalWorkHours/v1/getAbnormalWorkHoursTypeKeyValueList")
    Result<List<WfmKeyValuePair>> getAbnormalWorkHoursTypeKeyValueList();

    /**
     * 获取当天已开始工序
     *
     * @return
     */
    @GetMapping("/api/wfm/todayorder/v1/getProcessesStartedToday")
    Result<List<GetSpecifiedProcessDto>> getProcessesStartedToday();

    /**
     * 获取有基地权限的组织id集合
     *
     * @return
     */
    @GetMapping("/api/wfm/internal/process/management/v1/authed/products")
    Result<OrgScopeProductOrgIdsDto> getAuthedProductsOrgList(@RequestParam(value = "orgAuthCode") String orgAuthCode, @RequestParam(value = "userId") String userId);

    /**
     * 获取有权限访问的产品id.
     * 因为班组数据权限是根据工序来的，所以按产品过滤不涉及班组数据权限
     */
//    @PostMapping("/api/wfm/dataScope/v1/getAuthedProductIdsByDataScope")
//    Result<OrgScopeProductIdsDto> getAuthedProductIdsByDataScope(@RequestBody WfmOrgDataScopeEnum orgAuthCode);

    /**
     * 获取有权限访问的产品id
     *
     * @param orgAuthCode
     * @param userId
     * @return
     */
    @GetMapping("/api/wfm/dataScope/v1/getAuthedProductIds")
    Result<OrgScopeProductIdsDto> getAuthedProductIds(@RequestParam(value = "orgAuthCode") String orgAuthCode,
                                                      @RequestParam(value = "userId") String userId);

    /**
     * 获取有权限(组织权限)访问的工序id。
     */
    @PostMapping("/api/wfm/dataScope/v1/getAuthedProcessIdsByOrgScope")
    Result<OrgScopeProcessIdsDto> getAuthedProcessIdsByOrgScope(@RequestBody WfmOrgDataScopeEnum orgAuthCode);

    /**
     * 获取排班和工序状态
     */
    @PostMapping("/api/wfm/order/v1/fetchSchedulingAndProcessStatus")
    Result<SchedulingAndProcessStatusVo> fetchSchedulingAndProcessStatus(@RequestBody SchedulingAndProcessStatusDto schedulingAndProcessStatusDto);
}
