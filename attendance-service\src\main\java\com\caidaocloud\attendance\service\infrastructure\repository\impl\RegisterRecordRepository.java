package com.caidaocloud.attendance.service.infrastructure.repository.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaRegisterRecordMapper;
import com.caidao1.wa.mybatis.model.WaAnalyze;
import com.caidao1.wa.mybatis.model.WaParseGroup;
import com.caidao1.wa.mybatis.model.WaRegisterRecord;
import com.caidao1.wa.mybatis.model.WaRegisterRecordExample;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordOfPortalDo;
import com.caidaocloud.attendance.service.domain.repository.IRegisterRecordRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.RegisterRecordMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpParseGroup;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaRegisterRecordOfPortalPo;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.infrastructure.util.QueryAdapterUtil;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordRequestDto;
import com.caidaocloud.dto.FilterBean;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.github.miemiedev.mybatis.paginator.domain.Paginator;
import com.googlecode.totallylazy.Sequences;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 员工考勤打卡记录
 *
 * <AUTHOR>
 * @Date 2021/3/23
 */
@Repository
public class RegisterRecordRepository implements IRegisterRecordRepository {
    @Autowired
    private RegisterRecordMapper registerRecordMapper;
    @Resource
    private WaRegisterRecordMapper waRegisterRecordMapper;

    @Override
    public void batchSave(List<WaRegisterRecordDo> recordDoLists) {
        if (CollectionUtils.isEmpty(recordDoLists)) {
            return;
        }
        List<WaRegisterRecord> registerRecordList = ObjectConverter.convertList(recordDoLists, WaRegisterRecord.class);
        for (WaRegisterRecord registerRecord : registerRecordList) {
            waRegisterRecordMapper.insertSelective(registerRecord);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public AttendancePageResult<WaRegisterRecordDo> getRegisterPageList(RegisterRecordRequestDto requestDto) {
        PageBean pageBean = PageUtil.getPageBean(requestDto);
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", requestDto.getStartDate());
        params.put("endDate", requestDto.getEndDate());
        String filter = pageBean.getFilter();
        if (filter != null) {
            if (filter.contains("orgid")) {
                filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
            }
            if (filter.contains("stats")) {
                filter = filter.replaceAll("stats", "empStatus");
            }
            if (filter.contains("employ_type")) {
                filter = filter.replaceAll("employ_type", "empStyle");
            }
        }
        if (StringUtils.isNotBlank(pageBean.getFilter())) {
            params.put("filter", filter);
        }
        if (CollectionUtils.isNotEmpty(requestDto.getTypes())) {
            params.put("types", requestDto.getTypes());
        }
        params.put("corpId", requestDto.getCorpId());
        params.put("belongOrgId", requestDto.getBelongOrgId());
        if (StringUtils.isNotBlank(requestDto.getKeywords())) {
            params.put("keywords", requestDto.getKeywords());
        }
        if (StringUtils.isNotBlank(requestDto.getDataScope())) {
            params.put("datafilter", requestDto.getDataScope());
        }
        params.put("isAnalyze", requestDto.isAnalyze());
        if (requestDto.getIfShowAll() != null) {
            params.put("ifShowAll", requestDto.getIfShowAll());
        }
        PageList<Map> pageList;
        if (requestDto.getShowType() != null && requestDto.getShowType() == 1) {
            pageList = registerRecordMapper.getEffectiveRegisterRecordPageList(myPageBounds, params);
        } else {
            pageList = registerRecordMapper.getRegisterPageList(myPageBounds, params);
        }
        if (CollectionUtils.isNotEmpty(pageList)) {
            List<WaRegisterRecordDo> list = JSON.parseArray(JSON.toJSONString(pageList), WaRegisterRecordDo.class);
            return new AttendancePageResult<>(list, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        }
        return new AttendancePageResult<>(new ArrayList<>(), pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
    }

    @Override
    public Map getRegisterDetailById(Long corpId, Long registerId) {
        return registerRecordMapper.queryRegisterDetailById(corpId, registerId);
    }

    @Override
    public List<WaRegisterRecordDo> getEmpRegisterRecordList(Long empId, Long startDate, Long endDate, List<Integer> typeList, List<Integer> statusList) {
        WaRegisterRecordExample example = new WaRegisterRecordExample();
        WaRegisterRecordExample.Criteria criteria = example.createCriteria();
        criteria.andEmpidEqualTo(empId).andBelongDateBetween(startDate, endDate + 86399);
        if (CollectionUtils.isNotEmpty(typeList)) {
            criteria.andTypeIn(typeList);
        }

        if (CollectionUtils.isNotEmpty(statusList)) {
            criteria.andApprovalStatusIn(statusList);
        }

        List<WaRegisterRecord> recordList = waRegisterRecordMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(recordList)) {
            return ObjectConverter.convertList(recordList, WaRegisterRecordDo.class);
        }
        return new ArrayList<>();
    }

    @Override
    public WaRegisterRecordDo getWaRegisterRecordDoById(Integer recordId) {
        WaRegisterRecord waRegisterRecord = waRegisterRecordMapper.selectByPrimaryKey(recordId);
        return ObjectConverter.convert(waRegisterRecord, WaRegisterRecordDo.class);
    }

    @Override
    public int updateByPrimaryKeySelective(WaRegisterRecordDo waRegisterRecordDo) {
        WaRegisterRecord registerRecord = ObjectConverter.convert(waRegisterRecordDo, WaRegisterRecord.class);
        return waRegisterRecordMapper.updateByPrimaryKeySelective(registerRecord);
    }

    @Override
    public Map getDayAnalyzeDetailById(Integer analyzeId) {
        return registerRecordMapper.queryDayAnalyzeDetailById(analyzeId);
    }

    @Override
    public WaParseGroup selectAttendanceRuleByEmpidAndDate(String belongOrgId, Long empid, Long date) {
        return registerRecordMapper.selectAttendanceRuleByEmpidAndDate(belongOrgId, empid, date);
    }

    @Override
    public List<WaRegisterRecordDo> selectAllRecordListByBelongDate(String belongOrgId, Long empId, Long belongDate) {
        WaRegisterRecordExample recordExample = new WaRegisterRecordExample();
        recordExample.createCriteria().andBelongOrgIdEqualTo(belongOrgId).andEmpidEqualTo(empId).andBelongDateEqualTo(belongDate);
        List<WaRegisterRecord> list = waRegisterRecordMapper.selectByExample(recordExample);
        return ObjectConverter.convertList(list, WaRegisterRecordDo.class);
    }

    @Override
    public List<WaRegisterRecordDo> getAllRecordListByDateRange(String belongOrgId, List<Long> empIds, Long startDate, Long endDate) {
        WaRegisterRecordExample recordExample = new WaRegisterRecordExample();
        List<Integer> status = Collections.singletonList(1);
        recordExample.createCriteria().andClockSiteStatusIn(status).andBelongOrgIdEqualTo(belongOrgId).andEmpidIn(empIds).andBelongDateBetween(startDate, endDate);
        List<WaRegisterRecord> list = waRegisterRecordMapper.selectByExample(recordExample);
        return ObjectConverter.convertList(list, WaRegisterRecordDo.class);
    }

    @Override
    public PageList<Map> selectWaAnalyseListByWaGroup(MyPageBounds pageBounds, Map<String, Object> paramsMap) {
        return registerRecordMapper.selectWaAnalyseListByWaGroup(pageBounds, paramsMap);
    }

    @Override
    public List<WaRegisterRecordDo> selectRegListByIds(String belongOrgId, List<Integer> registerIds) {
        WaRegisterRecordExample recordExample = new WaRegisterRecordExample();
        recordExample.createCriteria().andBelongOrgIdEqualTo(belongOrgId).andRecordIdIn(registerIds);
        List<WaRegisterRecord> list = waRegisterRecordMapper.selectByExample(recordExample);
        return ObjectConverter.convertList(list, WaRegisterRecordDo.class);
    }

    @Override
    public AttendancePageResult<WaRegisterRecordDo> getRegisterPageListByEmpId(RegisterRecordRequestDto requestDto, Long empId) {
        PageBean pageBean = PageUtil.getPageBean(requestDto);
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        Map map = new HashMap();
        map.put("belongOrgId", requestDto.getBelongOrgId());
        map.put("startTime", requestDto.getStartDate());
        map.put("endTime", requestDto.getEndDate());
        map.put("empId", empId);
        map.put("queryApprovalBdk", requestDto.getQueryApprovalBdk());
        PageList<WaRegisterRecord> recordList = registerRecordMapper.getRegisterRecordListByEmpId(myPageBounds, map);
        if (CollectionUtils.isNotEmpty(recordList)) {
            List<WaRegisterRecordDo> list = JSON.parseArray(JSON.toJSONString(recordList), WaRegisterRecordDo.class);
            Paginator paginator = recordList.getPaginator();
            return new AttendancePageResult<>(list, requestDto.getPageNo(), requestDto.getPageSize(), paginator.getTotalCount());
        }
        return new AttendancePageResult<>(new ArrayList<>(), requestDto.getPageNo(), requestDto.getPageSize(), 0);
    }

    @Override
    public AttendancePageResult<WaRegisterRecordDo> getAllRegisterRecordPageList(AttendanceBasePage basePage, String belongOrgId, List<Long> empIds,
                                                                                 Long startDate, Long endDate, List<Integer> typeList, Integer ifValid, Integer clockSiteStatus, List<Integer> approvalStatusList) {
        PageBean pageBean = PageUtil.getPageBean(basePage);
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        myPageBounds.setOrders(Order.formString("record_id.desc"));
        PageList<WaRegisterRecord> pageList = registerRecordMapper.getAllRegisterRecordPageList(myPageBounds, belongOrgId, empIds, startDate, endDate, typeList, ifValid, clockSiteStatus, approvalStatusList);
        if (CollectionUtils.isEmpty(pageList)) {
            return new AttendancePageResult<>(new ArrayList<>(), basePage.getPageNo(), basePage.getPageSize(), 0);
        }
        List<WaRegisterRecordDo> list = JSON.parseArray(JSON.toJSONString(pageList), WaRegisterRecordDo.class);
        Paginator paginator = pageList.getPaginator();

        int totalCount = list.size();
        if (paginator != null) {
            totalCount = paginator.getTotalCount();
        }
        return new AttendancePageResult<>(list, basePage.getPageNo(), basePage.getPageSize(), totalCount);
    }

    @Override
    public AttendancePageResult<WaRegisterRecordDo> getAllRegisterRecordPageListNonAttendanceAnalyze(AttendanceBasePage basePage, String belongOrgId, List<Long> empIds,
                                                                                                     Long startDate, Long endDate, List<Integer> typeList, Integer ifValid, Integer clockSiteStatus, List<Integer> approvalStatusList) {
        PageBean pageBean = PageUtil.getNewPageBean(basePage);
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        myPageBounds.setOrders(Order.formString("record_id.desc"));
        PageList<WaRegisterRecord> pageList = registerRecordMapper.getAllRegisterRecordPageListNonAttendanceAnalyze(myPageBounds, belongOrgId, empIds, startDate, endDate, typeList, ifValid, clockSiteStatus, approvalStatusList);
        if (CollectionUtils.isEmpty(pageList)) {
            return new AttendancePageResult<>(new ArrayList<>(), basePage.getPageNo(), basePage.getPageSize(), 0);
        }
        List<WaRegisterRecordDo> list = JSON.parseArray(JSON.toJSONString(pageList), WaRegisterRecordDo.class);
        Paginator paginator = pageList.getPaginator();

        int totalCount = list.size();
        if (paginator != null) {
            totalCount = paginator.getTotalCount();
        }
        return new AttendancePageResult<>(list, basePage.getPageNo(), basePage.getPageSize(), totalCount);
    }

    @Override
    public List<Map> getEmpWorkTimeRecordByDay(Long empid, Long daytime, boolean includeOutReg) {
        return registerRecordMapper.getEmpWorkTimeRecordByDay(empid, daytime, includeOutReg);
    }

    @Override
    public List<WaRegisterRecordDo> getEmpBdkRegisterList(String belongOrgId, List<Long> empIdList, Long startDate, Long endDate) {
        List<WaRegisterRecord> list = registerRecordMapper.getEmpBdkRegisterList(belongOrgId, empIdList, startDate, endDate);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(list, WaRegisterRecordDo.class);
    }

    @Override
    public List<WaRegisterRecordDo> getEmpBdkRegisterList(String belongOrgId, List<Long> empIdList, Long belongDate) {
        List<WaRegisterRecord> list = registerRecordMapper.selectEmpBdkRegisterList(belongOrgId, empIdList, belongDate);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(list, WaRegisterRecordDo.class);
    }

    @Override
    public List<WaRegisterRecordDo> getRegisterRecordPageList(PageBean pageBean, String belongOrgId, Long startTime, Long endTime, List<Long> empIds, List<Integer> types, Integer clockSiteStatus) {
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        List<WaRegisterRecord> records = registerRecordMapper.getRegisterRecordList(myPageBounds, belongOrgId, startTime, endTime, empIds, types, clockSiteStatus);
        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(records, WaRegisterRecordDo.class);
    }

    @Override
    public AttendancePageResult<WaRegisterRecordDo> getPageList(AttendanceBasePage basePage, String belongOrgId, Long startTime, Long endTime, List<Long> empIds, List<Integer> types, Integer clockSiteStatus) {
        PageBean pageBean = PageUtil.getNewPageBean(basePage);
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        myPageBounds.setOrders(Order.formString("record_id.desc"));
        PageList<WaRegisterRecord> pageList = registerRecordMapper.getRegisterRecordPageList(myPageBounds, belongOrgId, startTime, endTime, empIds, types, clockSiteStatus);
        if (CollectionUtils.isEmpty(pageList)) {
            return new AttendancePageResult<>(new ArrayList<>(), basePage.getPageNo(), basePage.getPageSize(), 0);
        }
        List<WaRegisterRecordDo> list = FastjsonUtil.convertList(pageList, WaRegisterRecordDo.class);
        Paginator paginator = pageList.getPaginator();

        int totalCount = list.size();
        if (paginator != null) {
            totalCount = paginator.getTotalCount();
        }
        return new AttendancePageResult<>(list, basePage.getPageNo(), basePage.getPageSize(), totalCount);
    }

    @Override
    public List<Long> selectRegEmpIdList(String belongOrgId, Long startTime, Long endTime) {
        return registerRecordMapper.selectRegEmpIdList(belongOrgId, startTime, endTime);
    }

    @Override
    public List<EmpParseGroup> selectEmpParseGroupListByDate(String belongOrgId, List<Long> empIds, Long date) {
        return registerRecordMapper.selectEmpParseGroupListByDate(belongOrgId, empIds, date);
    }

    @Override
    public List<EmpParseGroup> selectEmpParseGroupListByDateRange(String belongOrgId, List<Long> empIds, Long startDate, Long endDate) {
        return registerRecordMapper.selectEmpParseGroupListByDateRange(belongOrgId, empIds, startDate, endDate);
    }

    @Override
    public int updateValidStateByIds(String belongOrgId, List<Integer> ids, Long userId, Integer ifValid) {
        WaRegisterRecordExample example = new WaRegisterRecordExample();
        example.createCriteria().andBelongOrgIdEqualTo(belongOrgId).andRecordIdIn(ids);
        WaRegisterRecord registerRecord = new WaRegisterRecord();
        registerRecord.setIfValid(ifValid);
        registerRecord.setUpdtime(System.currentTimeMillis() / 1000);
        registerRecord.setUpduser(userId);
        return waRegisterRecordMapper.updateByExampleSelective(registerRecord, example);
    }

    @Override
    public void deleteByIds(String belongOrgId, List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        registerRecordMapper.deleteByIds(belongOrgId, ids);
    }

    @Override
    public void updateClockSiteStatus(String belongOrgId, List<Integer> recordIds, Integer clockSiteStatus) {
        if (CollectionUtils.isEmpty(recordIds)) {
            return;
        }
        registerRecordMapper.updateClockSiteStatus(belongOrgId, recordIds, clockSiteStatus);
    }

    @Override
    public List<WaRegisterRecordDo> getWaRegisterRecordByBdkId(String tenantId, Long recordId) {
        List<WaRegisterRecord> registerRecords = registerRecordMapper.queryWaRegisterRecordByBdkId(tenantId, recordId);
        return ObjectConverter.convertList(registerRecords, WaRegisterRecordDo.class);
    }

    @Override
    public PageResult<WaRegisterRecordOfPortalDo> getPageOfPortal(QueryPageBean queryPageBean) {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        var page = new Page<WaRegisterRecordOfPortalPo>(queryPageBean.getPageNo(), queryPageBean.getPageSize());
        Long createTime = null;
        String status = null;
        if (CollectionUtils.isNotEmpty(queryPageBean.getFilterList())) {
            Iterator<FilterBean> iterator = queryPageBean.getFilterList().iterator();
            while (iterator.hasNext()) {
                FilterBean next = iterator.next();
                if ("crttime".equals(next.getProp()) && next.getValue() != null) {
                    createTime = Long.valueOf(next.getValue().toString());
                    iterator.remove();
                } else if ("status".equals(next.getProp()) && next.getValue() != null) {
                    status = next.getValue().toString();
                    iterator.remove();
                }
            }
        }
        QueryWrapper queryWrapper = QueryAdapterUtil.createQueryWrapper(queryPageBean, queryPageBean.getKeywords());
        queryWrapper.eq("empid", userInfo.getEmpId());
        if (createTime != null) {
            Calendar instance = Calendar.getInstance();
            instance.setTimeInMillis(createTime);
            instance.set(Calendar.HOUR_OF_DAY, 0);
            instance.set(Calendar.MINUTE, 0);
            instance.set(Calendar.SECOND, 0);
            instance.set(Calendar.MILLISECOND, 0);
            long startTime = instance.getTimeInMillis() / 1000;
            instance.set(Calendar.HOUR_OF_DAY, 23);
            instance.set(Calendar.MINUTE, 59);
            instance.set(Calendar.SECOND, 59);
            instance.set(Calendar.MILLISECOND, 59);
            long endTime = instance.getTimeInMillis() / 1000;
            queryWrapper.between("crttime", startTime, endTime);
        }
        if (StringUtils.isNotBlank(status)) {
            queryWrapper.in("approval_status", Arrays.stream(status.split(",")).filter(e -> StringUtils.isNotBlank(e))
                    .map(e -> Integer.valueOf(e)).collect(Collectors.toList()));
        }
        queryWrapper.orderByDesc("crttime");
        var pageResult = registerRecordMapper.getPageOfPortal(page, queryWrapper);
        return new PageResult(Sequences.sequence(pageResult).map(e -> e.toEntity()).toList(),
                (int) page.getCurrent(), (int) page.getSize(), (int) page.getTotal());
    }

    @Override
    public PageList<Map> selectWaAnalyseList(MyPageBounds pageBounds, Map<String, Object> paramsMap) {
        return registerRecordMapper.queryWaAnalyseList(pageBounds, paramsMap);
    }

    @Override
    public List<Long> selectWaAbnormalAnalyseList(String tenantId, Long startDate, Long endDate, Integer analyzeResult, List<Integer> waGroupIds) {
        return registerRecordMapper.queryWaAbnormalAnalyseList(tenantId, startDate, endDate, analyzeResult, waGroupIds);
    }

    @Override
    public AttendancePageResult<WaAnalyze> selectAnalyseList(AttendanceBasePage basePage, Map<String, Object> paramsMap) {
        PageBean pageBean = PageUtil.getPageBean(basePage);
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        myPageBounds.setOrders(Order.formString("analyze_id.desc"));
        PageList<WaAnalyze> pageList = registerRecordMapper.queryAnalyseList(myPageBounds, paramsMap);
        if (CollectionUtils.isEmpty(pageList)) {
            return new AttendancePageResult<>(new ArrayList<>(), basePage.getPageNo(), basePage.getPageSize(), 0);
        }
        Paginator paginator = pageList.getPaginator();
        int totalCount = pageList.size();
        if (paginator != null) {
            totalCount = paginator.getTotalCount();
        }
        return new AttendancePageResult<>(pageList, basePage.getPageNo(), basePage.getPageSize(), totalCount);
    }
}