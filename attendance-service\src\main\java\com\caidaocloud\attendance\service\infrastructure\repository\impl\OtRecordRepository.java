package com.caidaocloud.attendance.service.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidao1.commons.enums.LeaveStatusEnum;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaEmpOvertimeMapper;
import com.caidao1.wa.mybatis.model.WaEmpOvertime;
import com.caidao1.wa.mybatis.model.WaEmpOvertimeExample;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.dto.EmpOvertimeDto;
import com.caidaocloud.attendance.service.domain.entity.WaEmpOvertimeDo;
import com.caidaocloud.attendance.service.domain.repository.IOtRecordRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.OtRecordMapper;
import com.caidaocloud.attendance.service.infrastructure.util.QueryAdapterUtil;
import com.caidaocloud.dto.*;
import com.caidaocloud.imports.service.Infrastructure.util.PageUtil;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.github.miemiedev.mybatis.paginator.domain.Paginator;
import com.googlecode.totallylazy.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 员工加班记录
 *
 * <AUTHOR>
 * @Date 2021/3/21
 */
@Repository
public class OtRecordRepository implements IOtRecordRepository {
    @Autowired
    private OtRecordMapper otRecordMapper;
    @Autowired
    private WaEmpOvertimeMapper waEmpOvertimeMapper;
    @Autowired
    private ISessionService sessionService;

    @Override
    public WaEmpOvertimeDo getOtDetailById(Long corpid, Long otId) {
        return otRecordMapper.getOtDetailById(corpid, otId);
    }

    @Override
    public List<WaEmpOvertimeDo> getEmpOvertimeListByYmdDate(Long empid, Long startDate, Long endDate) {
        return otRecordMapper.getEmpOvertimeListByDayTime(empid, startDate, endDate);
    }

    @Override
    public PageResult<WaEmpOvertimeDo> getEmpOvertimePageList(BasePage basePage, WaEmpOvertimeDo search) {
        PageBean pageBean = PageUtil.getPageBean(basePage);
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        PageList<WaEmpOvertimeDo> overtimeList = otRecordMapper.getEmpOvertimeList(myPageBounds,
                search.getBelongOrgId(), search.getEmpid(), search.getStatus(), search.getWfFuncId(), search.getApplyTime(), search.getEndApplyTime());
        if (CollectionUtils.isNotEmpty(overtimeList)) {
            List<WaEmpOvertimeDo> list = new ArrayList<>(overtimeList);
            Paginator paginator = overtimeList.getPaginator();
            return new PageResult<>(list, basePage.getPageNo(), basePage.getPageSize(), paginator.getTotalCount());
        }
        return new PageResult<>(new ArrayList<>(), basePage.getPageNo(), basePage.getPageSize(), 0);
    }

    @Override
    public List<WaEmpOvertimeDo> getOverTimeList(Long empid, Long startDate, Long endDate) {
        WaEmpOvertimeExample example = new WaEmpOvertimeExample();
        WaEmpOvertimeExample.Criteria criteria = example.createCriteria();
        criteria.andEmpidEqualTo(empid);
        criteria.andStartTimeBetween(startDate, endDate);
        criteria.andStatusEqualTo(LeaveStatusEnum.LEAVE_STATUS_2.value);
        List<WaEmpOvertime> list = waEmpOvertimeMapper.selectByExample(example);
        return ObjectConverter.convertList(list, WaEmpOvertimeDo.class);
    }

    @Override
    public List<WaEmpOvertimeDo> getEmpOvertimeListByBelongDate(Long empId, Long startDate, Long endDate) {
        return otRecordMapper.queryEmpOvertimeListByBelongDate(empId, startDate, endDate);
    }

    @Override
    public PageResult<WaEmpOvertimeDo> getPageListOfPortal(QueryPageBean queryPageBean) {
        UserInfo userInfo = sessionService.getUserInfo();
        String overtimeValue = null;
        String status = null;
        Long createTime = null;
        if (CollectionUtils.isNotEmpty(queryPageBean.getFilterList())) {
            Iterator<FilterBean> iterator = queryPageBean.getFilterList().iterator();
            while (iterator.hasNext()) {
                FilterBean filterBean = iterator.next();
                if ("crttime".equals(filterBean.getProp()) && filterBean.getValue() != null) {
                    createTime = Long.valueOf(filterBean.getValue().toString());
                    iterator.remove();
                } else if ("overtime".equals(filterBean.getProp()) && filterBean.getValue() != null) {
                    overtimeValue = filterBean.getValue().toString();
                    iterator.remove();
                } else if ("status".equals(filterBean.getProp()) && filterBean.getValue() != null) {
                    status = filterBean.getValue().toString();
                    iterator.remove();
                }
            }
        }
        QueryWrapper queryWrapper = QueryAdapterUtil.createQueryWrapper(queryPageBean);
        if (createTime != null) {
            Calendar instance = Calendar.getInstance();
            instance.setTimeInMillis(createTime);
            instance.set(Calendar.HOUR_OF_DAY, 0);
            instance.set(Calendar.MINUTE, 0);
            instance.set(Calendar.SECOND, 0);
            instance.set(Calendar.MILLISECOND, 0);
            long startTime = instance.getTimeInMillis() / 1000;
            instance.set(Calendar.HOUR_OF_DAY, 23);
            instance.set(Calendar.MINUTE, 59);
            instance.set(Calendar.SECOND, 59);
            instance.set(Calendar.MILLISECOND, 50);
            long endTime = instance.getTimeInMillis() / 1000;
            queryWrapper.between("weo.crttime", startTime, endTime);
        }
        if (StringUtils.isNotBlank(overtimeValue)) {
            String[] split = overtimeValue.split(",");
            queryWrapper.ge("weo.start_time", Long.parseLong(split[0]) / 1000);
            if (split.length > 1) {
                queryWrapper.le("weo.end_time", Long.parseLong(split[1]) / 1000);
            }
        }
        if (StringUtils.isNotBlank(status)) {
            queryWrapper.in("weo.status", Arrays.stream(status.split(",")).filter(e -> StringUtils.isNotBlank(e))
                    .map(e -> Integer.valueOf(e)).collect(Collectors.toList()));
        }
        queryWrapper.orderByDesc("weo.crttime");
        Page page = new Page(queryPageBean.getPageNo(), queryPageBean.getPageSize());
        List<WaEmpOvertimeDo> pageResult = otRecordMapper.getEmpOvertimePage(page, userInfo.getStaffId(), queryWrapper);
        return new PageResult(pageResult, (int) page.getCurrent(), (int) page.getSize(), (int) page.getTotal());
    }

    @Override
    public Integer checkOvertimeTypeUsed(String tenantId, Integer overtimeTypeId) {
        return otRecordMapper.countOvertimeTypeUsed(tenantId, overtimeTypeId);
    }

    @Override
    public List<WaEmpOvertimeDo> getEmpOvertimes(String tenantId, Long empId, Long startDate, Long endDate, Integer overtimeTypeId) {
        return otRecordMapper.queryEmpOvertimes(tenantId, empId, startDate, endDate, overtimeTypeId);
    }

    @Override
    public WaEmpOvertimeDo getOtRevokeDetailById(String tenantId, Long id) {
        return otRecordMapper.queryOtRevokeDetailById(tenantId, id);
    }

    @Override
    public PageResult<WaEmpOvertimeDo> getRevokePageListOfPortal(QueryPageBean queryPageBean, BusinessCodeEnum workflowEnum) {
        UserInfo userInfo = sessionService.getUserInfo();
        QueryWrapper queryWrapper = getQueryWrapper(queryPageBean);
        queryWrapper.eq("wrr.module_name", workflowEnum.name());
        Page page = new Page(queryPageBean.getPageNo(), queryPageBean.getPageSize());
        List<WaEmpOvertimeDo> pageResult = otRecordMapper.getEmpOvertimeRevokePage(page, userInfo.getStaffId(), queryWrapper);
        return new PageResult(pageResult, (int) page.getCurrent(), (int) page.getSize(), (int) page.getTotal());
    }

    private QueryWrapper getQueryWrapper(QueryPageBean queryPageBean) {
        String overtimeValue = null;
        String status = null;
        Long createTime = null;
        if (CollectionUtils.isNotEmpty(queryPageBean.getFilterList())) {
            Iterator<FilterBean> iterator = queryPageBean.getFilterList().iterator();
            while (iterator.hasNext()) {
                FilterBean filterBean = iterator.next();
                if ("crttime".equals(filterBean.getProp()) && filterBean.getValue() != null) {
                    createTime = Long.valueOf(filterBean.getValue().toString());
                    iterator.remove();
                } else if ("overtime".equals(filterBean.getProp()) && filterBean.getValue() != null) {
                    overtimeValue = filterBean.getValue().toString();
                    iterator.remove();
                } else if ("status".equals(filterBean.getProp()) && filterBean.getValue() != null) {
                    status = filterBean.getValue().toString();
                    iterator.remove();
                }
            }
        }
        QueryWrapper queryWrapper = QueryAdapterUtil.createQueryWrapper(queryPageBean);
        if (createTime != null) {
            Calendar instance = Calendar.getInstance();
            instance.setTimeInMillis(createTime);
            instance.set(Calendar.HOUR_OF_DAY, 0);
            instance.set(Calendar.MINUTE, 0);
            instance.set(Calendar.SECOND, 0);
            instance.set(Calendar.MILLISECOND, 0);
            long startTime = instance.getTimeInMillis() / 1000;
            instance.set(Calendar.HOUR_OF_DAY, 23);
            instance.set(Calendar.MINUTE, 59);
            instance.set(Calendar.SECOND, 59);
            instance.set(Calendar.MILLISECOND, 50);
            long endTime = instance.getTimeInMillis() / 1000;
            queryWrapper.between("wrr.create_time", startTime, endTime);
        }
        if (StringUtils.isNotBlank(overtimeValue)) {
            String[] split = overtimeValue.split(",");
            queryWrapper.ge("weo.start_time", Long.parseLong(split[0]) / 1000);
            if (split.length > 1) {
                queryWrapper.le("weo.end_time", Long.parseLong(split[1]) / 1000);
            }
        }
        if (StringUtils.isNotBlank(status)) {
            queryWrapper.in("wrr.status", Arrays.stream(status.split(",")).filter(e -> StringUtils.isNotBlank(e))
                    .map(e -> Integer.valueOf(e)).collect(Collectors.toList()));
        }
        queryWrapper.orderByDesc("wrr.create_time");
        return queryWrapper;
    }

    @Override
    public List<WaEmpOvertimeDo> getEmpOtCompensatoryList(String tenantId, Long quotaId) {
        return otRecordMapper.queryEmpOtCompensatoryList(tenantId, quotaId);
    }

    @Override
    public List<WaEmpOvertimeDo> selectListByBatchId(Long batchId) {
        WaEmpOvertimeExample example = new WaEmpOvertimeExample();
        example.createCriteria().andBatchIdEqualTo(batchId);
        List<WaEmpOvertime> waEmpOvertimeList = waEmpOvertimeMapper.selectByExample(example);
        return ObjectConverter.convertList(waEmpOvertimeList, WaEmpOvertimeDo.class);
    }

    @Override
    public List<EmpOvertimeDto> listOvertimeByEmps(String tenantId, long startTime, long endTime, List<String> empIds) {
        if(empIds.isEmpty()){
            return Lists.list();
        }
        return otRecordMapper.listOvertimeByEmps(tenantId, startTime, endTime, empIds.stream().map(it->Long.valueOf(it)).collect(Collectors.toList()));
    }
}