package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.script.GroovyScriptEngine;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.commons.utils.MathUtil;
import com.caidao1.report.common.OpEnum;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaGroupMapper;
import com.caidao1.wa.mybatis.mapper.WaParseGroupMapper;
import com.caidao1.wa.mybatis.mapper.WaRegisterRecordMapper;
import com.caidao1.wa.mybatis.model.*;
import com.caidao1.wa.service.WaLeaveService;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.service.IEmpCompensatoryCaseApplyService;
import com.caidaocloud.attendance.service.application.service.IGroupService;
import com.caidaocloud.attendance.service.application.service.IOvertimeTransferRuleService;
import com.caidaocloud.attendance.service.application.service.user.MyCenterService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.domain.service.WaLogicConfigDomainService;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.RegisterRecordMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaRegisterRecordBdkMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WorkOvertimeMapper;
import com.caidaocloud.attendance.service.infrastructure.util.HandleMantissaUtil;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordDto;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.OvertimeTransferRuleDto;
import com.caidaocloud.attendance.service.interfaces.dto.user.MyWorkEventDto;
import com.caidaocloud.attendance.service.interfaces.vo.user.MyWorkEventVo;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.jetbrains.annotations.Nullable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.caidaocloud.attendance.service.application.enums.SummaryLabelEnum.REGISTER_NORMAL_TIME_TOTAL;
import static com.caidaocloud.attendance.service.application.enums.SummaryLabelEnum.REGISTER_TIME_TOTAL;

@Slf4j
@Service
public class WorkOvertimeService {
    @Autowired
    private WorkOvertimeMapper workOvertimeMapper;
    @Autowired
    private WaGroupMapper waGroupMapper;
    @Autowired
    private WaRegisterRecordMapper waRegisterRecordMapper;
    @Autowired
    private RegisterRecordMapper registerRecordMapper;
    @Autowired
    private WaParseGroupMapper waParseGroupMapper;
    @Autowired
    private WaLeaveTypeDo waLeaveTypeDo;
    @Autowired
    private WaTravelTypeDo waTravelTypeDo;
    @Autowired
    private OverTimeTypeDo overTimeTypeDo;
    @Resource
    private WaRegisterRecordBdkMapper registerRecordBdkMapper;
    @Resource
    private TextAspect textAspect;
    @Autowired
    private IOvertimeTransferRuleService overtimeTransferRuleService;
    @Autowired
    private GroovyScriptEngine groovyScriptEngine;
    @Autowired
    private WaLogicConfigDomainService waLogicConfigDomainService;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private IEmpCompensatoryCaseApplyService empCompensatoryCaseService;
    @Autowired
    @Lazy
    private MyCenterService myCenterService;
    @Resource
    private WaRegisterRecordDo waRegisterRecordDo;
    @Autowired
    private IGroupService groupService;
    @Autowired
    private WaLeaveService waLeaveService;

    private PageBean changeToday(PageBean pageBean) {
        List<FilterBean> list = pageBean.getFilterList();

        FilterBean fff;
        for (int i = 0; i < list.size(); ++i) {
            fff = list.get(i);
            boolean excludeField = ("crttime".equals(fff.getField()) || "last_approval_time".equals(fff.getField()))
                    && OpEnum.eq == fff.getOp();
            if (excludeField) {
                fff.setOp(OpEnum.bt);
                fff.setMax(Long.valueOf(fff.getMin()) + 86400L + "");
                list.set(i, fff);
            }
        }

        return pageBean;
    }

    private Integer getUnit(OvertimeTransferRuleDto transferRule) {
        TransferRuleEnum transferRuleEnum = TransferRuleEnum.getTransferRuleEnum(transferRule.getTransferRule());
        if (null == transferRuleEnum) {
            return 2;
        }
        Map<String, Object> map = transferRuleEnum.calTimeDuration(0f, transferRule.getTransferPeriods(),
                transferRule.getTransferTime());
        return (Integer) map.get("unit");
    }

    @Transactional(rollbackFor = Exception.class)
    public List getOvertimeList(String belongId, PageBean pageBean, Long empId, Long startDate, Long startTime,
            Long endDate, Long endTime, String dataScope, Integer[] filterStatus) {
        if (pageBean != null && pageBean.getFilterList() != null) {
            pageBean = this.changeToday(pageBean);
        }
        Map map = new HashMap();
        changeParam(pageBean, map);
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        if (filter != null && filter.contains("otType")) {
            filter = filter.replace("\"otType\"", "t.overtime_type_id");
        }
        map.put("filter", filter);
        map.put("filterStatus", filterStatus);
        if (empId != null) {
            map.put("empId", empId);
        }
        if (startDate != null && endDate != null) {
            Long startDateTime = startDate;
            Long endDateTime = endDate;
            if (startTime != null) {
                startDateTime = startDate + startTime * 60L;
            }
            if (endTime != null) {
                endDateTime = endDate + endTime * 60L;
            }
            map.put("startDateTime", startDateTime);
            map.put("endDateTime", endDateTime);
        }
        if (StringUtils.isNotBlank(pageBean.getKeywords())) {
            map.put("keywords", pageBean.getKeywords());
        }
        map.put("belongOrgId", belongId);
        map.put("datafilter", dataScope);
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(
                StringUtils.isBlank(pageBean.getOrder()) ? "start_time.desc,waid.desc" : pageBean.getOrder()));
        PageList<Map> list = workOvertimeMapper.getOvertimeList(pageBounds, map);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        String otIds = list.stream().map(obj -> obj.get("waid").toString()).collect(Collectors.joining(","));
        List<Map> tdList = workOvertimeMapper.getTimeDurationByOtIds(otIds);
        Map tdMap = timeDurationListToMap(tdList);
        List<Integer> overtimeTypeIds = list.stream().filter(o -> o.get("overtime_type_id") != null)
                .map(obj -> (Integer) obj.get("overtime_type_id")).distinct().collect(Collectors.toList());
        Map<Long, OvertimeTransferRuleDto> transferRuleMap = new HashMap<>();
        Map<Integer, String> overtimeTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(overtimeTypeIds)) {
            List<WaOvertimeType> overtimeTypes = overTimeTypeDo.getOvertimeTypeByIds(overtimeTypeIds);
            overtimeTypeMap = overtimeTypes.stream()
                    .collect(Collectors.toMap(WaOvertimeType::getOvertimeTypeId, WaOvertimeType::getTypeName));
            // 转换规则
            List<Long> transferRuleIds = overtimeTypes.stream().map(WaOvertimeType::getRuleId).filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(transferRuleIds)) {
                List<OvertimeTransferRuleDto> transferRules = overtimeTransferRuleService
                        .getOvertimeTransferRuleList(transferRuleIds);
                transferRuleMap = transferRules.stream().collect(
                        Collectors.toMap(OvertimeTransferRuleDto::getRuleId, Function.identity(), (v1, v2) -> v1));
            }
        }
        List<Map> item;
        for (Map woMap : list) {
            Integer overtimeTypeId = (Integer) woMap.get("overtime_type_id");
            Long ruleId = (Long) woMap.get("rule_id");
            if (null != overtimeTypeId) {
                String typeName = overtimeTypeMap.get(overtimeTypeId);
                if (StringUtil.isNotBlank(typeName)) {
                    woMap.put("typeName", typeName);
                }
            }
            Integer timeUint = (Integer) woMap.get("time_unit");
            if (timeUint == null) {
                timeUint = 1;
            }
            Integer status = (Integer) woMap.get("status");

            Integer duration = Integer.valueOf(Optional.ofNullable(woMap.get("duration")).orElse(0).toString());
            woMap.put("relTimeDuration", 0f);
            item = (List<Map>) tdMap.get(String.format("%s_%s", woMap.get("waid"), woMap.get("overtime_type_id")));
            Float relTimeDuration = 0f;
            if (null != item && !item.isEmpty() && !StringUtil.isEmpty(item.get(0).get("rel_time_duration"))) {
                relTimeDuration = Float.valueOf(item.get(0).get("rel_time_duration").toString());
            }
            if (timeUint == 1) {
                woMap.put("relTimeDuration", relTimeDuration);
            } else {
                BigDecimal v = (new BigDecimal(duration)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                woMap.put("duration", v.floatValue());
                BigDecimal v1 = (new BigDecimal(relTimeDuration)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                woMap.put("relTimeDuration", v1.floatValue());
            }
            woMap.put("timeUnitName", PreTimeUnitEnum.getName(timeUint));
            Integer transferUint = Optional.ofNullable((Integer) woMap.get("transfer_unit")).orElse(0);
            if (transferUint == 0) {
                if (transferRuleMap.containsKey(ruleId)) {
                    OvertimeTransferRuleDto transferRule = transferRuleMap.get(ruleId);
                    transferUint = getUnit(transferRule);
                    woMap.put("transfer_unit", transferUint);
                }
            }
            woMap.put("statusName", ApprovalStatusEnum.getName(status));
            woMap.put("status", status);
            String key = "date_type";
            String keyName = "date_type_txt";
            if (woMap.containsKey(key)) {
                woMap.put(keyName, DateTypeEnum.getName(Integer.parseInt(woMap.get(key).toString())));
            }
            if (woMap.containsKey("compensate_type")) {
                woMap.put("compensate_type_txt",
                        CompensateTypeEnum.getDescByOrdinal((Integer) woMap.get("compensate_type")));
            }
            woMap.put("businessKey", woMap.get("waidtype") + "_" + BusinessCodeEnum.OVERTIME.getCode());
        }
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public List getOvertimeList(String belongId, PageBean pageBean, Long empId, Long startDate, Long startTime,
            Long endDate, Long endTime, String dataScope) {
        return getOvertimeList(belongId, pageBean, empId, startDate, startTime, endDate, endTime, dataScope, null);
    }

    private void changeParam(PageBean pageBean, Map map) {
        List<FilterBean> list = pageBean.getFilterList();
        if (CollectionUtils.isNotEmpty(list)) {
            Iterator<FilterBean> it = list.iterator();
            while (it.hasNext()) {
                FilterBean filterBean = it.next();
                if ("overtime".equals(filterBean.getField())) {
                    if (StringUtils.isNotBlank(filterBean.getMin())) {
                        map.put("startDateTime", Long.valueOf(filterBean.getMin()));
                    }
                    if (StringUtils.isNotBlank(filterBean.getMax())) {
                        map.put("endDateTime", Long.valueOf(filterBean.getMax()));
                    }
                    it.remove();
                }
            }
        }

    }

    private Map timeDurationListToMap(List<Map> tdList) {
        if (null == tdList || tdList.isEmpty()) {
            return new HashMap();
        }

        return tdList.stream().collect(Collectors
                .groupingBy(obj -> String.format("%s_%s", obj.get("overtime_id"), obj.get("overtime_type_id"))));
    }

    /**
     * 查询员工补打卡次数
     *
     * @param paramsMap
     * @return
     */
    private Map<Long, Integer> getFillClockMap(Map<String, Object> paramsMap) {
        List<Map> fillClockList = registerRecordBdkMapper.getEmpFillClockCount(paramsMap);
        if (CollectionUtils.isEmpty(fillClockList)) {
            return new HashMap<>();
        }
        Map<Long, Integer> fillClockMap = new HashMap<>();
        fillClockList.forEach(row -> {
            if (row.get("num") != null) {
                fillClockMap.put((Long) row.get("empid"), Integer.valueOf(row.get("num").toString()));
            }
        });
        return fillClockMap;
    }

    private List<String> getStandardWaFields() {
        List<String> flist = new ArrayList<>();
        flist.add("late_time");
        flist.add("early_time");
        flist.add("work_time");
        flist.add("leave_time");
        flist.add("is_exp");
        flist.add("actual_work_time");
        flist.add("is_shift");
        flist.add("kg_work_time");
        flist.add("register_time");
        flist.add("is_kg"); // 旷工次数
        flist.add("latecount"); // 迟到次数
        flist.add("earlycount"); // 早退次数
        flist.add("bdk_count"); // 补打卡次数
        flist.add("ot2_tx_contrast");
        return flist;
    }

    /**
     * 根据考勤分组做月度统计
     *
     * @param pageBean
     * @param paramsMap
     * @return
     */
    public List searchRegMonthStatistics(PageBean pageBean, Map<String, Object> paramsMap,
            ImmutablePair<Long, Long> timePeriod, Boolean summary, UserInfo userInfo) {
        // 月度汇总字段列表
        String belongId = (String) paramsMap.get("belongid");
        List<String> flist = getStandardWaFields();
        Map fieldMap = generateColumns(paramsMap, 1, belongId);
        fieldMap.put("other", flist);
        List<String> ltField = (List<String>) fieldMap.get("lt");
        flist.addAll(ltField);
        List<String> trField = (List<String>) fieldMap.get("tr");
        flist.addAll(trField);
        List<String> otField = (List<String>) fieldMap.get("ot");
        flist.addAll(otField);
        List<String> extFiled = (List<String>) fieldMap.get("ext");
        flist.addAll(extFiled);
        Map<String, String> otNameMap = (Map<String, String>) fieldMap.get("otName");
        flist = flist.stream().distinct().collect(Collectors.toList());
        // 查询考勤分析规则
        Integer ym = (Integer) paramsMap.get("ym");
        Integer waGroupId = (Integer) paramsMap.get("waGroupId");
        WaParseGroup analyze = getEmpGroup(paramsMap, waGroupId, ym);
        // 查询员工每日考勤分析结果
        List<Map<String, Object>> empDailyWaAnalyzeList = workOvertimeMapper.searchRegMonthList(paramsMap);
        Map<Long, List<Map>> empDailyWaAnalyzeListMap = empDailyWaAnalyzeList.stream()
                .collect(Collectors.groupingBy(map -> (Long) map.get("empid")));
        // 补打卡次数查询
        Map<Long, Integer> fillClockMap = getFillClockMap(paramsMap);
        // 考勤数据汇总
        Integer isInterval = Optional.ofNullable((Integer) paramsMap.get("isInterval")).orElse(0);
        String clockResultFilter = (String) paramsMap.get("status");
        Integer statisticsType = (Integer) paramsMap.get("statisticsType");
        Map<Long, Map> empWaStatisticsMap = new HashMap<>();
        List<WaLogicConfigDo> lateEarlyTimeToKg = waLogicConfigDomainService.getListByCodes(belongId, Lists.newArrayList("LateEarlyTimeToKg"));
        val leaveDetail = waLeaveService.listLeaveDetail(timePeriod.left, timePeriod.right, empDailyWaAnalyzeListMap.entrySet().stream().map(it->it.getKey()).collect(Collectors.toList()));
        for (Map.Entry<Long, List<Map>> entry : empDailyWaAnalyzeListMap.entrySet()) {
            Long empId = entry.getKey();
            Map<String, Object> originMap = new HashMap<>();
            originMap.put("empid", empId);
            if (fillClockMap.containsKey(empId)) {
                originMap.put("bdk_count", fillClockMap.get(empId));
            }
            doWaStatistic(empId, leaveDetail, lateEarlyTimeToKg, entry.getValue(), originMap, summary, analyze, flist, isInterval, ltField, trField, otNameMap, belongId);
            convertData(originMap, flist, false, extFiled, ltField, trField);
            setDefaultValue(fieldMap, originMap);
            doHandleOtField(otField, originMap, belongId);
            convertKgTimeToKgDay(originMap);
            doSummaryTxt(empId, originMap, timePeriod, summary);
            // 根据考勤结果状态进行数据过滤
            if (filterWaResult(originMap, clockResultFilter, statisticsType)) {
                empWaStatisticsMap.put(empId, originMap);
            }
        }
        if (MapUtils.isEmpty(empWaStatisticsMap)) {
            return new PageList<>();
        }
        var empIdList = Lists.newArrayList(empWaStatisticsMap.keySet());
        // 查询员工并组合员工信息和月度考勤数据
        paramsMap.put("empIds", empIdList);
        paramsMap.put("isOnlyShowOrgName", true);
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(),
                Order.formString(pageBean.getOrder()));
        PageList<Map> empWaStatisticsPageResult = waRegisterRecordMapper.searchEmpInfoList(pageBounds, paramsMap);
        Long startDate = (Long) paramsMap.get("startDate");
        Long endDate = (Long) paramsMap.get("endDate");
        Map<Long, BigDecimal> empCompensatoryMap = handleCompensatory(empIdList, startDate, endDate);
        combineEmpAndWa(empWaStatisticsPageResult, empWaStatisticsMap, empCompensatoryMap, belongId, paramsMap, summary,
                userInfo);
        Map<Long, Map<String, String>> empLeaveMap = getEmpLeaveMap(belongId, startDate, endDate, empIdList);
        List<String> empIds = empWaStatisticsPageResult.stream().map(row -> row.get("empid").toString())
                .collect(Collectors.toList());
        Map<Long, Map<String, Object>> fullPathMap = getEmpWorkInfoMap(belongId, empIds);
        empWaStatisticsPageResult.forEach(t -> {
            t.put("bid", SnowUtil.nextId());
            Long empId = (Long) t.get("empid");
            if (empLeaveMap.containsKey(empId)) {
                t.putAll(empLeaveMap.get(empId));
            }
            if (fullPathMap.containsKey(empId)) {
                t.putAll(fullPathMap.get(empId));
            }
        });
        return empWaStatisticsPageResult;
    }

    private Map<Long, Map<String, Object>> getEmpWorkInfoMap(String tenantId, List<String> empIds) {
        Map<Long, Map<String, Object>> empFullPathMap = new HashMap<>();
        if (CollectionUtils.isEmpty(empIds)) {
            return empFullPathMap;
        }
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            List<List<String>> list = ListTool.split(empIds, 200);
            for (List<String> empIdList : list) {
                if (CollectionUtils.isEmpty(empIdList)) {
                    continue;
                }
                DataFilter dataFilter = DataFilter.eq("tenantId", tenantId).andIn("empId", empIdList)
                        .andEq("deleted", Boolean.FALSE.toString());
                var pageResult = DataQuery.identifier("entity.hr.EmpWorkInfo")
                        .specifyLanguage().decrypt().queryInvisible()
                        .filter(dataFilter, DataSimple.class);
                if (CollectionUtils.isNotEmpty(pageResult.getItems())) {
                    for (DataSimple item : pageResult.getItems()) {
                        Map<String, Object> fullPathMap = new HashMap<>();
                        NestPropertyValue props = item.getProperties();
                        Long empId = Long.valueOf(item.getBid());
                        SimplePropertyValue orgFullPathSimple = (SimplePropertyValue) props.get("org_full_path");
                        String orgFullPath = orgFullPathSimple == null ? ""
                                : Optional.ofNullable(orgFullPathSimple.getValue()).orElse("");
                        fullPathMap.put("org_full_path", orgFullPath);
                        SimplePropertyValue axOrgFullPathSimple = (SimplePropertyValue) props.get("ax_org_full_path");
                        String axOrgFullPath = axOrgFullPathSimple == null ? ""
                                : Optional.ofNullable(axOrgFullPathSimple.getValue()).orElse("");
                        fullPathMap.put("ax_org_full_path", axOrgFullPath);
                        empFullPathMap.put(empId, fullPathMap);
                    }
                }
            }
        } catch (Exception e) {
            log.error("getEmpWorkInfo err, {}", e.getMessage(), e);
        } finally {
            UserContext.removeSecurityUserInfo();
        }
        return empFullPathMap;
    }

    private Map<Long, Map<String, String>> getEmpLeaveMap(String tenantId, Long startDate, Long endDate,
            List<Long> empIds) {
        Map<String, Object> paramsMap = new HashMap<>();
        List<Integer> statusList = Lists.newArrayList();
        statusList.add(2);
        paramsMap.put("statusList", statusList);
        paramsMap.put("tenantId", tenantId);
        paramsMap.put("startDate", startDate);
        paramsMap.put("endDate", endDate);
        List<List<Long>> empIdsList = ListTool.split(empIds, 200);
        List<Map> leaveDayTimes = Lists.newArrayList();
        for (List<Long> list : empIdsList) {
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            paramsMap.put("anyEmpIds", "'{" + StringUtils.join(list, ",") + "}'");
            leaveDayTimes.addAll(waLeaveTypeDo.getEmpLeaveDayTimeByEmpId(paramsMap));
        }
        Map<Long, List<Map>> leaveDayTimeMap = leaveDayTimes.stream()
                .collect(Collectors.groupingBy(l -> (Long) l.get("empid")));
        Map<Long, Map<String, String>> empLeaveMap = new HashMap<>();
        for (Map.Entry<Long, List<Map>> entry : leaveDayTimeMap.entrySet()) {
            Map<String, String> leaveMap = new HashMap<>();
            List<Map> empLeaveDayTimes = entry.getValue();
            BigDecimal fullPaidLeaveDuration = BigDecimal.ZERO;
            BigDecimal fullPaidLeaveDurationDay = BigDecimal.ZERO;
            BigDecimal deductionLeaveDuration = BigDecimal.ZERO;
            BigDecimal deductionLeaveDurationDay = BigDecimal.ZERO;
            for (Map d : empLeaveDayTimes) {
                String timeUnit = d.get("time_unit").toString();
                String paidLeave = Optional.ofNullable(d.get("paid_leave")).orElse("false").toString();
                BigDecimal timeDuration = new BigDecimal(
                        Optional.ofNullable(d.get("time_duration")).orElse("0").toString());
                if ("1".equals(timeUnit)) {
                    if ("false".equals(paidLeave)) {
                        deductionLeaveDurationDay = deductionLeaveDurationDay.add(timeDuration);
                    } else {
                        fullPaidLeaveDurationDay = fullPaidLeaveDurationDay.add(timeDuration);
                    }
                } else {
                    timeDuration = timeDuration.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
                    if ("false".equals(paidLeave)) {
                        deductionLeaveDuration = deductionLeaveDuration.add(timeDuration);
                    } else {
                        fullPaidLeaveDuration = fullPaidLeaveDuration.add(timeDuration);
                    }
                }
            }
            leaveMap.put("full_paid_leave_duration", fullPaidLeaveDuration.toPlainString());
            leaveMap.put("full_paid_leave_duration_day", fullPaidLeaveDurationDay.toPlainString());
            leaveMap.put("deduction_leave_duration", deductionLeaveDuration.toPlainString());
            leaveMap.put("deduction_leave_duration_day", deductionLeaveDurationDay.toPlainString());
            empLeaveMap.put(entry.getKey(), leaveMap);
        }
        return empLeaveMap;
    }

    private void doSummaryTxt(Long empId, Map<String, Object> empWaStatisticsMap, ImmutablePair<Long, Long> timePeriod,
            boolean summary) {
        empWaStatisticsMap.put(REGISTER_TIME_TOTAL.getKey() + "_unit", PreTimeUnitEnum.HOUR.getIndex());
        empWaStatisticsMap.put(REGISTER_NORMAL_TIME_TOTAL.getKey() + "_unit", PreTimeUnitEnum.HOUR.getIndex());
        List<String> summaryKeys = empWaStatisticsMap.keySet().stream().filter(o1 -> o1.startsWith("summary")).collect(Collectors.toList());
        if (summary && CollectionUtils.isNotEmpty(summaryKeys)) {
            Long startDay = timePeriod.getLeft();
            Long endDay = timePeriod.getRight();
            do {
                String daySummaryKey = String.format("summary_%s", startDay);
                String summaryResultTxt = appendSummaryTxt(empWaStatisticsMap, daySummaryKey);
                empWaStatisticsMap.put(daySummaryKey, summaryResultTxt);
                startDay = com.caidaocloud.attendance.service.infrastructure.util.DateUtil.getNextDayInstant(startDay);
            } while (startDay <= endDay);
        }
    }

    private List<RegisterRecordDto> getEmpRegisterRecord(Long dayTime, Long empId, UserInfo userInfo) {
        MyWorkEventVo vo = new MyWorkEventVo();
        Integer dayValue = com.caidaocloud.attendance.service.infrastructure.util.DateUtil.getDayValue(dayTime);
        WaParseGroup parseGroup = myCenterService.calcClockType(vo, dayTime, empId, userInfo);
        MyWorkEventDto eventDto = myCenterService.getMyAttendance(dayValue, vo.getClockType(), parseGroup, empId,
                userInfo);
        return Optional.ofNullable(eventDto.getRecords()).orElse(Lists.newArrayList());
    }

    private List<RegisterRecordDto> getEmpRegisterRecord(Long dayTime, Long empId, UserInfo userInfo,
            EmpShiftInfo shiftInfo, Map originMap) {
        WaParseGroup parseGroup = myCenterService.calcClockType(new MyWorkEventVo(), dayTime, empId, userInfo);
        Integer clockType = parseGroup == null ? ParseGroupClockTypeEnum.SIGN_TWICE.getIndex()
                : Optional.ofNullable(parseGroup.getClockType()).orElse(ParseGroupClockTypeEnum.SIGN_TWICE.getIndex());
        return getRegisterList(empId, dayTime, clockType, shiftInfo, parseGroup, originMap);
    }

    private List<Map> checkBdkAndGetRegister(Long empId, Long dayTime, WaParseGroup parseGroup, Map originMap) {
        boolean includeOutReg = null != parseGroup && null != parseGroup.getFieldClockLinkShift()
                && parseGroup.getFieldClockLinkShift();
        // 外勤联动班次打卡
        List<Map> originalRecordList = waRegisterRecordDo.getEmpWorkTimeRecordByDay(empId, dayTime, includeOutReg);
        originMap.put(String.format("approved_bdk_%s", dayTime),
                originalRecordList.stream().anyMatch(r -> "6".equals(r.get("type").toString())));
        return originalRecordList;
    }

    private List<RegisterRecordDto> getRegisterList(Long empId, Long daytime, Integer clockType, EmpShiftInfo shiftInfo,
            WaParseGroup parseGroup, Map originMap) {
        List<Map> originalRecordList = checkBdkAndGetRegister(empId, daytime, parseGroup, originMap);
        if (CollectionUtils.isEmpty(originalRecordList)) {
            return new ArrayList<>();
        }
        List<Map> recordList = new ArrayList<>();
        if (ParseGroupClockTypeEnum.SIGN_ONCE.getIndex().equals(clockType)) {
            // 一次卡
            originalRecordList.sort(Comparator.comparing(o -> Long.valueOf(o.get("reg_date_time").toString())));
            // 查找有效的打卡记录
            if (shiftInfo != null && parseGroup != null && parseGroup.getClockRule() != null) {
                JSONObject json = JSONObject.parseObject(parseGroup.getClockRule());
                Integer clockRule = Integer.valueOf(json.get("clockRule").toString());
                // 计算正常取卡区间
                Integer normalRegStartTime;
                Integer normalRegEndTime;
                // 班次跨夜或者打卡区间跨夜
                boolean isKy;
                if (DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftInfo.getDateType())) {
                    // 工作日，跨夜
                    isKy = CdWaShiftUtil.checkCrossNightV2(shiftInfo, shiftInfo.getDateType())
                            || shiftInfo.getOffDutyStartTime() > shiftInfo.getOffDutyEndTime();
                    if (ParseGroupClockRuleEnum.SIGN_TIME_RANGE.getIndex().equals(clockRule)) {
                        // 在班次打卡时段内存在打卡、补卡记录
                        normalRegStartTime = shiftInfo.getOnDutyStartTime();
                        normalRegEndTime = shiftInfo.getOffDutyEndTime();
                    } else {
                        // 在班次上班时间-下班时间内存在打卡、补卡记录
                        normalRegStartTime = shiftInfo.getStartTime();
                        normalRegEndTime = shiftInfo.getEndTime();
                    }
                } else {
                    isKy = shiftInfo.getOffDutyStartTime() > shiftInfo.getOffDutyEndTime();
                    if (ParseGroupClockRuleEnum.SIGN_TIME_RANGE.getIndex().equals(clockRule)) {
                        // 在班次打卡时段内存在打卡、补卡记录
                        normalRegStartTime = shiftInfo.getOnDutyStartTime();
                        normalRegEndTime = shiftInfo.getOffDutyEndTime();
                    } else {
                        // 在班次打卡时段内存在打卡、补卡记录
                        normalRegStartTime = shiftInfo.getOnDutyStartTime();
                        normalRegEndTime = shiftInfo.getOffDutyEndTime();
                    }
                }
                Map normalRegisterRecord = new HashMap();
                for (Map registerRecord : originalRecordList) {
                    Long belongDte = (Long) registerRecord.get("belong_date");
                    Long regTime = (Long) registerRecord.get("reg_date_time");
                    boolean flag = myCenterService.isMissClock(belongDte, regTime, normalRegStartTime, normalRegEndTime,
                            isKy);
                    if (flag) {
                        normalRegisterRecord.putAll(registerRecord);
                        break;
                    }
                }
                // 以前是通过registerType=4来标记一次卡，后面不再使用此字段来标记，使用clockType来标记，regMap put registerType
                // = 4 目的时为了兼容之前逻辑，后面上限稳定后可以去掉
                normalRegisterRecord.put("register_type", ClockTypeEnum.SIGN_ONCE.getIndex());
                recordList.add(normalRegisterRecord);
            } else {
                log.info("MyCenterService.getRegisterList workDateShiftDto is null or parseGroup is null");
            }
        } else {
            // 二次卡 取最早的签到和最晚的签退
            originalRecordList.sort(Comparator.comparing(o -> Long.valueOf(o.get("reg_date_time").toString())));
            // 签到
            Map sign = originalRecordList.get(0);
            sign.put("register_type", ClockTypeEnum.SIGN_IN.getIndex());
            recordList.add(sign);
            // 签退
            if (originalRecordList.size() > 1) {
                Map signOff = originalRecordList.get(originalRecordList.size() - 1);
                signOff.put("register_type", ClockTypeEnum.SIGN_OUT.getIndex());
                recordList.add(signOff);
            }
        }
        List<RegisterRecordDto> recordDtoList = new ArrayList<>();
        recordList.forEach(row -> recordDtoList.add(myCenterService.convertRegData(row)));
        return recordDtoList;
    }

    private String appendSummaryTxt(Map<String, Object> staticsMap, String summaryDayPrefix) {
        StringBuilder builder = new StringBuilder();
        SummaryLabelEnum[] allLabels = SummaryLabelEnum.values();
        val tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        for (SummaryLabelEnum item : allLabels) {
            if(Lists.newArrayList("1", "6").contains(tenantId) && !item.equals(REGISTER_TIME_TOTAL)){
                continue;
            }
            String labelKey = item.getKey();
            if (item.isRegex()) {
                List<String> regexFields = staticsMap.keySet().stream().filter(o1 -> {
                    Pattern pattern = Pattern.compile(labelKey);
                    return pattern.matcher(o1).find();
                }).collect(Collectors.toList());
                for (String fieldKey : regexFields) {
                    Integer unit = (Integer) staticsMap.get(fieldKey.replaceAll(summaryDayPrefix, "") + "_unit");
                    String typeName = (String) staticsMap.getOrDefault(fieldKey.replaceAll(summaryDayPrefix, "") + "_name", "未知");
                    appendFieldTxt(staticsMap, builder, item, summaryDayPrefix + "_" + fieldKey, typeName, unit, false);
                }
            } else {
                final String key = summaryDayPrefix + "_" + labelKey;
                Integer unit = (Integer) staticsMap.get(labelKey + "_unit");
                appendFieldTxt(staticsMap, builder, item, key, null, unit, Lists.newArrayList("1", "6").contains(tenantId));
            }
        }
        return builder.toString().replaceAll(",+$", "");
    }

    private void appendFieldTxt(Map<String, Object> staticsMap, StringBuilder builder, SummaryLabelEnum item, String fieldKey, String name, Integer unit, boolean onlyValue) {
        String unitTxt = unit == null ? ResponseWrap.wrapResult(AttendanceCodes.MINUTE, null).getMsg() : PreTimeUnitEnum.getName(unit);
        String value = Objects.toString(staticsMap.getOrDefault(fieldKey, ""));
        if (StringUtils.isNotBlank(value)) {
            value = value.replaceAll("\\.\\d?0+$", "").replaceAll("\\.0*$", "");
            if(onlyValue){
                builder.append(value)
                        .append(",");
            }else{
                builder.append(StringUtils.isNotBlank(name) ? String.format(SummaryLabelEnum.getFormatBy(item), name, value) :
                        String.format(SummaryLabelEnum.getFormatBy(item), value))
                        .append(unitTxt).append(",");
            }

        }
        staticsMap.remove(fieldKey);
    }

    private Map<Long, BigDecimal> handleCompensatory(List<Long> empIdList, Long startDate, Long endDate) {
        if (CollectionUtils.isEmpty(empIdList)) {
            return Maps.newHashMap();
        }
        return empCompensatoryCaseService.getApprovedOfCompensatoryCase(empIdList, startDate, endDate);
    }

    /**
     * 旷工分钟数转旷工天数
     *
     * @param originMap
     */
    private void convertKgTimeToKgDay(Map originMap) {
        if (!originMap.containsKey("kg_work_time_minute") || null == originMap.get("kg_work_time_minute")) {
            return;
        }
        Float kgWorkTimeMinute = Float.valueOf(originMap.get("kg_work_time_minute").toString());
        if (kgWorkTimeMinute <= 0) {
            return;
        }
        BigDecimal kgWorkTimeDay = BigDecimal.valueOf(kgWorkTimeMinute).divide(BigDecimal.valueOf(480), 4,
                BigDecimal.ROUND_HALF_UP);
        // 向上取整0.5
        kgWorkTimeDay = HandleMantissaUtil.handleMantissa(kgWorkTimeDay, 2);
        originMap.put("kg_work_time_day", kgWorkTimeDay.floatValue());
    }

    private void doHandleOtField(List<String> otField, Map originMap, String belongId) {
        if (CollectionUtils.isEmpty(otField)) {
            return;
        }
        Boolean hideUnit = "1".equals(belongId) || "6".equals(belongId) || "11".equals(belongId);
        otField.forEach((key) -> {
            if (!com.caidao1.commons.utils.StringUtil.isNotContainsKeyOrisNullOrEmpty(originMap, key)) {
                if (!key.contains("_key_unit")) {
                    Integer unit = (Integer) originMap.get(key + "_unit");
                    if (null != unit) {
                        String unitName = PreTimeUnitEnum.getName(unit);
                        originMap.put(key + "_simple", originMap.get(key).toString());
                        String value = originMap.get(key).toString();
                        if(hideUnit){
                            originMap.put(key, value );
                        }else {
                            originMap.put(key, value + unitName);
                        }
                    }
                }
            }
        });
    }

    private boolean filterResultType(Map<String, Object> originMap, Integer statisticsType) {
        if (statisticsType != null) {
            int lateCount = originMap.get("latecount") == null ? 0 : (Integer) originMap.get("latecount");
            int earlyCount = originMap.get("earlycount") == null ? 0 : (Integer) originMap.get("earlycount");
            switch (statisticsType) {
                case 0:
                    // 休假
                    return originMap.keySet().stream()
                            .anyMatch(it -> it.startsWith("lt_") && it.endsWith("_key")
                                    && Objects.nonNull(originMap.get(it))
                                    && originMap.get(it) != null && !Objects.toString(originMap.get(it)).equals("0"));
                case 1:
                    // 出差
                    return originMap.keySet().stream()
                            .anyMatch(it -> it.startsWith("tr_") && it.endsWith("_key")
                                    && Objects.nonNull(originMap.get(it))
                                    && originMap.get(it) != null && !Objects.toString(originMap.get(it)).equals("0"));
                case 2:
                    // 加班
                    return originMap.keySet().stream().anyMatch(it -> it.startsWith("ot_") && it.endsWith("_key")
                            && originMap.get(it) != null && !Objects.toString(originMap.get(it)).equals("0"));
                case 3:
                    // 迟到
                    return lateCount > 0;
                case 4:
                    // 早退
                    return earlyCount > 0;
            }
        }
        return false;
    }

    private boolean filterWaResult(Map originMap, String clockResultFilter, Integer statisticsType) {
        int latecount = originMap.get("latecount") == null ? 0 : (Integer) originMap.get("latecount");// 迟到次数
        int earlycount = originMap.get("earlycount") == null ? 0 : (Integer) originMap.get("earlycount");// 早退次数
        int isKg = originMap.get("is_kg") == null ? 0 : (Integer) originMap.get("is_kg");// 旷工次数
        if (statisticsType != null) {
            return filterResultType(originMap, statisticsType);
        } else {
            if (String.valueOf(ClockResultEnum.NORMAL.getIndex()).equals(clockResultFilter)) {
                if (latecount == 0 && earlycount == 0 && isKg == 0) {
                    return Boolean.TRUE;
                }
            } else if (String.valueOf(ClockResultEnum.ABNORMAL.getIndex()).equals(clockResultFilter)) {
                return latecount > 0 || earlycount > 0 || isKg > 0;
            } else {
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        }
    }

    /**
     * 组合员工信息和月度考勤数据
     *
     * @param empWaStatisticsPageResult
     * @param empWaStatisticsMap
     * @param belongId
     */
    private void combineEmpAndWa(PageList<Map> empWaStatisticsPageResult, Map<Long, Map> empWaStatisticsMap,
            Map<Long, BigDecimal> empCompensatoryMap, String belongId, Map<String, Object> paramsMap,
            Boolean summary, UserInfo userInfo) {
        if (CollectionUtils.isEmpty(empWaStatisticsPageResult)) {
            return;
        }
        List<Long> selectedEmpIds = empWaStatisticsPageResult.stream().map(l -> Long.valueOf(l.get("empid").toString()))
                .distinct().collect(Collectors.toList());
        Long startDate = Long.valueOf(paramsMap.get("startDate").toString());
        Long endDate = Long.valueOf(paramsMap.get("endDate").toString());
        Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(belongId);
        Map<Long, BigDecimal> empShiftInfosMap = new HashMap<>();
        Map<Long, Long> empWorkTimeDayMap = new HashMap<>();
        Map<String, EmpShiftInfo> empShiftMap = new HashMap<>();
        Map<Long, List<AttEmpGroupDo>> empGroupMap = new HashMap<>();
        for (List<Long> splitEmpIds : ListTool.split(selectedEmpIds, 500)) {
            if (CollectionUtils.isEmpty(splitEmpIds)) {
                continue;
            }
            empShiftInfosMap.putAll(getEmpShiftWorkTimeMaps(belongId, startDate, endDate, splitEmpIds, shiftDefMap,
                    empWorkTimeDayMap, empShiftMap));
            empGroupMap.putAll(getEmpGroupMap(belongId, splitEmpIds, startDate, endDate + 86399));
        }
        Map<Object, String> employTypeMap = new HashMap<>();
        Map<Object, String> empStatusMap = new HashMap<>();
        for (Map originMap : empWaStatisticsPageResult) {
            if (originMap.containsKey("employ_type")) {
                if (employTypeMap.containsKey(originMap.get("employ_type"))) {
                    originMap.put("employ_type_name", employTypeMap.get(originMap.get("employ_type")));
                } else {
                    if (originMap.get("employ_type") != null) {
                        String employTypeName = textAspect.getDictCache(originMap.get("employ_type").toString(),
                                ResponseWrap.getLocale());
                        employTypeMap.put(originMap.get("employ_type"), employTypeName);
                        originMap.put("employ_type_name", employTypeName);
                    }
                }
            }
            if (originMap.containsKey("emp_status")) {
                if (empStatusMap.containsKey(originMap.get("emp_status"))) {
                    originMap.put("emp_status_name", empStatusMap.get(originMap.get("emp_status")));
                } else {
                    if (null != originMap.get("emp_status")) {
                        String empStatusName = textAspect.getEmpStatusEnumText(originMap.get("emp_status").toString(),
                                belongId);
                        originMap.put("emp_status_name", empStatusName);
                        empStatusMap.put(originMap.get("emp_status"), empStatusName);
                    }
                }
            }
            Long empId = (Long) originMap.get("empid");
            if (empWaStatisticsMap.containsKey(empId) && null != empWaStatisticsMap.get(empId)) {
                Long hireDate = (Long) originMap.get("hire_date");
                Map empResultMap = empWaStatisticsMap.get(empId);
                if (summary != null && summary) {
                    List<Object> daySummaryList = (List<Object>) empResultMap.keySet().stream()
                            .filter(it -> Objects.toString(it).startsWith("summary_")).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(daySummaryList)) {
                        for (Object it : daySummaryList) {
                            try {
                                String[] arr = Objects.toString(it).split("_");
                                String dayLabel = arr[1];
                                EmpShiftInfo shiftInfo = empShiftMap.get(String.format("%s_%s", empId, dayLabel));
                                if (empResultMap.get(it) != null
                                        && StringUtils.isNotBlank(Objects.toString(empResultMap.get(it)))) {
                                    empResultMap.put(String.format("record_%s", dayLabel), getEmpRegisterRecord(
                                            Long.valueOf(dayLabel), empId, userInfo, shiftInfo, originMap));
                                } else if (hireDate != null && hireDate > Long.parseLong(dayLabel)) {
                                    empResultMap.put(it, "-");
                                } else {
                                    WaParseGroup parseGroup = myCenterService.calcClockType(new MyWorkEventVo(),
                                            Long.valueOf(dayLabel), empId, userInfo);
                                    checkBdkAndGetRegister(empId, Long.valueOf(dayLabel), parseGroup, originMap);
                                }
                            } catch (NumberFormatException e) {
                                log.error("parse day label error, text:{}, error:{}", it, e.getMessage(), e);
                            }
                        }
                    }
                }
                if (empGroupMap.containsKey(empId)) {
                    List<AttEmpGroupDo> empGroups = empGroupMap.get(empId);
                    StringBuilder groupBuilder = new StringBuilder();
                    for (AttEmpGroupDo empGroup : empGroups) {
                        String groupName = LangParseUtil.getI18nLanguage(empGroup.getI18nWaGroupName(),
                                empGroup.getWaGroupName());
                        groupBuilder.append(groupName).append(":");
                        if (startDate >= empGroup.getStartTime() && startDate < empGroup.getEndTime()) {
                            groupBuilder.append(DateUtil.getDateStrByTimesamp(startDate)).append("~")
                                    .append(DateUtil.getDateStrByTimesamp(empGroup.getEndTime())).append("\n");
                        } else if (empGroup.getStartTime() <= endDate && endDate < empGroup.getEndTime()) {
                            groupBuilder.append(DateUtil.getDateStrByTimesamp(empGroup.getStartTime())).append("~")
                                    .append(DateUtil.getDateStrByTimesamp(endDate));
                        } else {
                            groupBuilder.append(DateUtil.getDateStrByTimesamp(empGroup.getStartTime())).append("~")
                                    .append(DateUtil.getDateStrByTimesamp(empGroup.getEndTime())).append("\n");
                        }
                    }
                    originMap.put("wa_group_name", groupBuilder.toString());
                }
                originMap.putAll(empResultMap);
            }
            int latecount = originMap.get("latecount") == null ? 0 : (Integer) originMap.get("latecount");// 迟到次数
            int earlycount = originMap.get("earlycount") == null ? 0 : (Integer) originMap.get("earlycount");// 早退次数
            int isKg = originMap.get("is_kg") == null ? 0 : (Integer) originMap.get("is_kg");// 旷工次数
            if (latecount > 0 || earlycount > 0 || isKg > 0) {
                originMap.put("status", ClockResultEnum.ABNORMAL.getIndex());
            } else {
                originMap.put("status", ClockResultEnum.NORMAL.getIndex());
            }
            if (empCompensatoryMap.containsKey(empId)) {
                originMap.put("compensatory", empCompensatoryMap.get(empId).doubleValue());
            } else {
                originMap.put("compensatory", 0);
            }
            EmpInfoDTO empInfoDTO = FastjsonUtil.convertObject(originMap, EmpInfoDTO.class);
            empInfoDTO.setName((String) originMap.get("emp_name"));
            originMap.put("empInfo", empInfoDTO);
            if (originMap.containsKey("work_time")) {
                originMap.put("work_time", empShiftInfosMap.get(empId));
                originMap.put("work_time_day", empWorkTimeDayMap.get(empId));
            }
            BigDecimal travelDuration = BigDecimal.ZERO;
            BigDecimal travelDurationDay = BigDecimal.ZERO;
            BigDecimal leaveDuration = BigDecimal.ZERO;
            BigDecimal leaveDurationDay = BigDecimal.ZERO;
            for (Object o : originMap.keySet()) {
                String key = o.toString();
                if (key.contains("tr_") && key.contains("_key_unit")) {
                    String[] arr = key.split("_");
                    Integer unit = (Integer) originMap.get(key);
                    BigDecimal duration = new BigDecimal(originMap.get(String.format("tr_%s_key", arr[1])).toString());
                    if (2 == unit) {
                        travelDuration = travelDuration.add(duration);
                    } else {
                        travelDurationDay = travelDurationDay.add(duration);
                    }
                }
                if (key.contains("lt_") && key.contains("_key_unit")) {
                    String[] arr = key.split("_");
                    Integer unit = (Integer) originMap.get(key);
                    BigDecimal duration = new BigDecimal(originMap.get(String.format("lt_%s_key", arr[1])).toString());
                    if (2 == unit) {
                        leaveDuration = leaveDuration.add(duration);
                    } else {
                        leaveDurationDay = leaveDurationDay.add(duration);
                    }
                }
                if (key.contains("approved_bdk_") && (Boolean) originMap.get(key)) {
                    String[] arr = key.split("_");
                    String summaryKey = String.format("summary_%s", arr[2]);
                    Object summaryValue = originMap.get(summaryKey);
                    val tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
                    if(!Lists.newArrayList("1", "6").contains(tenantId)){
                        if (StringUtil.isNotEmpty(summaryValue)) {
                        originMap.put(summaryKey, String.format("%s,%s", summaryValue,
                                CalendarStatusEnum.getName(CalendarStatusEnum.PATCH.getIndex())));
                        } else {
                            originMap.put(summaryKey, "approved_bdk");
                        }
                    }
                }
            }
            originMap.put("leave_duration", leaveDuration.toPlainString());
            originMap.put("leave_duration_day", leaveDurationDay.toPlainString());
            originMap.put("travel_duration", travelDuration.toPlainString());
            originMap.put("travel_duration_day", travelDurationDay.toPlainString());
        }
    }

    public Map<Long, List<AttEmpGroupDo>> getEmpGroupMap(String tenantId, List<Long> empIds, Long startDate,
            Long endDate) {
        if (CollectionUtils.isEmpty(empIds)) {
            return new HashMap<>();
        }
        List<AttEmpGroupDo> empGroups = groupService.getEmpGroupIdByEmpIds(tenantId, empIds, startDate, endDate);
        return empGroups.stream().collect(Collectors.groupingBy(AttEmpGroupDo::getEmpId));
    }

    public Map<Long, BigDecimal> getEmpShiftWorkTimeMaps(String belongId, Long startDate, Long endDate, List<Long> empIdList,
            Map<Integer, WaShiftDef> corpShiftDefMap, Map<Long, Long> empWorkTimeDayMap,
            Map<String, EmpShiftInfo> empShiftMap) {
        Map<String, Object> shiftMap = new HashMap<>();
        shiftMap.put("belongid", belongId);
        shiftMap.put("startDate", startDate);
        shiftMap.put("endDate", endDate);
        shiftMap.put("anyEmpids", "'{" + StringUtils.join(empIdList, ",").concat("}'"));
        waCommonService.getEmpShiftInfoListMaps(shiftMap, empShiftMap, empIdList, corpShiftDefMap);
        List<EmpShiftInfo> empShiftInfos = new ArrayList<>(empShiftMap.values());
        List<EmpShiftInfo> filters = empShiftInfos.stream().filter(shift -> shift.getWorkDate() >= startDate && shift.getWorkDate() <= endDate).filter(shift -> {
            Long realHireDate = Optional.ofNullable(shift.getInternshipDate()).orElse(shift.getHireDate());
            if (realHireDate != null && shift.getTerminationDate() != null) {
                return shift.getDateType() == 1 && realHireDate <= shift.getWorkDate()
                        && shift.getTerminationDate() >= shift.getWorkDate();
            } else if (realHireDate != null) {
                return shift.getDateType() == 1 && realHireDate <= shift.getWorkDate();
            } else if (shift.getTerminationDate() != null) {
                return shift.getDateType() == 1 && shift.getTerminationDate() >= shift.getWorkDate();
            } else {
                return shift.getDateType() == 1;
            }
        }).collect(Collectors.toList());
        empWorkTimeDayMap.putAll(filters.stream().collect(Collectors.groupingBy(EmpShiftInfo::getEmpid, Collectors.counting())));
        Map<Long, IntSummaryStatistics> empWorkTimeMap = filters.stream().collect(Collectors.groupingBy(EmpShiftInfo::getEmpid, Collectors.summarizingInt(EmpShiftInfo::doGetWorkTotalTime)));
        return empWorkTimeMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> BigDecimal.valueOf(e.getValue().getSum()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP)));
    }

    /**
     * 生成 请假加班出差列
     *
     * @param paramsMap
     * @param type
     * @param belongid
     * @return
     */
    private Map<String, List<String>> generateColumns(Map<String, Object> paramsMap, int type, String belongid) {
        List<String> ltField = new ArrayList<>();
        List<String> trField = new ArrayList<>();
        List<String> otField = new ArrayList<>();
        List<String> extField = new ArrayList<>();
        Map<String, String> otNameMap = Maps.newHashMap();
        StringBuffer columns = new StringBuffer();
        Boolean isOrigin = (Boolean) paramsMap.get("isOrigin");
        Integer waGroupId = (Integer) paramsMap.get("waGroupId");
        String leaveField = "level_column_jsonb";
        if (isOrigin != null && isOrigin) {
            leaveField = "origin_level_column_jsonb";
        }
        List<WaLeaveTypeDo> leaveTypeDoList = waLeaveTypeDo.getLeaveTypeByGroupId(belongid, waGroupId);
        List<WaLeaveType> leaveTypes = ObjectConverter.convertList(leaveTypeDoList, WaLeaveType.class);
        if (leaveTypes != null && leaveTypes.size() > 0) {
            for (WaLeaveType lt : leaveTypes) {
                if (columns.length() > 0 && !columns.toString().endsWith(",")) {
                    columns.append(",");
                }
                String alias = "lt_" + lt.getLeaveType() + "_key";
                ltField.add(alias);
                String timeUnit = "lt_" + lt.getLeaveType() + "_key_unit";
                String nameKey = "lt_" + lt.getLeaveType() + "_name";
                String aliasNameKey = "lt_" + lt.getLeaveType() + "_key_name";
                columns.append("cast(" + leaveField + " ->> '" + timeUnit + "' as integer)  as \"" + timeUnit + "\",");
                columns.append("cast(" + leaveField + " ->> '" + nameKey + "' as text)  as \"" + aliasNameKey + "\",");
                if (type == 1) {
                    columns.append("cast(" + leaveField + " ->> '" + alias + "' as numeric)  as \"" + alias + "\"");
                } else if (type == 2) {
                    columns.append("round(cast(" + leaveField + " ->> '" + alias + "' as numeric)/60,2)  as \"" + alias
                            + "\"");
                }
            }
            /* 增加休假分钟字段 */
            columns.append(", cast(" + leaveField + "->> 'time_duration' as numeric) as \"" + "lt_time\",");
        }

        // 查询出差类型
        String travelField = "travel_column_jsonb";
        if (isOrigin != null && isOrigin) {
            travelField = "origin_travel_column_jsonb";
        }
        List<WaTravelTypeDo> travelTypeList = waTravelTypeDo.getWaTravelTypeList(belongid);
        if (CollectionUtils.isNotEmpty(travelTypeList)) {
            for (WaTravelTypeDo travelTypeDo : travelTypeList) {
                if (columns.length() > 0 && !columns.toString().endsWith(",")) {
                    columns.append(",");
                }
                String fileKey = "lt_" + travelTypeDo.getTravelTypeId() + "_key";
                String aliasKey = "tr_" + travelTypeDo.getTravelTypeId() + "_key";
                trField.add(aliasKey);
                String timeUnit = "lt_" + travelTypeDo.getTravelTypeId() + "_key_unit";
                String aliasUnitKey = "tr_" + travelTypeDo.getTravelTypeId() + "_key_unit";
                String nameKey = "lt_" + travelTypeDo.getTravelTypeId() + "_name";
                String aliasNameKey = "tr_" + travelTypeDo.getTravelTypeId() + "_key_name";
                columns.append("cast(" + travelField + " ->> '" + nameKey + "' as text)  as \"" + aliasNameKey + "\",");
                columns.append(
                        "cast(" + travelField + " ->> '" + timeUnit + "' as integer)  as \"" + aliasUnitKey + "\",");
                if (type == 1) {
                    columns.append(
                            "cast(" + travelField + " ->> '" + fileKey + "' as numeric)  as \"" + aliasKey + "\"");
                } else if (type == 2) {
                    columns.append("round(cast(" + travelField + " ->> '" + fileKey + "' as numeric)/60,2)  as \""
                            + aliasKey + "\"");
                }
            }
            /* 增加出差分钟字段 */
            columns.append(", cast(" + travelField + "->> 'time_duration' as numeric) as \"" + "travel_time\",");
        }

        String overtimeField = "ot_column_jsob";
        if (isOrigin != null && isOrigin) {
            overtimeField = "origin_ot_column_jsonb";
        }

        // 加班类型+补偿规则
        List<OverTimeTypeDo> overtimeTypes = overTimeTypeDo.getAllOtTypes(belongid, null,
                DateUtil.getCurrentTime(true));
        if (CollectionUtils.isNotEmpty(overtimeTypes)) {
            for (OverTimeTypeDo overtimeType : overtimeTypes) {
                if (columns.length() > 0 && !columns.toString().endsWith(",")) {
                    columns.append(",");
                }
                String alias = "ot_" + overtimeType.getOvertimeTypeId() + "_key";
                String timeUnit = "ot_" + overtimeType.getOvertimeTypeId() + "_key_unit";
                String aliasNameKey = "ot_" + overtimeType.getOvertimeTypeId() + "_key_name";
                columns.append(
                        "cast(" + overtimeField + " ->> '" + timeUnit + "' as integer)  as \"" + timeUnit + "\",");
                otField.add(alias);
                if (type == 1) {
                    columns.append("cast(" + overtimeField + " ->> '" + alias + "' as numeric)  as \"" + alias + "\"");
                } else if (type == 2) {
                    columns.append("round(cast(" + overtimeField + " ->> '" + alias + "' as numeric)/60,2)  as \""
                            + alias + "\"");
                }
                otNameMap.put(aliasNameKey, overtimeType.getTypeName());
            }
            columns.append(", cast(" + overtimeField + "->> 'time_duration' as numeric) as \"" + "ot_time\"");
        }
        if (columns.length() > 0) {
            if (!columns.toString().endsWith(",")) {
                columns.append(",a.signin_id");
                columns.append(",a.signoff_id");
            } else {
                columns.append("a.signin_id");
                columns.append(",a.signoff_id");
            }
            paramsMap.put("columns", "," + columns);
        }
        Map fieldMap = new HashMap();
        fieldMap.put("lt", ltField);
        fieldMap.put("ot", otField);
        fieldMap.put("tr", trField);
        fieldMap.put("ext", extField);
        fieldMap.put("otName", otNameMap);
        return fieldMap;
    }

    public void setDefaultValue(Map<String, List<String>> fieldList, Map map) {
        // 标准列
        String excludeKey = "is_exp|is_shift|is_kg";
        List<String> other = fieldList.get("other");
        other.forEach(key -> {
            if ((!excludeKey.contains(key)) && isNotContainsKeyOrisNullOrEmpty(map, key)) {
                map.put(key, 0);
                map.put(key.concat("_minute"), 0);
            }
        });
        // 加班列
        List<String> otlist = fieldList.get("ot");
        otlist.forEach((key) -> {
            if (isNotContainsKeyOrisNullOrEmpty(map, key)) {
                map.put(key, 0);
                map.put(key.concat("_minute"), 0);
            }
        });

        // 请假列
        List<String> ltlist = fieldList.get("lt");
        ltlist.forEach(key -> {
            if (isNotContainsKeyOrisNullOrEmpty(map, key)) {
                map.put(key, 0);
                map.put(key.concat("_minute"), 0);
            }
        });

        // 出差列
        List<String> trList = fieldList.get("tr");
        trList.forEach(key -> {
            if (isNotContainsKeyOrisNullOrEmpty(map, key)) {
                map.put(key, 0);
                map.put(key.concat("_minute"), 0);
            }
        });

        List<String> extlist = fieldList.get("ext");
        extlist.forEach(key -> {
            if (isNotContainsKeyOrisNullOrEmpty(map, key)) {
                map.put(key, 0);
            }
        });
    }

    public boolean isNotContainsKeyOrisNullOrEmpty(Map map, String key) {

        return !map.containsKey(key) || map.get(key) == null || StringUtil.isEmpty(map.get(key).toString());
    }

    private WaParseGroup getEmpGroup(Map<String, Object> paramsMap, Integer waGroupId, Integer ym) {
        if (waGroupId == null)
            return null;
        WaGroup group = waGroupMapper.selectByPrimaryKey(waGroupId);
        if (group.getIsDefault()) {
            paramsMap.put("defaultGroup", true);
        } else {
            paramsMap.put("defaultGroup", false);
        }
        Integer date = group.getCyleStartdate();
        if (date == null) {
            paramsMap.put("startDate", 0);
            paramsMap.put("endDate", 0);
            return null;
        }
        Integer parseGroupId = group.getParseGroupId();
        return waParseGroupMapper.selectByPrimaryKey(parseGroupId);
    }

    /**
     * 考勤分析月度数据汇总
     *
     * @param empDailyWaAnalyzeList
     * @param originMap
     * @param analyze
     * @param flist
     * @param isInterval
     * @param ltField
     * @param tenantId
     */
    private void doWaStatistic(Long empId, List<WaLeaveDetailDo> leaveList, List<WaLogicConfigDo> waLogicConfigDos, List<Map> empDailyWaAnalyzeList, Map<String, Object> originMap, boolean summary, WaParseGroup analyze, List<String> flist,
                                Integer isInterval, List<String> ltField, List<String> trField, Map<String, String> otNameMap, String tenantId) {
        if (CollectionUtils.isEmpty(empDailyWaAnalyzeList)) {
            log.debug("analysisRegister.empDailyWaAnalyzeList empty");
            return;
        }
        // 迟到早退豁免
        exemptLateAndEarlyTime(empDailyWaAnalyzeList, analyze, isInterval);
        if (CollectionUtils.isNotEmpty(waLogicConfigDos) && analyze != null) {
            // 执行自定义计算逻辑：迟到早退转旷工
            doExeCustomLogicWithLateEarlyTimeToKg(waLogicConfigDos, originMap, empDailyWaAnalyzeList, analyze,
                    tenantId);
        } else {
            log.debug("doExeCustomLogicWithLateEarlyTimeToKg fail waLogicConfigDos empty, tenantId={}", tenantId);
        }
        // 汇总计算
        int registerErrorCount = 0;
        BigDecimal otSumTime = new BigDecimal(0);
        BigDecimal registerTimeDay = BigDecimal.ZERO;
        // 实际出勤总时长
        BigDecimal registerTimeTotal = BigDecimal.ZERO;
        // 正常工作日工时
        BigDecimal registerNormalTimeTotal = BigDecimal.ZERO;
        // 加班总时长
        BigDecimal overworkTimeTotal = BigDecimal.ZERO;
        for (Map map : empDailyWaAnalyzeList) {
            BigDecimal registerTimeTotalDay = BigDecimal.ZERO;
            BigDecimal registerNormalTotalDay = BigDecimal.ZERO;
            BigDecimal overworkTimeTotalDay = BigDecimal.ZERO;
            if (map.get("registerErrorCount") != null) {
                registerErrorCount = registerErrorCount + Integer.parseInt(map.get("registerErrorCount").toString());
            }
            Integer registerTime = Optional.ofNullable((Integer) map.get("register_time")).orElse(0);
            Integer workTime = Optional.ofNullable((Integer) map.get("work_time")).orElse(0);//min
            if (workTime != 0) {
                registerTimeDay = registerTimeDay.add(BigDecimal.valueOf(registerTime.floatValue() / workTime));
            } else if (registerTime > 0) {
                registerTimeDay = registerTimeDay.add(BigDecimal.valueOf(registerTime.floatValue() / 480));
            }
            boolean isKg = (int) map.getOrDefault("is_kg", 0) == 1;

            val date = (Long)map.get("belong_date");
            val leaveTime = leaveList.stream().filter(it->empId.equals(it.getEmpId()) && date.equals(it.getLeaveDate())).mapToDouble(it->{
                Double duration = it.getTimeDuration() - it.getCancelTimeDuration();
                if(1 == it.getTimeUnit()){
                    duration = duration * 8 * 60;
                }
                return duration;
            }).sum();
            //Integer leaveTime = Optional.ofNullable((Integer) map.get("leave_time")).orElse(0);


            val lateTime = Optional.ofNullable((Number)map.get("late_time")).orElse(0d).doubleValue();
            val earlyTime = Optional.ofNullable((Number)map.get("early_time")).orElse(0d).doubleValue();
            // //旷工||迟到早退>30||全天假
            if (isKg || lateTime+earlyTime>30 || leaveTime>=workTime) {

            }
            // 请非全天假
            else if (leaveTime > 0) {//非全天假
                BigDecimal bigDecimal = BigDecimal.valueOf(Math.max(workTime - leaveTime, 0));
                registerTimeTotalDay = registerTimeTotalDay.add(bigDecimal);
                registerNormalTotalDay = registerNormalTotalDay.add(bigDecimal);
            }
            // 正常出勤
            else {
                registerTimeTotalDay = registerTimeTotalDay.add(BigDecimal.valueOf(workTime));
                registerNormalTotalDay = registerNormalTotalDay.add(BigDecimal.valueOf(workTime));
            }
            Long belongDate = (Long) map.get("belong_date");
            String summaryItemKey = "summary_%s_%s";
            for (String k : flist) {
                String unitKey;
                String nameKey;
                if (!isNotContainsKeyOrisNullOrEmpty(map, k)) {
                    if (ltField.contains(k) || trField.contains(k)) {
                        unitKey = k + "_unit";
                        nameKey = k + "_name";
                        originMap.put(unitKey, map.get(unitKey));
                        originMap.put(nameKey, map.get(nameKey));
                    }
                    if (k.contains("ot_") && !k.contains("_unit")) {
                        unitKey = k + "_unit";
                        nameKey = k + "_name";
                        originMap.put(unitKey, map.get(unitKey));
                        originMap.put(nameKey, otNameMap.get(nameKey));
                    }
                    Object v = map.get(k);
                    SummaryLabelEnum labelEnum = summary ? SummaryLabelEnum.indexLabel(k) : null;
                    if ((v instanceof Double) || (v instanceof Float) || (v instanceof BigDecimal)) {
                        BigDecimal a = new BigDecimal(String.valueOf(v));
                        if (a.compareTo(BigDecimal.ZERO) != 0 && labelEnum != null) {
                            if (k.contains("ot_") && !k.contains("_unit")) {
                                overworkTimeTotalDay = overworkTimeTotalDay.add(a.multiply(BigDecimal.valueOf(60)));
                            }
                            originMap.put(String.format(summaryItemKey, belongDate, k), a);
                        }
                        if (originMap.containsKey(k)) {
                            BigDecimal b = new BigDecimal(String.valueOf(originMap.get(k)));
                            originMap.put(k, a.add(b));
                        } else {
                            originMap.put(k, a);
                        }
                    } else if (v instanceof Integer) {
                        Integer a = Integer.valueOf(v.toString());
                        if (a != 0 && labelEnum != null) {
                            originMap.put(String.format(summaryItemKey, belongDate, k), a);
                        }
                        if (originMap.containsKey(k)) {
                            Integer b = Integer.valueOf(originMap.get(k).toString());
                            originMap.put(k, a + b);
                        } else {
                            originMap.put(k, a);
                        }
                    } else {
                        if (StringUtils.isNotBlank(Objects.toString(v)) && labelEnum != null) {
                            originMap.put(String.format(summaryItemKey, belongDate, k), v);
                        }
                        originMap.put(k, v);
                    }
                }
            }
            originMap.put(String.format(summaryItemKey, belongDate, REGISTER_TIME_TOTAL.getKey()), registerTimeTotalDay.add(overworkTimeTotalDay));
            originMap.put(String.format(summaryItemKey, belongDate, REGISTER_NORMAL_TIME_TOTAL.getKey()), registerNormalTotalDay);

            registerTimeTotal = registerTimeTotal.add(registerTimeTotalDay);
            overworkTimeTotal = overworkTimeTotal.add(overworkTimeTotalDay);
            registerNormalTimeTotal = registerNormalTimeTotal.add(registerNormalTotalDay);
        }
        originMap.put("register_time_day", registerTimeDay.setScale(2, RoundingMode.HALF_DOWN).toPlainString());
        originMap.put("otSumTime", MathUtil.getRelHour(otSumTime.longValue()));
        originMap.put("registerErrorCount", registerErrorCount);
        originMap.put("registerTimeTotal", registerTimeTotal.add(overworkTimeTotal).divide(BigDecimal.valueOf(60),2,RoundingMode.HALF_DOWN));
        originMap.put("registerNormalTimeTotal", registerNormalTimeTotal.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN));
    }

    /**
     * 执行自定义计算逻辑
     *
     * @param empWaStatisticMap
     * @param empDailyWaAnalyzeList
     * @param waParseGroup
     * @param tenantId
     */
    private void doExeCustomLogicWithLateEarlyTimeToKg(List<WaLogicConfigDo> waLogicConfigDos, Map empWaStatisticMap,
            List<Map> empDailyWaAnalyzeList,
            WaParseGroup waParseGroup, String tenantId) {
        String logicExp = waLogicConfigDos.get(0).getLogicExp();
        if (StringUtils.isBlank(logicExp)) {
            log.debug("doExeCustomLogicWithLateEarlyTimeToKg fail logicExp empty, tenantId={}", tenantId);
            return;
        }
        Map<String, Object> binding = new HashMap<>();
        binding.put("empWaStatisticeMap", empWaStatisticMap);
        binding.put("empDailyWaAnalyzeList", empDailyWaAnalyzeList);
        binding.put("waParseGroup", waParseGroup);
        binding.put("tenantId", tenantId);
        groovyScriptEngine.executeBigDecimal(logicExp, binding);
    }

    /**
     * 迟到早退豁免
     *
     * @param empRegList
     * @param analyze
     * @param isInterval
     */
    private void exemptLateAndEarlyTime(List<Map> empRegList, WaParseGroup analyze, Integer isInterval) {
        Integer lateAllowUnit, earlyAllowUnit, lateCount, earlyCount;
        double lateExemptDuration, earlyExemptDuration;
        if (analyze != null) {
            // 迟到豁免单位：1 按照次数 、 2 按照时长（分钟）
            lateAllowUnit = Optional.ofNullable(analyze.getLateAllowUnit()).orElse(0);
            // 早退豁免单位：1 按照次数 、 2 按照时长（分钟）
            earlyAllowUnit = Optional.ofNullable(analyze.getEarlyAllowUnit()).orElse(0);
            // 迟到豁免次数
            lateCount = Optional.ofNullable(analyze.getLateAllowNumber()).orElse(0);
            // 早退豁免次数
            earlyCount = Optional.ofNullable(analyze.getEarlyAllowNumber()).orElse(0);

            // 迟到豁免时长
            lateExemptDuration = null != analyze.getLateCount() ? analyze.getLateCount().doubleValue() : 0d;
            if (analyze.getLateUnit() != null && analyze.getLateUnit() == 1) {
                lateExemptDuration *= 60;
            }

            // 早退豁免时长
            earlyExemptDuration = null != analyze.getEarlyCount() ? analyze.getEarlyCount().doubleValue() : 0d;
            if (analyze.getEarlyUnit() != null && analyze.getEarlyUnit() == 1) {
                earlyExemptDuration *= 60;
            }
        } else {
            lateAllowUnit = earlyAllowUnit = lateCount = earlyCount = 0;
            lateExemptDuration = earlyExemptDuration = 0;
        }

        // 迟到、早退豁免
        for (Map map : empRegList) {
            String key = "late_time";
            double lateTime = 0d;
            if (map.get(key) != null) {
                lateTime = Double.parseDouble(map.get(key).toString());
            }
            if (lateTime > 0) {
                if (lateAllowUnit == 1 && lateCount > 0) {
                    // 按照次数豁免
                    // 如果迟到分钟数不在允许范围内则不予豁免
                    if (lateTime <= lateExemptDuration && isInterval == 0) {
                        lateTime = 0d;
                        lateCount--;
                    }
                } else if (lateAllowUnit == 2 && lateExemptDuration > 0) {
                    // 按照时长豁免
                    if (lateTime > lateExemptDuration) {
                        lateExemptDuration = 0d;
                        lateTime = MathUtil.sub(lateTime, lateExemptDuration);
                    } else {
                        lateExemptDuration = MathUtil.sub(lateExemptDuration, lateTime);
                        lateTime = 0d;
                    }
                }
                map.put(key, lateTime);
                if (lateTime > 0) {
                    map.put("latecount", 1);
                }
            }
            key = "early_time";
            double earlyTime = 0d;
            if (map.get(key) != null) {
                earlyTime = Double.parseDouble(map.get(key).toString());
            }
            if (earlyTime > 0) {
                if (earlyAllowUnit == 1 && earlyCount > 0) {
                    // 按照次数豁免
                    // 如果早退分钟数不在允许范围内则不予豁免
                    if (earlyTime <= earlyExemptDuration && isInterval == 0) {
                        earlyTime = 0d;
                        earlyCount--;
                    }
                } else if (earlyAllowUnit == 2 && earlyExemptDuration > 0 && analyze.getEarlyUnit() != null) {
                    // 按照时长豁免
                    if (earlyTime > earlyExemptDuration) {
                        earlyExemptDuration = 0d;
                        earlyTime = MathUtil.sub(earlyTime, earlyExemptDuration);
                    } else {
                        earlyExemptDuration = MathUtil.sub(earlyExemptDuration, earlyTime);
                        earlyTime = 0d;
                    }
                }
                map.put(key, earlyTime);
                if (earlyTime > 0) {
                    map.put("earlycount", 1);
                }
            }
        }
    }

    private void convertData(Map<String, Object> originMap, List<String> fields, Boolean isReturnNegative,
            List<String> extFiled, List<String> ltField, List<String> trField) {
        // 返回值是否允许是负数，默认false
        boolean finalIsReturnNegative = isReturnNegative != null && isReturnNegative;
        String a = "is_exp|is_shift|is_kg|latecount|earlycount!bdk_count";
        if (originMap != null && originMap.size() > 0) {
            Set<String> fieldSet = Sets.newHashSet(originMap.keySet());
            for (String key : fieldSet) {
                final String summaryKeyRegex = "^summary_(\\d+)_([\\w\\d]+)$";
                Pattern pattern = Pattern.compile(summaryKeyRegex);
                boolean matchSummary = false;
                String fileKey = key;
                Matcher matcher = pattern.matcher(key);
                if (matcher.find()) {
                    fileKey = matcher.group(2);
                    matchSummary = true;
                } else if (!fields.contains(key)) {
                    continue;
                }
                if (!isNotContainsKeyOrisNullOrEmpty(originMap, fileKey) && (!a.contains(fileKey))) {
                    // 此集合的字段不做分钟转小时转换
                    boolean isConvert = (!CollectionUtils.isNotEmpty(extFiled) || !extFiled.contains(fileKey))
                            && !fileKey.contains("ot_");
                    // 单位转换 分钟转小时
                    if (isConvert) {
                        Double l = getFiledValue(originMap, key);
                        if (l != null) {
                            if (ltField.contains(fileKey) || trField.contains(fileKey)) {
                                Integer unit = (Integer) originMap.get(fileKey + "_unit");
                                if (unit != null && unit == 1) {
                                    continue;
                                }
                            }
                            // 分钟转换为小时
                            BigDecimal v = new BigDecimal(l).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP)
                                    .stripTrailingZeros();
                            if (!finalIsReturnNegative && v.doubleValue() < 0) {
                                v = new BigDecimal("0");
                            }
                            double d = v.doubleValue();
                            if ("late_time".equalsIgnoreCase(fileKey) || "early_time".equalsIgnoreCase(fileKey)
                                    || "kg_work_time".equalsIgnoreCase(fileKey)
                                    || key.startsWith("ot") || "late_time_adjust".equalsIgnoreCase(fileKey)
                                    || "early_time_adjust".equalsIgnoreCase(fileKey)) {
                                Float value = new BigDecimal(String.valueOf(l)).setScale(2, RoundingMode.HALF_UP)
                                        .floatValue();
                                if (matchSummary) {
                                    originMap.put(key, value);
                                    continue;
                                } else {
                                    originMap.put(key.concat("_minute"), value);
                                }
                            }
                            originMap.put(key, d);
                        }
                    }
                }
            }
        }
    }

    @Nullable
    private Double getFiledValue(Map originMap, String key) {
        Double l = null;
        if (originMap.get(key) instanceof Double) {
            l = (Double) originMap.get(key);
        } else if (originMap.get(key) instanceof Integer) {
            Integer value = (Integer) originMap.get(key);
            if (value != null) {
                l = value.doubleValue();
            }
        } else if (originMap.get(key) instanceof BigDecimal) {
            BigDecimal value = (BigDecimal) originMap.get(key);
            if (value != null) {
                l = value.doubleValue();
            }
        } else if (originMap.get(key) instanceof Float) {
            Float value = (Float) originMap.get(key);
            if (value != null) {
                l = value.doubleValue();
            }
        }
        return l;
    }

    /**
     * 支持多个考勤分组ID的月度统计查询
     */
    public List<Map> searchRegMonthStatisticsMultiGroup(PageBean pageBean, Map<String, Object> paramsMap,
            ImmutablePair<Long, Long> summaryPeriod, Boolean summary, UserInfo userInfo) {
        String belongId = (String) paramsMap.get("belongid");
        List<String> flist = getStandardWaFields();
        Map fieldMap = generateColumns(paramsMap, 1, belongId);
        fieldMap.put("other", flist);
        List<String> ltField = (List<String>) fieldMap.get("lt");
        flist.addAll(ltField);
        List<String> trField = (List<String>) fieldMap.get("tr");
        flist.addAll(trField);
        List<String> otField = (List<String>) fieldMap.get("ot");
        flist.addAll(otField);
        List<String> extFiled = (List<String>) fieldMap.get("ext");
        flist.addAll(extFiled);
        Map<String, String> otNameMap = (Map<String, String>) fieldMap.get("otName");
        flist = flist.stream().distinct().collect(Collectors.toList());


        Long startDate = (Long) paramsMap.get("startDate");
        Long endDate = (Long) paramsMap.get("endDate");
        List<Integer> waGroupIds = (List<Integer>) paramsMap.get("waGroupIds");

        if (CollectionUtils.isEmpty(waGroupIds)) {
            // 如果没有传入考勤分组ID，则返回空结果
            return new ArrayList<>();
        }

        // 查询员工每日考勤分析结果
        List<Map<String, Object>> empDailyWaAnalyzeList = workOvertimeMapper.searchRegMonthList(paramsMap);
        Map<Long, List<Map>> empDailyWaAnalyzeListMap = empDailyWaAnalyzeList.stream()
                .collect(Collectors.groupingBy(map -> (Long) map.get("empid")));
        // 补打卡次数查询
        Map<Long, Integer> fillClockMap = getFillClockMap(paramsMap);
        // 考勤数据汇总
        Integer isInterval = Optional.ofNullable((Integer) paramsMap.get("isInterval")).orElse(0);
        String clockResultFilter = (String) paramsMap.get("status");
        Integer statisticsType = (Integer) paramsMap.get("statisticsType");
        List<WaLogicConfigDo> lateEarlyTimeToKg = waLogicConfigDomainService.getListByCodes(belongId,
                Lists.newArrayList("LateEarlyTimeToKg"));
        val leaveDetail = waLeaveService.listLeaveDetail(summaryPeriod.left, summaryPeriod.right, empDailyWaAnalyzeListMap.entrySet().stream().map(it->it.getKey()).collect(Collectors.toList()));

        // 遍历所有考勤分组，获取每个分组的统计数据
        Map<String, Map> empWaStatisticsMap = new HashMap<>();

        for (Integer waGroupId : waGroupIds) {
            Map<String, Object> singleGroupParams = new HashMap<>(paramsMap);
            singleGroupParams.put("waGroupId", waGroupId);

            // 查询考勤分析规则
            Integer ym = (Integer) paramsMap.get("ym");
            WaParseGroup analyze = getEmpGroup(paramsMap, waGroupId, ym);

            for (Map.Entry<Long, List<Map>> entry : empDailyWaAnalyzeListMap.entrySet()) {
                Long empId = entry.getKey();
                Map<String, Object> originMap = new HashMap<>();
                originMap.put("empid", empId);
                if (fillClockMap.containsKey(empId)) {
                    originMap.put("bdk_count", fillClockMap.get(empId));
                }
                doWaStatistic(empId, leaveDetail,lateEarlyTimeToKg, entry.getValue(), originMap, summary, analyze, flist, isInterval, ltField,
                        trField, otNameMap, belongId);
                convertData(originMap, flist, false, extFiled, ltField, trField);
                setDefaultValue(fieldMap, originMap);
                doHandleOtField(otField, originMap,belongId);
                convertKgTimeToKgDay(originMap);
                doSummaryTxt(empId, originMap, summaryPeriod, summary);
                // 根据考勤结果状态进行数据过滤
                if (filterWaResult(originMap, clockResultFilter, statisticsType)) {
                    String empGroupKey = empId + "_" + waGroupId;
                    empWaStatisticsMap.put(empGroupKey, originMap);
                }
            }
        }

        // 查询多个考勤分组下的所有员工信息
        var empGroupKeys = Lists.newArrayList(empWaStatisticsMap.keySet());
        if (empGroupKeys.isEmpty()) {
            return new ArrayList<>();
        }

        // 从empGroupKeys中提取empIds
        List<Long> empIds = empGroupKeys.stream()
                .map(key -> Long.valueOf(key.split("_")[0]))
                .distinct()
                .collect(Collectors.toList());

        paramsMap.put("empIds", empIds);
        paramsMap.put("waGroupIds", waGroupIds);
        paramsMap.put("isOnlyShowOrgName", true);

        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(),
                Order.formString(pageBean.getOrder()));
        PageList<Map> empWaStatisticsPageResult = registerRecordMapper.searchEmpInfoListMultiGroup(pageBounds,
                paramsMap);

        Map<Long, BigDecimal> empCompensatoryMap = handleCompensatory(empIds, startDate, endDate);
        combineEmpAndWaMultiGroup(empWaStatisticsPageResult, empWaStatisticsMap, empCompensatoryMap, belongId,
                paramsMap, summary, userInfo);

        Map<Long, Map<String, String>> empLeaveMap = getEmpLeaveMap(belongId, startDate, endDate, empIds);
        Map<Long, Map<String, Object>> fullPathMap = getEmpWorkInfoMap(belongId, empWaStatisticsPageResult.stream()
                .map(row -> row.get("empid").toString())
                .collect(Collectors.toList()));
        for (Map empMap : empWaStatisticsPageResult) {
            Long empId = (Long) empMap.get("empid");
            if (empLeaveMap.containsKey(empId)) {
                empMap.putAll(empLeaveMap.get(empId));
            }
            if (fullPathMap.containsKey(empId)) {
                empMap.putAll(fullPathMap.get(empId));
            }
        }

        return empWaStatisticsPageResult;
    }

    /**
     * 组合员工信息和考勤数据（支持多个考勤分组）
     */
    private void combineEmpAndWaMultiGroup(PageList<Map> empWaStatisticsPageResult, Map<String, Map> empWaStatisticsMap,
            Map<Long, BigDecimal> empCompensatoryMap, String belongId, Map<String, Object> paramsMap,
            Boolean summary, UserInfo userInfo) {
        if (CollectionUtils.isEmpty(empWaStatisticsPageResult)) {
            return;
        }

        Long startDate = Long.valueOf(paramsMap.get("startDate").toString());
        Long endDate = Long.valueOf(paramsMap.get("endDate").toString());
        Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(belongId);
        Map<Long, BigDecimal> empShiftInfosMap = new HashMap<>();
        Map<Long, Long> empWorkTimeDayMap = new HashMap<>();
        Map<String, EmpShiftInfo> empShiftMap = new HashMap<>();
        Map<Long, List<AttEmpGroupDo>> empGroupMap = new HashMap<>();

        // 获取所有员工ID
        List<Long> selectedEmpIds = empWaStatisticsPageResult.stream()
                .map(l -> Long.valueOf(l.get("empid").toString()))
                .distinct()
                .collect(Collectors.toList());

        for (List<Long> splitEmpIds : ListTool.split(selectedEmpIds, 500)) {
            if (CollectionUtils.isEmpty(splitEmpIds)) {
                continue;
            }
            empShiftInfosMap.putAll(getEmpShiftWorkTimeMaps(belongId, startDate, endDate, splitEmpIds, shiftDefMap,
                    empWorkTimeDayMap, empShiftMap));
            empGroupMap.putAll(getEmpGroupMap(belongId, splitEmpIds, startDate, endDate + 86399));
        }

        Map<Object, String> employTypeMap = new HashMap<>();
        Map<Object, String> empStatusMap = new HashMap<>();

        for (Map originMap : empWaStatisticsPageResult) {
            // 处理员工类型和状态
            if (originMap.containsKey("employ_type")) {
                if (employTypeMap.containsKey(originMap.get("employ_type"))) {
                    originMap.put("employ_type_name", employTypeMap.get(originMap.get("employ_type")));
                } else {
                    if (originMap.get("employ_type") != null) {
                        String employTypeName = textAspect.getDictCache(originMap.get("employ_type").toString(),
                                ResponseWrap.getLocale());
                        employTypeMap.put(originMap.get("employ_type"), employTypeName);
                        originMap.put("employ_type_name", employTypeName);
                    }
                }
            }

            if (originMap.containsKey("emp_status")) {
                if (empStatusMap.containsKey(originMap.get("emp_status"))) {
                    originMap.put("emp_status_name", empStatusMap.get(originMap.get("emp_status")));
                } else {
                    if (null != originMap.get("emp_status")) {
                        String empStatusName = textAspect.getEmpStatusEnumText(originMap.get("emp_status").toString(),
                                belongId);
                        originMap.put("emp_status_name", empStatusName);
                        empStatusMap.put(originMap.get("emp_status"), empStatusName);
                    }
                }
            }

            // 根据员工ID和考勤分组ID获取统计结果
            String empGroupKey = originMap.get("bid").toString(); // bid格式为: empid_groupid
            Long empId = (Long) originMap.get("empid");
            if (empWaStatisticsMap.containsKey(empGroupKey) && null != empWaStatisticsMap.get(empGroupKey)) {
                Long hireDate = (Long) originMap.get("hire_date");
                Map empResultMap = empWaStatisticsMap.get(empGroupKey);

                if (summary != null && summary) {
                    List<Object> daySummaryList = (List<Object>) empResultMap.keySet().stream()
                            .filter(it -> Objects.toString(it).startsWith("summary_"))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(daySummaryList)) {
                        for (Object it : daySummaryList) {
                            try {
                                String[] arr = Objects.toString(it).split("_");
                                String dayLabel = arr[1];
                                EmpShiftInfo shiftInfo = empShiftMap.get(String.format("%s_%s", empId, dayLabel));
                                if (empResultMap.get(it) != null && StringUtils.isNotBlank(Objects.toString(empResultMap.get(it)))) {
                                    empResultMap.put(String.format("record_%s", dayLabel), getEmpRegisterRecord(Long.valueOf(dayLabel), empId, userInfo, shiftInfo, originMap));
                                } else if (hireDate != null && hireDate > Long.parseLong(dayLabel)) {
                                    empResultMap.put(it, "-");
                                } else {
                                    WaParseGroup parseGroup = myCenterService.calcClockType(new MyWorkEventVo(), Long.valueOf(dayLabel), empId, userInfo);
                                    checkBdkAndGetRegister(empId, Long.valueOf(dayLabel), parseGroup, originMap);
                                }
                            } catch (NumberFormatException e) {
                                log.error("parse day label error, text:{}, error:{}", it, e.getMessage(), e);
                            }
                        }
                    }
                }

                if (empGroupMap.containsKey(empId)) {
                    List<AttEmpGroupDo> empGroups = empGroupMap.get(empId);
                    StringBuilder groupBuilder = new StringBuilder();
                    for (AttEmpGroupDo empGroup : empGroups) {
                        String groupName = LangParseUtil.getI18nLanguage(empGroup.getI18nWaGroupName(),
                                empGroup.getWaGroupName());
                        groupBuilder.append(groupName).append(":");
                        if (startDate >= empGroup.getStartTime() && startDate < empGroup.getEndTime()) {
                            groupBuilder.append(DateUtil.getDateStrByTimesamp(startDate)).append("~")
                                    .append(DateUtil.getDateStrByTimesamp(empGroup.getEndTime())).append("\n");
                        } else if (empGroup.getStartTime() <= endDate && endDate < empGroup.getEndTime()) {
                            groupBuilder.append(DateUtil.getDateStrByTimesamp(empGroup.getStartTime())).append("~")
                                    .append(DateUtil.getDateStrByTimesamp(endDate));
                        } else {
                            groupBuilder.append(DateUtil.getDateStrByTimesamp(empGroup.getStartTime())).append("~")
                                    .append(DateUtil.getDateStrByTimesamp(empGroup.getEndTime())).append("\n");
                        }
                    }
                    originMap.put("wa_group_name", groupBuilder.toString());
                }

                originMap.putAll(empResultMap);
            }
            int latecount = originMap.get("latecount") == null ? 0 : (Integer) originMap.get("latecount");// 迟到次数
            int earlycount = originMap.get("earlycount") == null ? 0 : (Integer) originMap.get("earlycount");// 早退次数
            int isKg = originMap.get("is_kg") == null ? 0 : (Integer) originMap.get("is_kg");// 旷工次数
            if (latecount > 0 || earlycount > 0 || isKg > 0) {
                originMap.put("status", ClockResultEnum.ABNORMAL.getIndex());
            } else {
                originMap.put("status", ClockResultEnum.NORMAL.getIndex());
            }
            if (empCompensatoryMap.containsKey(empId)) {
                originMap.put("compensatory", empCompensatoryMap.get(empId).doubleValue());
            } else {
                originMap.put("compensatory", 0);
            }
            EmpInfoDTO empInfoDTO = FastjsonUtil.convertObject(originMap, EmpInfoDTO.class);
            empInfoDTO.setName((String) originMap.get("emp_name"));
            originMap.put("empInfo", empInfoDTO);
            if (originMap.containsKey("work_time")) {
                originMap.put("work_time", empShiftInfosMap.get(empId));
                originMap.put("work_time_day", empWorkTimeDayMap.get(empId));
            }
            BigDecimal travelDuration = BigDecimal.ZERO;
            BigDecimal travelDurationDay = BigDecimal.ZERO;
            BigDecimal leaveDuration = BigDecimal.ZERO;
            BigDecimal leaveDurationDay = BigDecimal.ZERO;
            for (Object o : originMap.keySet()) {
                String key = o.toString();
                if (key.contains("tr_") && key.contains("_key_unit")) {
                    String[] arr = key.split("_");
                    Integer unit = (Integer) originMap.get(key);
                    BigDecimal duration = new BigDecimal(originMap.get(String.format("tr_%s_key", arr[1])).toString());
                    if (2 == unit) {
                        travelDuration = travelDuration.add(duration);
                    } else {
                        travelDurationDay = travelDurationDay.add(duration);
                    }
                }
                if (key.contains("lt_") && key.contains("_key_unit")) {
                    String[] arr = key.split("_");
                    Integer unit = (Integer) originMap.get(key);
                    BigDecimal duration = new BigDecimal(originMap.get(String.format("lt_%s_key", arr[1])).toString());
                    if (2 == unit) {
                        leaveDuration = leaveDuration.add(duration);
                    } else {
                        leaveDurationDay = leaveDurationDay.add(duration);
                    }
                }
                if (key.contains("approved_bdk_") && (Boolean) originMap.get(key)) {
                    String[] arr = key.split("_");
                    String summaryKey = String.format("summary_%s", arr[2]);
                    Object summaryValue = originMap.get(summaryKey);
                    if (StringUtil.isNotEmpty(summaryValue)) {
                        originMap.put(summaryKey, String.format("%s,%s", summaryValue,
                                CalendarStatusEnum.getName(CalendarStatusEnum.PATCH.getIndex())));
                    } else {
                        originMap.put(summaryKey, "approved_bdk");
                    }
                }
            }
            originMap.put("leave_duration", leaveDuration.toPlainString());
            originMap.put("leave_duration_day", leaveDurationDay.toPlainString());
            originMap.put("travel_duration", travelDuration.toPlainString());
            originMap.put("travel_duration_day", travelDurationDay.toPlainString());
        }
    }
}