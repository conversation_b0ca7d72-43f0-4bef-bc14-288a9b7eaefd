package com.caidaocloud.attendance.service.interfaces.vo;

import com.caidaocloud.attendance.service.application.dto.clock.ClockListShiftDefDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class RegisterRecordVo {
    @ApiModelProperty("打卡记录ID")
    private Integer recordId;
    @ApiModelProperty("帐号")
    private String workno;
    @ApiModelProperty("姓名")
    private String empName;
    @ApiModelProperty("员工类型")
    private Long empStyle;
    @ApiModelProperty("员工类型名称")
    private String empStyleName;
    @ApiModelProperty("入职日期")
    private Long hireDate;
    @ApiModelProperty("员工状态")
    private Integer empStatus;
    @ApiModelProperty("员工状态名称")
    private String empStatusName;
    @ApiModelProperty("组织")
    private String orgName;
    @ApiModelProperty("组织全路径")
    private String fullPath;
    @ApiModelProperty("打卡方式")
    private String typeName;
    @ApiModelProperty("打卡时间")
    private Long regDateTime;
    @ApiModelProperty("打卡地点")
    private String regAddr;
    @ApiModelProperty("设备号")
    private String mobDeviceNum;
    @ApiModelProperty("打卡时间范围")
    private String normalDate;
    @ApiModelProperty("用户名称")
    private String userName;
    @ApiModelProperty("创建时间")
    private Long crttime;
    @ApiModelProperty("数据来源")
    private String sourceFromTypeName;
    @ApiModelProperty("部门主键id")
    private Long orgid;
    @ApiModelProperty("考勤日期(归属日期)")
    private Long belongDate;
    @ApiModelProperty("打卡地点状态:0无效1有效")
    private Integer clockSiteStatus;
    @ApiModelProperty("1正常数据，2强制打卡数据")
    private Integer dataType;
    @ApiModelProperty("经度")
    private BigDecimal lng;
    @ApiModelProperty("纬度")
    private BigDecimal lat;
    @ApiModelProperty("原因")
    private String reason;
    @ApiModelProperty("班次ID")
    private Integer shiftDefId;
    @ApiModelProperty("班次信息")
    private List<ClockListShiftDefDto> shiftDefList;
    @ApiModelProperty("考勤状态编码；打卡结果：1 正常 2 异常")
    private Integer resultType;
}
