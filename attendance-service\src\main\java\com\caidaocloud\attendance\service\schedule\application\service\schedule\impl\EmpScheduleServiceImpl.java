package com.caidaocloud.attendance.service.schedule.application.service.schedule.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.*;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.wa.vo.MultiShiftSimpleVo;
import com.caidaocloud.attendance.core.wa.vo.MultiWorkTimeInfoSimpleVo;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.enums.CalendarStatusEnum;
import com.caidaocloud.attendance.service.application.service.ICacheCommonService;
import com.caidaocloud.attendance.service.domain.entity.SysEmpInfoDo;
import com.caidaocloud.attendance.service.infrastructure.feign.WfmFeignClient;
import com.caidaocloud.attendance.service.infrastructure.feign.wfm.GetSpecifiedProcessDto;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.attendance.service.schedule.application.service.dto.*;
import com.caidaocloud.attendance.service.schedule.application.service.enums.ScheduleDataTypeEnum;
import com.caidaocloud.attendance.service.schedule.application.service.notice.WaShiftNoticeService;
import com.caidaocloud.attendance.service.schedule.application.service.schedule.IEmpScheduleService;
import com.caidaocloud.attendance.service.schedule.application.service.shiftGroup.impl.WaEmpShiftGroupService;
import com.caidaocloud.attendance.service.schedule.application.service.utils.DateParseUtil;
import com.caidaocloud.attendance.service.schedule.domain.entity.WaEmpScheduleDo;
import com.caidaocloud.attendance.service.schedule.domain.entity.WaEmpScheduleDraftDo;
import com.caidaocloud.attendance.service.schedule.domain.entity.WaEmpShiftGroupDo;
import com.caidaocloud.attendance.service.schedule.domain.entity.WaShiftGroupDo;
import com.caidaocloud.attendance.service.schedule.interfaces.dto.schedule.*;
import com.caidaocloud.attendance.service.schedule.interfaces.dto.shiftGroup.ShiftGroupDto;
import com.caidaocloud.attendance.service.schedule.interfaces.dto.wfm.ProcessEmpInfoItemDto;
import com.caidaocloud.attendance.service.schedule.interfaces.dto.wfm.ScheduleCardDto;
import com.caidaocloud.attendance.service.schedule.interfaces.vo.schedule.CalendarDateDto;
import com.caidaocloud.attendance.service.schedule.interfaces.vo.schedule.EmpScheduleItemVo;
import com.caidaocloud.attendance.service.schedule.interfaces.vo.schedule.ScheduleItemVo;
import com.caidaocloud.attendance.service.wfm.application.dto.EmpMultiShiftInfoDto;
import com.caidaocloud.attendance.service.wfm.application.dto.WfmCalendarDateDto;
import com.caidaocloud.attendance.service.wfm.application.dto.WfmOrderInfo;
import com.caidaocloud.attendance.service.wfm.application.dto.WfmProductInfo;
import com.caidaocloud.attendance.service.wfm.application.enums.OrgDataScopeEnum;
import com.caidaocloud.attendance.service.wfm.application.service.OrgDataScopeService;
import com.caidaocloud.attendance.service.wfm.application.service.WfmHolidayService;
import com.caidaocloud.attendance.service.wfm.application.service.WfmShiftService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class EmpScheduleServiceImpl implements IEmpScheduleService {

    @Autowired
    private WaEmpScheduleDraftDo waEmpScheduleDraftDo;
    @Autowired
    private WaEmpScheduleDo waEmpScheduleDo;
    @Autowired
    private WfmHolidayService wfmHolidayService;
    @Autowired
    private SysEmpInfoDo sysEmpInfoDo;
    @Autowired
    private ICacheCommonService cacheCommonService;
    @Autowired
    private WaShiftNoticeService waShiftNoticeService;
    @Autowired
    private WaEmpShiftGroupDo waEmpShiftGroupDo;
    @Autowired
    private WfmShiftService wfmShiftService;
    @Autowired
    private WaShiftGroupDo waShiftGroupDo;
    @Resource
    private WfmFeignClient wfmFeignClient;

    @Autowired
    private OrgDataScopeService orgDataScopeService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private WaEmpShiftGroupService waEmpShiftGroupService;

    private final static String EMP_WORK_INFO_IDENTIFIER = "entity.hr.EmpWorkInfo";
    private final static String WORK_PROCESS_IDENTIFIER = "entity.wfm.ProcessManagement";
    private final static String WFM_ORDER_IDENTIFIER = "entity.wfm.OrderManagement";
    private final static String WFM_PRODUCT_IDENTIFIER = "entity.wfm.ProductManagement";

    private final SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    /**
     * 年月转换
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param isUnix    是否是秒
     * @return Map<Short, List < Short>>
     */
    private Map<Short, List<Short>> getYearAndMonthMap(Long startDate, Long endDate, Boolean isUnix) {
        //年月范围
        List<String> yearAndMonth = DateParseUtil.getMonthBetween(startDate, endDate, "yyyy-MM", isUnix);
        //解析排班年月按年分组
        return yearAndMonth.stream().collect(Collectors.groupingBy(row -> Short.valueOf(row.split("-")[0]), Collectors.mapping(row -> Short.valueOf(row.split("-")[1]), Collectors.toList())));
    }

    /**
     * 获取当前工序是否有扫码记录
     *
     * @return Map<String, Boolean>
     */
    private Map<String, Boolean> getRegisterRecordProcessMap() {
        try {
            Result<List<GetSpecifiedProcessDto>> result = wfmFeignClient.getProcessesStartedToday();
            // 检查结果是否有效
            if (isResultInvalid(result)) {
                return Collections.emptyMap();
            }
            List<GetSpecifiedProcessDto> items = result.getData();
            if (CollectionUtils.isEmpty(items)) {
                return Collections.emptyMap();
            }
            return items.stream().collect(Collectors.toMap(this::generateProcessKey, row -> true, (v1, v2) -> v2));
        } catch (Exception e) {
            log.error("Failed to get register record process map", e);
            return Collections.emptyMap();
        }
    }

    /**
     * generateProcessKey
     *
     * @param row 指定工序
     * @return String
     */
    private String generateProcessKey(GetSpecifiedProcessDto row) {
        return String.format("%s_%s_%s_%s", row.getSchedulingTime(), row.getProcessId(), row.getOrderId(), row.getShiftId());
    }

    /**
     * 响应结果校验
     *
     * @param result result
     * @return boolean
     */
    private boolean isResultInvalid(Result<?> result) {
        return result == null || result.getCode() != 0 || !result.isSuccess();
    }

    /**
     * 校验是否可编辑，如果当前工序的排班有扫码记录，则不可进行班次变更
     *
     * @param workDate                 排班日期
     * @param schedule                 班次
     * @param registerRecordProcessMap 工序扫码记录
     * @return boolean
     */
    private boolean checkIfEdit(Long workDate, ScheduleItemVo schedule, Map<String, Boolean> registerRecordProcessMap) {
        String mapKey = String.format("%s_%s_%s_%s", workDate * 1000, schedule.getSalaryProcessId(), schedule.getLeafNumberId(), schedule.getShiftId());
        return Optional.ofNullable(registerRecordProcessMap.get(mapKey)).orElse(false);
    }

    @Override
    public PageResult<EmpSchedulePageDto> getEmpScheduleList(EmpSchedulePage dto, UserInfo userInfo) {
        Long startDate = dto.getStartDate();
        Long endDate = dto.getEndDate();
        String tenantId = userInfo.getTenantId();
        //获取数据权限
        String dataFilter = getDataScope(tenantId, userInfo.getUserId(), userInfo.getStaffId(), dto.getShiftGroupIds());
        //按照班组筛选条件
        List<Long> shiftGroupIds = dto.getShiftGroupIds();
        List<Long> empIds = sysEmpInfoDo.getEmpList(tenantId, startDate, endDate, dto.getKeywords(), shiftGroupIds, null, dataFilter);
        // 过滤基地权限： 列表字段产品>产品管理页面所属团队>组织
        // 取交集
        List<Long> authedProductEmps = orgDataScopeService.getOrgScopeEmpIds(OrgDataScopeEnum.EMP_SCHEDULE_LIST, startDate, endDate, dto.getKeywords(), userInfo);
        if (CollectionUtils.isEmpty(authedProductEmps)) {
            return new PageResult<>(new ArrayList<>(), dto.getPageNo(), dto.getPageSize(), 0);
        }
        empIds = empIds.stream().filter(authedProductEmps::contains).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(empIds)) {
            return new PageResult<>(new ArrayList<>(), dto.getPageNo(), dto.getPageSize(), 0);
        }
        //特殊日期
        List<WfmCalendarDateDto> specialDate = wfmHolidayService.getCalendarDateList(tenantId, startDate, endDate);
        //解析排班年月按年分组
        Map<Short, List<Short>> yearAndMonthMap = getYearAndMonthMap(startDate, endDate, true);
        //排班草稿
        List<WaEmpScheduleDraftDo> scheduleDrafts = Lists.newArrayList();
        List<WaEmpShiftGroupDo> empShiftGroups = Lists.newArrayList();
        List<List<Long>> empIdList = ListTool.split(empIds, 500);
        for (List<Long> rows : empIdList) {
            if (CollectionUtils.isEmpty(rows)) {
                continue;
            }
            for (Map.Entry<Short, List<Short>> entry : yearAndMonthMap.entrySet()) {
                Short year = entry.getKey();
                List<Short> months = entry.getValue();
                scheduleDrafts.addAll(waEmpScheduleDraftDo.getEmpSchedules(tenantId, Collections.singletonList(year), months, rows));
            }
            empShiftGroups.addAll(waEmpShiftGroupDo.getEmShiftGroupByDateRange(tenantId, rows, startDate, endDate, shiftGroupIds, dataFilter));
        }
        List<EmpSchedulePageDto> items = Lists.newArrayList();
        //查询租户下所有班次
        List<MultiShiftSimpleVo> shifts = wfmShiftService.getAllShiftDefList(tenantId, startDate, endDate);
        Map<Long, MultiShiftSimpleVo> workTotalTimeMap = shifts.stream().collect(Collectors.toMap(MultiShiftSimpleVo::getShiftDefId, Function.identity(), (v1, v2) -> v2));
        //班组按员工分组
        Map<Long, List<WaEmpShiftGroupDo>> empShiftGroupMap = empShiftGroups.stream().collect(Collectors.groupingBy(WaEmpShiftGroupDo::getEmpId));
        //草稿按员工分组
        Map<Long, List<WaEmpScheduleDraftDo>> scheduleDraftMap = scheduleDrafts.stream().collect(Collectors.groupingBy(WaEmpScheduleDraftDo::getEmpId));
        //工序
        Map<Long, EmpWorkInfoProcess> empInfoMap = getEmpInfoMap(tenantId, empIds, dto.getProcessIds());
        //是否签到过
        Map<String, Boolean> registerRecordProcessMap = getRegisterRecordProcessMap();
        List<Long> workDates = getDays(startDate, endDate);
        for (Map.Entry<Long, EmpWorkInfoProcess> entry : empInfoMap.entrySet()) {
            Long empId = entry.getKey();
            EmpSchedulePageDto empSchedulePageDto = null;
            if (scheduleDraftMap.containsKey(empId)) {
                List<WaEmpScheduleDraftDo> empScheduleDrafts = scheduleDraftMap.get(empId);
                List<EmpScheduleItemVo> empScheduleMonth = Lists.newArrayList();
                List<EmpScheduleItemVo> empSchedulePublishedMonth = Lists.newArrayList();
                for (WaEmpScheduleDraftDo empScheduleDraft : empScheduleDrafts) {
                    if (null == empSchedulePageDto) {
                        empSchedulePageDto = ObjectConverter.convert(empScheduleDraft, EmpSchedulePageDto.class);
                    }
                    empScheduleMonth.addAll(getScheduleMonthVo(empScheduleDraft.getWorkTime()));
                    empSchedulePublishedMonth.addAll(getScheduleMonthVo(empScheduleDraft.getOldWorkTime()));
                }
                if (null != empSchedulePageDto) {
                    //特殊日期标识
                    empSchedulePageDto.setSpecialDate(specialDate);
                    //处理员工班组，以及班组有效时间范围内可编辑日期
                    this.handleDataRange(userInfo.getStaffId(), empId, startDate, endDate, empShiftGroupMap, empSchedulePageDto);
                    //筛选时间范围内的员工排班草稿
                    Map<Long, EmpScheduleItemVo> draftMap = empScheduleMonth.stream().filter(s -> s.getDate() >= startDate && s.getDate() <= endDate)
                            .collect(Collectors.toMap(EmpScheduleItemVo::getDate, Function.identity(), (v1, v2) -> v2));
                    //筛选时间范围内的员工排班（已发布）
                    Map<Long, EmpScheduleItemVo> publishedMap = empSchedulePublishedMonth.stream().filter(s -> s.getDate() >= startDate && s.getDate() <= endDate)
                            .collect(Collectors.toMap(EmpScheduleItemVo::getDate, Function.identity(), (v1, v2) -> v2));
                    //排班工时
                    int workTotalTime = 0;
                    List<EmpScheduleItemVo> monthSchedules = Lists.newArrayList();
                    //遍历日期
                    for (Long workDate : workDates) {
                        //具体每一天的排班
                        List<ScheduleItemVo> schedules = Lists.newArrayList();
                        if (draftMap.containsKey(workDate)) {
                            EmpScheduleItemVo empScheduleItemVo = draftMap.get(workDate);
                            if (CollectionUtils.isNotEmpty(empScheduleItemVo.getSchedules())) {
                                schedules.addAll(empScheduleItemVo.getSchedules());
                            }
                        }
                        List<ScheduleItemVo> publishedSchedules = Lists.newArrayList();
                        if (publishedMap.containsKey(workDate)) {
                            EmpScheduleItemVo empScheduleItemVo = publishedMap.get(workDate);
                            if (CollectionUtils.isNotEmpty(empScheduleItemVo.getSchedules())) {
                                schedules.addAll(empScheduleItemVo.getSchedules());
                                publishedSchedules.addAll(empScheduleItemVo.getSchedules());
                            }
                        }
                        schedules = distinctByKey(schedules, s -> String.format("%s_%s_%s_%s", s.getShiftId(), s.getSalaryProcessId(), s.getProductId(), s.getLeafNumberId()));
                        List<Long> dayShiftIds = schedules.stream().map(ScheduleItemVo::getShiftId).distinct().collect(Collectors.toList());
                        workTotalTime += shifts.stream().filter(shift -> dayShiftIds.contains(shift.getShiftDefId())).map(MultiShiftSimpleVo::getWorkTotalTime).filter(Objects::nonNull).reduce(0, Integer::sum);
                        List<ScheduleItemVo> actualSchedules = Lists.newArrayList();
                        for (ScheduleItemVo schedule : schedules) {
                            boolean published = publishedSchedules.stream().anyMatch(s -> s.getShiftId() != null && s.getShiftId().equals(schedule.getShiftId()));
                            schedule.setDataType(published ? ScheduleDataTypeEnum.PUBLISHED : ScheduleDataTypeEnum.DRAFT);
                            if (workTotalTimeMap.containsKey(schedule.getShiftId())) {
                                MultiShiftSimpleVo shift = workTotalTimeMap.get(schedule.getShiftId());
                                schedule.setTemporary(shift.isTemporary());
                                schedule.setIsNight(shift.getIsNight());
                                schedule.setShiftName(shift.getShiftDefName());
                                schedule.setShiftTimes(shift.getMultiWorkTimes());
                                schedule.setWorkTotalTime(shift.getWorkTotalTime());
                                schedule.setStartTime(shift.getStartTime());
                                schedule.setEndTime(shift.getEndTime());
                                schedule.setStartTimeBelong(shift.getStartTimeBelong());
                                schedule.setEndTimeBelong(shift.getEndTimeBelong());
                                schedule.setDateType(shift.getDateType());
                                actualSchedules.add(schedule);
                            }
                            schedule.setNotEditable(checkIfEdit(workDate, schedule, registerRecordProcessMap));
                        }
                        monthSchedules.add(new EmpScheduleItemVo(workDate, actualSchedules));
                    }
                    empSchedulePageDto.setScheduleWorkTime(BigDecimal.valueOf(workTotalTime).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN).floatValue());
                    empSchedulePageDto.setSchedules(monthSchedules.stream().sorted(Comparator.comparing(EmpScheduleItemVo::getDate)).collect(Collectors.toList()));
                }
            } else {
                empSchedulePageDto = new EmpSchedulePageDto();
                empSchedulePageDto.setEmpId(empId);
                empSchedulePageDto.setScheduleWorkTime(0f);
                empSchedulePageDto.setSchedules(getSchedules(workDates));
                //特殊日期标识
                empSchedulePageDto.setSpecialDate(specialDate);
                //处理员工班组，以及班组有效时间范围内可编辑日期
                this.handleDataRange(userInfo.getStaffId(), empId, startDate, endDate, empShiftGroupMap, empSchedulePageDto);
            }
            if (null != empSchedulePageDto) {
                EmpWorkInfoProcess empInfo = entry.getValue();
                empSchedulePageDto.setWorkNo(empInfo.getWorkno());
                empSchedulePageDto.setEmpName(empInfo.getName());
                empSchedulePageDto.setOrganize(empInfo.getOrganize());
                empSchedulePageDto.setOrganizeTxt(empInfo.getOrganizeTxt());
                empSchedulePageDto.setPost(empInfo.getPost());
                empSchedulePageDto.setPostTxt(empInfo.getPostTxt());
                empSchedulePageDto.setWorkshopSection(empInfo.getWorkshopSection());
                empSchedulePageDto.setWorkingProcess(empInfo.getWorkingProcess());
                empSchedulePageDto.setWorkingProcessName(empInfo.getWorkingProcessName());
                items.add(empSchedulePageDto);
            }
        }
        int total = items.size();
        int pageNo = Optional.of(dto.getPageNo()).orElse(1);
        int pageSize = Optional.of(dto.getPageSize()).orElse(10);
        items = items.stream().skip(((long) pageNo - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        return new PageResult<>(items, pageNo, pageSize, total);
    }

    private List<Long> getDays(Long startDate, Long endDate) {
        startDate = DateUtil.getOnlyDate(new Date(startDate * 1000));
        endDate = DateUtil.getOnlyDate(new Date(endDate * 1000));
        List<Long> list = Lists.newArrayList();
        while (startDate <= endDate) {
            list.add(startDate);
            startDate += 86400;
        }
        return list;
    }

    private List<EmpScheduleItemVo> getSchedules(List<Long> days) {
        List<EmpScheduleItemVo> list = Lists.newArrayList();
        for (Long day : days) {
            list.add(new EmpScheduleItemVo(day, new ArrayList<>()));
        }
        return list;
    }

    private List<EmpWorkInfo> getEmpInfo(String tenantId, List<Long> empIds, List<Long> processIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return Collections.emptyList();
        }
        DataFilter filter = DataFilter.eq("tenantId", tenantId).andIn("empId", empIds.stream().map(String::valueOf).collect(Collectors.toList())).andNe("deleted", Boolean.TRUE.toString());
        if (CollectionUtils.isNotEmpty(processIds)) {
            List<String> process = processIds.stream().filter(Objects::nonNull).map(String::valueOf).distinct().collect(Collectors.toList());
            filter = filter.andIn("workingProcess", process);
        }
        List<EmpWorkInfo> empList = Lists.newArrayList();
        try {
            empList = DataQuery.identifier(EMP_WORK_INFO_IDENTIFIER).decrypt().dept().specifyLanguage()
                    .queryInvisible().limit(-1, 1).exp()
                    .filter(filter, EmpWorkInfo.class, System.currentTimeMillis())
                    .getItems();
        } catch (Exception e) {
            log.error("Query {} exception:{}", EMP_WORK_INFO_IDENTIFIER, e.getMessage(), e);
        }
        return empList;
    }

    private Map<Long, EmpWorkInfoProcess> getEmpInfoMap(String tenantId, List<Long> empIds, List<Long> processIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return Collections.emptyMap();
        }
        List<EmpWorkInfo> empList = Lists.newArrayList();
        List<List<Long>> empIdList = ListTool.split(empIds, 500);
        for (List<Long> rows : empIdList) {
            if (rows.isEmpty()) {
                continue;
            }
            empList.addAll(getEmpInfo(tenantId, rows, processIds));
        }
        List<String> workingProcess = empList.stream().map(EmpWorkInfo::getWorkingProcess).filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, WfmProcessInfo> workingProcessMap = getWorkingProcessMap(tenantId, workingProcess);
        return empList.stream().map(emp -> {
            EmpWorkInfoProcess empWorkInfo = ObjectConverter.convert(emp, EmpWorkInfoProcess.class);
            Optional<WfmProcessInfo> workingProcessOpt = Optional.ofNullable(workingProcessMap.get(empWorkInfo.getWorkingProcess()));
            if (workingProcessOpt.isPresent()) {
                WfmProcessInfo processInfo = workingProcessOpt.get();
                empWorkInfo.setWorkingProcess(processInfo.getBid());
                empWorkInfo.setWorkingProcessName(processInfo.getProcessName());
            }
            return empWorkInfo;
        }).collect(Collectors.toMap(emp -> Long.valueOf(emp.getEmpId()), Function.identity(), (v1, v2) -> v2));
    }

    private Map<String, WfmProcessInfo> getWorkingProcessMap(String tenantId, List<String> workingProcess) {
        if (CollectionUtils.isEmpty(workingProcess)) {
            return new HashMap<>();
        }
        DataFilter processFilter = DataFilter.eq("tenantId", tenantId).andIn("bid", workingProcess);
        try {
            SecurityUserInfo securityUserInfo = new SecurityUserInfo();
            securityUserInfo.setTenantId(tenantId);
            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
            StopWatch stopWatch = new StopWatch("查询工序");
            stopWatch.start();
            List<WfmProcessInfo> workingProcessList = DataQuery.identifier(WORK_PROCESS_IDENTIFIER).decrypt().dept().specifyLanguage()
                    .queryInvisible().limit(-1, 1).exp()
                    .filter(processFilter, WfmProcessInfo.class, System.currentTimeMillis())
                    .getItems();
            if (null == workingProcessList) {
                workingProcessList = Lists.newArrayList();
            }
            stopWatch.stop();
            log.info("查询工序时长:{}", stopWatch.prettyPrint());
            Map<String, WfmProcessInfo> processInfoMap = workingProcessList.stream().collect(Collectors.toMap(WfmProcessInfo::getBid, Function.identity()));
            return processInfoMap;
        } catch (Exception e) {
            log.error("Query {} exception:{}", WORK_PROCESS_IDENTIFIER, e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
        return new HashMap<>();
    }

    public String getDataScope(String tenantId, Long userId, Long empId, List<Long> shiftGroupIds) {
        return cacheCommonService.getShiftGroupDataScope(tenantId, userId, "EMP_SCHEDULE_LIST", empId, shiftGroupIds);
    }

    private void handleDataRange(Long empId, Long staffId, Long startDate, Long endDate, Map<Long, List<WaEmpShiftGroupDo>> empShiftGroupMap, EmpSchedulePageDto empSchedulePageDto) {
        if (null == empShiftGroupMap || empShiftGroupMap.isEmpty()) {
            return;
        }
        //查询时间范围内员工生效中的班组
        if (empShiftGroupMap.containsKey(staffId) && CollectionUtils.isNotEmpty(empShiftGroupMap.get(staffId))) {
            List<WaEmpShiftGroupDo> empShiftGroupList = empShiftGroupMap.get(staffId);
            empShiftGroupList.forEach(shiftGroup -> shiftGroup.setShiftGroupName(LangParseUtil.getI18nLanguage(shiftGroup.getI18nShiftGroupName(), shiftGroup.getShiftGroupName())));
            //员工班组
            empSchedulePageDto.setShiftGroups(ObjectConverter.convertList(empShiftGroupList, ShiftGroupDto.class));
            if (CollectionUtils.isNotEmpty(empSchedulePageDto.getShiftGroups())) {
                empSchedulePageDto.setShiftGroups(new ArrayList<>(empSchedulePageDto.getShiftGroups()
                        .stream().collect(Collectors.toMap(ShiftGroupDto::getShiftGroupId, Function.identity(), (existing, replacement) -> existing))
                        .values()));
            }
            long minDate = startDate;
            long maxDate = endDate;
            List<Long> nonEditableDates = Lists.newArrayList();
            while (minDate <= maxDate) {
                long finalMinDate = minDate;
                //当前用户非班组长且日期不在有效班组时间范围内，则当日不可编辑
                boolean nonEditableDate = empShiftGroupList.stream()
                        .anyMatch(group -> Arrays.stream(group.getShiftGroupAdmins().split(","))
                                .map(Long::parseLong).collect(Collectors.toList())
                                .contains(empId) && group.getStartDate() <= finalMinDate && group.getEndDate() >= finalMinDate);
                if (!nonEditableDate) {
                    nonEditableDates.add(minDate);
                }
                minDate += 86400;
            }
            empSchedulePageDto.setNonEditableDate(nonEditableDates);
        }
    }

    @Override
    public void saveEmpScheduleDraft(EmpScheduleDraftDto dto, UserInfo userInfo) throws Exception {
        String tenantId = userInfo.getTenantId();
        Long startDate = dto.getStartDate();
        Long endDate = dto.getEndDate();
        //解析排班年月按年分组
        Map<Short, List<Short>> yearAndMonthMap = getYearAndMonthMap(startDate, endDate, true);
        //待排班草稿数据
        List<EmpScheduleDto> empScheduleDtoList = dto.getEmpSchedules();
        if (CollectionUtils.isEmpty(empScheduleDtoList)) {
            return;
        }
        empScheduleDtoList = distinctByKey(empScheduleDtoList, s -> String.valueOf(s.getEmpId()));
        List<WaEmpScheduleDo> empScheduledList = Lists.newArrayList();
        List<WaEmpScheduleDraftDo> empScheduledDraftList = Lists.newArrayList();
        //排班员工
        List<Long> empIds = empScheduleDtoList.stream().map(EmpScheduleDto::getEmpId).distinct().collect(Collectors.toList());
        //分批查询已排班草稿数据
        List<List<Long>> empIdList = ListTool.split(empIds, 500);
        for (List<Long> ids : empIdList) {
            if (!CollectionUtils.isNotEmpty(ids)) {
                continue;
            }
            for (Map.Entry<Short, List<Short>> entry : yearAndMonthMap.entrySet()) {
                Short year = entry.getKey();
                List<Short> months = entry.getValue();
                querySchedules(tenantId, Collections.singletonList(year), months, ids, empScheduledList, empScheduledDraftList);
            }
        }
        Short minYear = getYear(startDate, true);
        Short minMonth = getMonth(startDate, true);
        Short maxYear = getYear(endDate, true);
        Short maxMonth = getMonth(endDate, true);
        List<WaEmpScheduleDraftDo> updSchedules = Lists.newArrayList();
        Map<String, WaEmpScheduleDo> publishedScheduleMap = empScheduledList.stream()
                .collect(Collectors.toMap(s -> String.format("%s_%s_%s", s.getEmpId(), s.getYear(), s.getMonth()), Function.identity(), (v1, v2) -> v2));
        Map<String, WaEmpScheduleDraftDo> processedMap = new HashMap<>();
        //已排班草稿数据循环
        for (WaEmpScheduleDraftDo empScheduleDraft : empScheduledDraftList) {
            //员工按月已排班草稿数据
            List<EmpScheduleItemDto> empScheduleMonth = getScheduleMonth(empScheduleDraft.getWorkTime());
            Short year = empScheduleDraft.getYear();
            Short month = empScheduleDraft.getMonth();
            //构造每月的时间范围
            Long[] monthRange = calculateMonthRange(year, month, startDate, endDate, minYear, minMonth, maxYear, maxMonth);
            Long monthFirstDay = monthRange[0];
            Long monthLastDay = monthRange[1];
            Optional<EmpScheduleDto> opt = empScheduleDtoList.stream().filter(s -> s.getEmpId().equals(empScheduleDraft.getEmpId())).findFirst();
            if (!opt.isPresent()) {
                continue;
            }
            EmpScheduleDto empSchedule = opt.get();
            //每个员工的所有待排班次
            List<EmpScheduleItemDto> scheduleItemDtoList = empSchedule.getSchedules();
            if (CollectionUtils.isEmpty(scheduleItemDtoList)) {//待排班次为空
                continue;
            }
            if (null == empSchedule.getEmpId()) {
                continue;
            }
            //因是按月保存排班，故需按月进行筛选排班
            List<EmpScheduleItemDto> monthScheduleItemDtoList = scheduleItemDtoList.stream().filter(schedule -> schedule.getDate() != null && schedule.getDate() >= monthFirstDay && schedule.getDate() <= monthLastDay)
                    .sorted(Comparator.comparing(EmpScheduleItemDto::getDate)).collect(Collectors.toList());
            empScheduleMonth = processEmpScheduleMonth(empScheduleMonth, monthScheduleItemDtoList);
            empScheduleDraft.setWorkTime(JSON.toJSONString(empScheduleMonth));
            empScheduleDraft.setUpdater(userInfo.getUserId());
            empScheduleDraft.setUpdateTime(DateUtil.getCurrentTime(true));
            processUpdSchedules(empScheduleDraft, publishedScheduleMap, processedMap, updSchedules, userInfo);
            empSchedule.getSchedules().removeAll(monthScheduleItemDtoList);
        }
        //未排班数据处理
        List<WaEmpScheduleDraftDo> addSchedules = Lists.newArrayList();
        for (EmpScheduleDto empSchedule : empScheduleDtoList) {
            //每个员工的所有待排班次
            List<EmpScheduleItemDto> scheduleItemDtoList = empSchedule.getSchedules();
            if (CollectionUtils.isEmpty(scheduleItemDtoList)) {//待排班次为空
                continue;
            }
            for (Map.Entry<Short, List<Short>> entry : yearAndMonthMap.entrySet()) {
                Short year = entry.getKey();
                for (Short month : entry.getValue()) {
                    //构造每月的时间范围
                    Long[] monthRange = calculateMonthRange(year, month, startDate, endDate, minYear, minMonth, maxYear, maxMonth);
                    //因是按月保存排班，故需按月进行筛选排班
                    processAddSchedules(empSchedule, year, month, monthRange[0], monthRange[1], scheduleItemDtoList, publishedScheduleMap, addSchedules, userInfo);
                }
            }
        }
        updSchedules = distinctByKey(updSchedules, s -> String.format("%s_%s_%s", s.getEmpId(), s.getYear(), s.getMonth()));
        if (CollectionUtils.isNotEmpty(updSchedules)) {
            waEmpScheduleDraftDo.batchUpdate(tenantId, updSchedules);
        }
        addSchedules = distinctByKey(addSchedules, s -> String.format("%s_%s_%s", s.getEmpId(), s.getYear(), s.getMonth()));
        if (CollectionUtils.isNotEmpty(addSchedules)) {
            waEmpScheduleDraftDo.batchSave(tenantId, addSchedules);
        }
    }

    private void processAddSchedules(EmpScheduleDto empSchedule, Short year, Short month, Long monthFirstDay, Long monthLastDay, List<EmpScheduleItemDto> scheduleItemDtoList, Map<String, WaEmpScheduleDo> publishedScheduleMap, List<WaEmpScheduleDraftDo> addSchedules, UserInfo userInfo) {
        List<EmpScheduleItemDto> monthScheduleItemDtoList = scheduleItemDtoList.stream()
                .filter(schedule -> schedule.getDate() != null && schedule.getDate() >= monthFirstDay && schedule.getDate() <= monthLastDay)
                .sorted(Comparator.comparing(EmpScheduleItemDto::getDate))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(monthScheduleItemDtoList)) {
            return;
        }
        WaEmpScheduleDraftDo empScheduleDraft = getWaEmpScheduleDraft(year, month, userInfo, empSchedule, monthScheduleItemDtoList);
        String scheduleKey = String.format("%s_%s_%s", empSchedule.getEmpId(), year, month);
        if (publishedScheduleMap.containsKey(scheduleKey)) {
            empScheduleDraft.setOldWorkTime(publishedScheduleMap.get(scheduleKey).getWorkTime());
        }
        addSchedules.add(empScheduleDraft);
    }

    private void processUpdSchedules(WaEmpScheduleDraftDo empScheduleDraft, Map<String, WaEmpScheduleDo> publishedScheduleMap, Map<String, WaEmpScheduleDraftDo> processedMap, List<WaEmpScheduleDraftDo> updSchedules, UserInfo userInfo) {
        String scheduleKey = String.format("%s_%s_%s", empScheduleDraft.getEmpId(), empScheduleDraft.getYear(), empScheduleDraft.getMonth());
        if (publishedScheduleMap.containsKey(scheduleKey)) {
            empScheduleDraft.setOldWorkTime(publishedScheduleMap.get(scheduleKey).getWorkTime());
        }
        if (!processedMap.containsKey(scheduleKey)) {
            processedMap.put(scheduleKey, empScheduleDraft);
            updSchedules.add(empScheduleDraft);
        }
    }

    private List<EmpScheduleItemDto> processEmpScheduleMonth(List<EmpScheduleItemDto> empScheduleMonth, List<EmpScheduleItemDto> monthScheduleItemDtoList) {
        //符合条件的新排班放到已排班草稿数据
        empScheduleMonth.addAll(monthScheduleItemDtoList);
        //去重复
        return distinctByKey(empScheduleMonth, s -> String.valueOf(s.getDate()));
    }

    private void querySchedules(String tenantId, List<Short> years, List<Short> months, List<Long> empIds, List<WaEmpScheduleDo> empScheduledList, List<WaEmpScheduleDraftDo> empScheduledDraftList) {
        empScheduledList.addAll(waEmpScheduleDo.getEmpSchedules(tenantId, years, months, empIds));
        empScheduledDraftList.addAll(waEmpScheduleDraftDo.getEmpSchedules(tenantId, years, months, empIds));
    }

    private Long[] calculateMonthRange(Short year, Short month, Long startDate, Long endDate, Short minYear, Short minMonth, Short maxYear, Short maxMonth) throws ParseException {
        Long monthFirstDay = DateUtil.getTimeStampByYearMonthDay(Integer.valueOf(year), Integer.valueOf(month), 1);
        if (minYear.equals(year) && minMonth.equals(month)) {
            monthFirstDay = Math.max(startDate, monthFirstDay);
        }
        Long monthLastDay = DateUtil.getOnlyDate(new Date(DateUtil.getMonthEnd(monthFirstDay) * 1000));
        if (maxYear.equals(year) && maxMonth.equals(month)) {
            monthLastDay = Math.min(endDate, monthLastDay);
        }
        return new Long[]{monthFirstDay, monthLastDay};
    }

    private <T> List<T> distinctByKey(List<T> list, Function<T, String> keyExtractor) {
        return list.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(keyExtractor, Function.identity(), (v1, v2) -> v2),
                        map -> new ArrayList<>(map.values())
                ));
    }

    private WaEmpScheduleDraftDo getWaEmpScheduleDraft(Short year, Short month, UserInfo userInfo, EmpScheduleDto empSchedule, List<EmpScheduleItemDto> scheduleItemDtoList) {
        WaEmpScheduleDraftDo empScheduleDraft = new WaEmpScheduleDraftDo();
        empScheduleDraft.setScheduleDetailId(snowflakeUtil.createId());
        empScheduleDraft.setTenantId(userInfo.getTenantId());
        empScheduleDraft.setEmpId(empSchedule.getEmpId());
        empScheduleDraft.setWorkNo(empSchedule.getWorkNo());
        empScheduleDraft.setEmpName(empSchedule.getEmpName());
        empScheduleDraft.setYear(year);
        empScheduleDraft.setMonth(month);
        empScheduleDraft.setWorkTime(JSON.toJSONString(scheduleItemDtoList));
        empScheduleDraft.setDeleted(0);
        empScheduleDraft.setCreator(userInfo.getUserId());
        empScheduleDraft.setCreateTime(DateUtil.getCurrentTime(true));
        return empScheduleDraft;
    }

    private Short getYear(Long dateTime, Boolean isUnix) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(isUnix ? dateTime * 1000 : dateTime);
        return (short) calendar.get(Calendar.YEAR);
    }

    private Short getMonth(Long dateTime, Boolean isUnix) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(isUnix ? dateTime * 1000 : dateTime);
        return (short) (calendar.get(Calendar.MONTH) + 1);
    }

    @Override
    @Transactional
    public void publishEmpSchedules(EmpSchedulePublishDto dto, UserInfo userInfo) throws Exception {
        if (CollectionUtils.isNotEmpty(dto.getEmpSchedules())) {
            saveEmpScheduleDraft(dto, userInfo);
        }
        if (CollectionUtils.isNotEmpty(dto.getEmpIds())) {
            publishSchedules(dto, userInfo);
        }
    }

    public void publishSchedules(EmpSchedulePublishDto dto, UserInfo userInfo) throws Exception {
        Long startDate = dto.getStartDate();
        Long endDate = dto.getEndDate();
        String tenantId = userInfo.getTenantId();
        //解析排班年月按年分组
        Map<Short, List<Short>> yearAndMonthMap = getYearAndMonthMap(startDate, endDate, true);
        //待发布排班员工
        List<Long> empIds = dto.getEmpIds();
        List<WaEmpScheduleDo> empScheduledList = Lists.newArrayList();
        List<WaEmpScheduleDraftDo> empScheduledDraftList = Lists.newArrayList();
        //分批查询已发布排班数据
        List<List<Long>> empIdList = ListTool.split(empIds, 500);
        for (List<Long> ids : empIdList) {
            if (!CollectionUtils.isNotEmpty(ids)) {
                continue;
            }
            for (Map.Entry<Short, List<Short>> entry : yearAndMonthMap.entrySet()) {
                Short year = entry.getKey();
                List<Short> months = entry.getValue();
                querySchedules(tenantId, Collections.singletonList(year), months, ids, empScheduledList, empScheduledDraftList);
            }
        }
        Short minYear = getYear(startDate, true);
        Short minMonth = getMonth(startDate, true);
        Short maxYear = getYear(endDate, true);
        Short maxMonth = getMonth(endDate, true);
        //待更新已发布员工按月排班数据
        List<WaEmpScheduleDo> updSchedules = Lists.newArrayList();
        List<WaEmpScheduleDo> addSchedules = Lists.newArrayList();
        List<WaEmpScheduleDraftDo> updScheduleDrafts = Lists.newArrayList();
        //已排班草稿数据循环
        for (WaEmpScheduleDraftDo empScheduleDraft : empScheduledDraftList) {
            //员工按月已排班草稿数据
            List<EmpScheduleItemDto> empScheduleDraftMonth = getScheduleMonth(empScheduleDraft.getWorkTime());
            Long empId = empScheduleDraft.getEmpId();
            Short month = empScheduleDraft.getMonth();
            Short year = empScheduleDraft.getYear();
            //构造每月的时间范围
            Long[] monthRange = calculateMonthRange(year, month, startDate, endDate, minYear, minMonth, maxYear, maxMonth);
            Long monthFirstDay = monthRange[0];
            Long monthLastDay = monthRange[1];
            //已发布排班
            Optional<WaEmpScheduleDo> empScheduleOpt = empScheduledList.stream().filter(s -> s.getEmpId().equals(empId) && s.getYear().equals(year) && s.getMonth().equals(month)).findFirst();
            List<EmpScheduleItemDto> empScheduleDraftMonthFiltered = filterScheduleByMonthRange(empScheduleDraftMonth, monthFirstDay, monthLastDay);
            if (empScheduleOpt.isPresent()) {
                WaEmpScheduleDo empSchedulePublished = empScheduleOpt.get();
                processPublishedSchedule(empSchedulePublished, empScheduleDraftMonthFiltered, userInfo);
                updSchedules.add(empSchedulePublished);
                //草稿
                processDraftSchedule(empScheduleDraft, empScheduleDraftMonthFiltered, empSchedulePublished.getWorkTime(), userInfo);
                updScheduleDrafts.add(empScheduleDraft);
            } else if (CollectionUtils.isNotEmpty(empScheduleDraftMonthFiltered)) {
                processAddSchedule(empScheduleDraft, year, month, empScheduleDraftMonthFiltered, addSchedules, userInfo);
                updScheduleDrafts.add(empScheduleDraft);
            }
        }
        List<WaEmpScheduleDo> changeList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(addSchedules)) {
            waEmpScheduleDo.batchSave(tenantId, addSchedules);
            changeList.addAll(addSchedules);
        }
        if (CollectionUtils.isNotEmpty(updSchedules)) {
            waEmpScheduleDo.batchUpdate(tenantId, updSchedules);
            changeList.addAll(updSchedules);
        }
        if (CollectionUtils.isNotEmpty(updScheduleDrafts)) {
            waEmpScheduleDraftDo.batchUpdate(tenantId, updScheduleDrafts);
        }
        // 发布班次更新数据触发消息通知,需要增加历史排班数据
        if (CollectionUtils.isNotEmpty(changeList)) {
            waShiftNoticeService.sendShiftChangeNotice(changeList);
        }
    }

    private void processPublishedSchedule(WaEmpScheduleDo empSchedulePublished, List<EmpScheduleItemDto> empScheduleDraftMonthFiltered, UserInfo userInfo) {
        List<EmpScheduleItemDto> empSchedulePublishedMonth = getScheduleMonth(empSchedulePublished.getWorkTime());
        if (StringUtil.isNotBlank(empSchedulePublished.getWorkTime()) && !"[]".equals(empSchedulePublished.getWorkTime()) && StringUtil.isEmpty(empSchedulePublished.getPreWorkTime())) {
            empSchedulePublished.setPreWorkTime(empSchedulePublished.getWorkTime());
        }
        empSchedulePublishedMonth.addAll(empScheduleDraftMonthFiltered);
        empSchedulePublishedMonth = distinctByKey(empSchedulePublishedMonth, s -> String.valueOf(s.getDate()));
        empSchedulePublished.setWorkTime(JSON.toJSONString(empSchedulePublishedMonth));
        empSchedulePublished.setUpdater(userInfo.getUserId());
        empSchedulePublished.setUpdateTime(DateUtil.getCurrentTime(true));
    }

    private void processAddSchedule(WaEmpScheduleDraftDo empScheduleDraft, Short year, Short month, List<EmpScheduleItemDto> empScheduleDraftMonthFiltered, List<WaEmpScheduleDo> addSchedules, UserInfo userInfo) {
        WaEmpScheduleDo empSchedule = getWaEmpSchedule(year, month, userInfo, empScheduleDraft, empScheduleDraftMonthFiltered);
        addSchedules.add(empSchedule);
        processDraftSchedule(empScheduleDraft, empScheduleDraftMonthFiltered, JSON.toJSONString(empScheduleDraftMonthFiltered), userInfo);
    }

    private void processDraftSchedule(WaEmpScheduleDraftDo empScheduleDraft, List<EmpScheduleItemDto> empScheduleDraftMonthFiltered, String oldWorkTime, UserInfo userInfo) {
        List<EmpScheduleItemDto> empScheduleDraftMonth = getScheduleMonth(empScheduleDraft.getWorkTime());
        List<Long> dayTimes = empScheduleDraftMonthFiltered.stream().map(EmpScheduleItemDto::getDate).distinct().collect(Collectors.toList());
        empScheduleDraftMonth = empScheduleDraftMonth.stream().filter(draft -> !dayTimes.contains(draft.getDate())).collect(Collectors.toList());
        empScheduleDraft.setWorkTime(JSON.toJSONString(empScheduleDraftMonth));
        empScheduleDraft.setOldWorkTime(oldWorkTime);
        empScheduleDraft.setUpdater(userInfo.getUserId());
        empScheduleDraft.setUpdateTime(DateUtil.getCurrentTime(true));
    }

    private List<EmpScheduleItemDto> filterScheduleByMonthRange(List<EmpScheduleItemDto> schedules, Long monthFirstDay, Long monthLastDay) {
        return schedules.stream()
                .filter(schedule -> schedule.getDate() != null && schedule.getDate() >= monthFirstDay && schedule.getDate() <= monthLastDay)
                .sorted(Comparator.comparing(EmpScheduleItemDto::getDate))
                .collect(Collectors.toList());
    }

    /**
     * 班次对象
     *
     * @param year                年
     * @param month               月
     * @param userInfo            用户
     * @param empScheduleDraft    草稿
     * @param scheduleItemDtoList 排班班次
     * @return 排班班次对象
     */
    private WaEmpScheduleDo getWaEmpSchedule(Short year, Short month, UserInfo userInfo, WaEmpScheduleDraftDo empScheduleDraft, List<EmpScheduleItemDto> scheduleItemDtoList) {
        WaEmpScheduleDo empSchedule = new WaEmpScheduleDo();
        empSchedule.setScheduleDetailId(snowflakeUtil.createId());
        empSchedule.setTenantId(userInfo.getTenantId());
        empSchedule.setWorkNo(empScheduleDraft.getWorkNo());
        empSchedule.setEmpName(empScheduleDraft.getEmpName());
        empSchedule.setEmpId(empScheduleDraft.getEmpId());
        empSchedule.setMonth(month);
        empSchedule.setYear(year);
        empSchedule.setWorkTime(JSON.toJSONString(scheduleItemDtoList));
        empSchedule.setDeleted(0);
        empSchedule.setCreator(userInfo.getUserId());
        empSchedule.setCreateTime(DateUtil.getCurrentTime(true));
        return empSchedule;
    }

    @Override
    public Result<Boolean> checkShiftTimeOverlap(ShiftCheckDto dto) {
        List<Long> shiftIds = dto.getShiftIds();
        List<MultiShiftSimpleVo> shifts = getMultiShiftInfos(shiftIds);
        if (CollectionUtils.isEmpty(shifts)) {
            return ResponseWrap.wrapResult(AttendanceCodes.SHIFT_NOT_EXIST, Boolean.FALSE);
        }
        if (shiftIds.size() > shifts.size() && shifts.stream().allMatch(shift -> shiftIds.contains(shift.getShiftDefId()))) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_TIME_OVERLAP, null).getMsg());
        }
        long workDate = Optional.ofNullable(dto.getWorkDate()).orElse(DateUtil.getOnlyDate());
        String tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        Long empId = SecurityUserUtil.getSecurityUserInfo().getEmpId();
        // 检查当前班次之间的重叠
        String msg = checkCurrentShiftsOverlap(shifts, workDate);
        if (StringUtil.isNotBlank(msg)) {
            return Result.fail(msg);
        }
        // 前一天排班
        long preDay = DateUtil.addDate(workDate * 1000, -1);
        List<MultiShiftSimpleVo> preDaySchedules = ObjectConverter.convertList(getEmpWfmShiftInfos(tenantId, preDay, preDay, Collections.singletonList(empId), null, false), MultiShiftSimpleVo.class);
        // 检查前一天最后一个班次与当前班次的重叠
        if (!CollectionUtils.isEmpty(preDaySchedules)) {
            Optional<MultiShiftSimpleVo> lastPreDayShiftOpt = findShiftWithLatestEndTime(preDaySchedules);
            Optional<MultiShiftSimpleVo> firstCurrentShiftOpt = findShiftWithEarliestStartTime(shifts);
            if (lastPreDayShiftOpt.isPresent() && firstCurrentShiftOpt.isPresent()) {
                // 前一天
                MultiShiftSimpleVo lastPreDayShift = lastPreDayShiftOpt.get();
                // 当前
                MultiShiftSimpleVo firstCurrentShift = firstCurrentShiftOpt.get();
                long lastPreDayEndTime = getLatestEndTime(lastPreDayShift.getMultiWorkTimes(), preDay);
                long firstCurrentStartTime = getEarliestStartTime(firstCurrentShift.getMultiWorkTimes(), workDate);
                if (checkTimeRangeOverlap(lastPreDayEndTime, lastPreDayEndTime, firstCurrentStartTime, firstCurrentStartTime)) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_TIME_OVERLAP_BETWEEN, null).getMsg(),
                            DateUtil.getDateStrByTimesamp(preDay), lastPreDayShift.getShiftDefName(), DateUtil.getDateStrByTimesamp(workDate), firstCurrentShift.getShiftDefName()));
                }
            }
        }
        // 后一天排班
        long nextDay = DateUtil.addDate(workDate * 1000, 1);
        List<MultiShiftSimpleVo> nextDaySchedules = ObjectConverter.convertList(getEmpWfmShiftInfos(tenantId, nextDay, nextDay, Collections.singletonList(empId), null, false), MultiShiftSimpleVo.class);
        // 检查当前班次与后一天第一个班次的重叠
        if (!CollectionUtils.isEmpty(nextDaySchedules)) {
            // 后一天
            Optional<MultiShiftSimpleVo> firstNextDayShiftOpt = findShiftWithEarliestStartTime(nextDaySchedules);
            // 当前
            Optional<MultiShiftSimpleVo> lastCurrentShiftOpt = findShiftWithLatestEndTime(shifts);
            if (firstNextDayShiftOpt.isPresent() && lastCurrentShiftOpt.isPresent()) {
                MultiShiftSimpleVo firstNextDayShift = firstNextDayShiftOpt.get();
                MultiShiftSimpleVo lastCurrentShift = lastCurrentShiftOpt.get();
                long firstNextDayStartTime = getEarliestStartTime(firstNextDayShift.getMultiWorkTimes(), nextDay);
                long lastCurrentEndTime = getLatestEndTime(lastCurrentShift.getMultiWorkTimes(), workDate);
                if (checkTimeRangeOverlap(lastCurrentEndTime, lastCurrentEndTime, firstNextDayStartTime, firstNextDayStartTime)) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_TIME_OVERLAP_BETWEEN, null).getMsg(),
                            DateUtil.getDateStrByTimesamp(workDate), lastCurrentShift.getShiftDefName(), DateUtil.getDateStrByTimesamp(nextDay), firstNextDayShift.getShiftDefName()));
                }
            }
        }
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 检查当前班次之间的重叠
     *
     * @param shifts 班次
     * @param today  当前时间戳
     * @return String
     */
    private String checkCurrentShiftsOverlap(List<MultiShiftSimpleVo> shifts, long today) {
        for (int i = 0; i < shifts.size() - 1; i++) {
            MultiShiftSimpleVo start = shifts.get(i);
            long startMaxStartTime = getEarliestStartTime(start.getMultiWorkTimes(), today);
            long startMaxEndTime = getLatestEndTime(start.getMultiWorkTimes(), today);
            for (int j = i + 1; j < shifts.size(); j++) {
                MultiShiftSimpleVo end = shifts.get(j);
                long endMinStartTime = getEarliestStartTime(end.getMultiWorkTimes(), today);
                long endMinEndTime = getLatestEndTime(end.getMultiWorkTimes(), today);
                if (checkTimeRangeOverlap(startMaxStartTime, startMaxEndTime, endMinStartTime, endMinEndTime)) {
                    return String.format(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_TIME_OVERLAP_BETWEEN, null).getMsg(),
                            DateUtil.getDateStrByTimesamp(today), start.getShiftDefName(), DateUtil.getDateStrByTimesamp(today), end.getShiftDefName());
                }
            }
        }
        return "";
    }

    /**
     * 查找最早上班时间的班次
     *
     * @param shifts 班次
     * @return Optional<MultiShiftSimpleVo>
     */
    private Optional<MultiShiftSimpleVo> findShiftWithEarliestStartTime(List<MultiShiftSimpleVo> shifts) {
        return shifts.stream()
                .flatMap(shift -> shift.getMultiWorkTimes().stream()
                        .map(shiftSection -> new AbstractMap.SimpleEntry<>(shift, shiftSection.getRealStartTime())))
                .min(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey);
    }

    /**
     * 查找最晚下班时间的班次
     *
     * @param shifts 班次
     * @return Optional<MultiShiftSimpleVo>
     */
    private Optional<MultiShiftSimpleVo> findShiftWithLatestEndTime(List<MultiShiftSimpleVo> shifts) {
        return shifts.stream()
                .flatMap(shift -> shift.getMultiWorkTimes().stream()
                        .map(shiftSection -> new AbstractMap.SimpleEntry<>(shift, shiftSection.getRealEndTime())))
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey);
    }

    /**
     * 获取最早上班时间
     *
     * @param workTimes 班次时间段
     * @param baseDate  日期
     * @return long
     */
    private long getEarliestStartTime(List<MultiWorkTimeInfoSimpleVo> workTimes, long baseDate) {
        return workTimes.stream()
                .map(shift -> shift.getRealStartTime() * 60 + baseDate)
                .min(Long::compare)
                .orElse(0L);
    }

    /**
     * 获取最晚下班时间
     *
     * @param workTimes 班次时间段
     * @param baseDate  日期
     * @return long
     */
    private long getLatestEndTime(List<MultiWorkTimeInfoSimpleVo> workTimes, long baseDate) {
        return workTimes.stream()
                .map(shift -> shift.getRealEndTime() * 60 + baseDate)
                .max(Long::compare)
                .orElse(0L);
    }

    /**
     * 校验时间重叠
     *
     * @param start  开始时间
     * @param end    结束时间
     * @param start1 开始时间
     * @param end1   结束时间
     * @return 布尔值
     */
    private boolean checkTimeRangeOverlap(long start, long end, long start1, long end1) {
        return end > start1 && start < end1;
    }

    @Override
    public ShiftDetailDto getEmpShiftDetailInfo(ShiftDetailReqDto dto, UserInfo userInfo) {
        String tenantId = userInfo.getTenantId();
        Long workDate = dto.getWorkDate();
        Long empId = dto.getEmpId();
        Short year = getYear(workDate, true);
        Short month = getMonth(workDate, true);
        List<WaEmpScheduleDraftDo> drafts = waEmpScheduleDraftDo.getEmpSchedules(tenantId, Collections.singletonList(year), Collections.singletonList(month), Collections.singletonList(empId));
        if (CollectionUtils.isEmpty(drafts)) {
            log.error("没有排班记录1");
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, null).getMsg());
        }
        Long shiftId = dto.getShiftId();
        WaEmpScheduleDraftDo draft = drafts.get(0);
        Optional<ScheduleItemDto> draftShiftOpt = getShiftOptional(draft.getWorkTime(), workDate, shiftId);
        Optional<ScheduleItemDto> publishedShiftOpt = getShiftOptional(draft.getOldWorkTime(), workDate, shiftId);
        if (!draftShiftOpt.isPresent() && !publishedShiftOpt.isPresent()) {
            log.error("没有排班记录2");
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, null).getMsg());
        }
        ShiftDetailDto shiftDetailDto;
        if (draftShiftOpt.isPresent() && publishedShiftOpt.isPresent()) {
            shiftDetailDto = ObjectConverter.convert(draftShiftOpt.get(), ShiftDetailDto.class);
            shiftDetailDto.setDataType(ScheduleDataTypeEnum.PUBLISHED);
        } else if (draftShiftOpt.isPresent()) {
            shiftDetailDto = ObjectConverter.convert(draftShiftOpt.get(), ShiftDetailDto.class);
            shiftDetailDto.setDataType(ScheduleDataTypeEnum.DRAFT);
        } else {
            shiftDetailDto = ObjectConverter.convert(publishedShiftOpt.get(), ShiftDetailDto.class);
            shiftDetailDto.setDataType(ScheduleDataTypeEnum.PUBLISHED);
        }
        shiftDetailDto.setEmpId(empId);
        shiftDetailDto.setWorkDate(workDate);
        Optional<MultiShiftSimpleVo> multiShiftSimpleOpt = Optional.ofNullable(getMultiShiftInfo(shiftId));
        if (multiShiftSimpleOpt.isPresent()) {
            MultiShiftSimpleVo multiShiftSimpleVo = multiShiftSimpleOpt.get();
            List<MultiWorkTimeInfoSimpleVo> multiWorkTimes = multiShiftSimpleVo.getMultiWorkTimes();
            List<ShiftTimeDto> shiftTimes = Lists.newArrayList();
            for (MultiWorkTimeInfoSimpleVo multiWorkTime : multiWorkTimes) {
                shiftTimes.add(new ShiftTimeDto(workDate + multiWorkTime.getRealStartTime() * 60, workDate + multiWorkTime.getRealEndTime() * 60));
            }
            shiftDetailDto.setShiftTimes(shiftTimes);
        }
        return shiftDetailDto;
    }

    /**
     * 查询班次
     *
     * @param shiftDefId 班次
     * @return 班次信息
     */
    private MultiShiftSimpleVo getMultiShiftInfo(Long shiftDefId) {
        List<MultiShiftSimpleVo> shifts = getMultiShiftInfos(Collections.singletonList(shiftDefId));
        if (CollectionUtils.isEmpty(shifts)) {
            return null;
        }
        return shifts.get(0);
    }

    /**
     * 批量查询班次
     *
     * @param shiftDefIds 班次
     * @return 班次信息
     */
    @Override
    public List<MultiShiftSimpleVo> getMultiShiftInfos(List<Long> shiftDefIds) {
        if (CollectionUtils.isEmpty(shiftDefIds)) {
            return Lists.newArrayList();
        }
        return wfmShiftService.getListByIds(shiftDefIds);
    }

    private Optional<ScheduleItemDto> getShiftOptional(String workTime, Long workDate, Long shiftId) {
        List<EmpScheduleItemDto> shiftMonth = getScheduleMonth(workTime);
        return shiftMonth.stream().filter(s -> s.getDate().equals(workDate)).map(s -> {
            List<ScheduleItemDto> schedules = Optional.ofNullable(s.getSchedules()).orElse(new ArrayList<>());
            Optional<ScheduleItemDto> opt = schedules.stream().filter(shift -> shift.getShiftId().equals(shiftId)).findFirst();
            return opt.orElse(null);
        }).filter(Objects::nonNull).findFirst();
    }

    private List<EmpScheduleItemDto> getScheduleMonth(String workTime) {
        List<EmpScheduleItemDto> shiftMonth = Lists.newArrayList();
        if (StringUtil.isNotBlank(workTime) && !"[]".equals(workTime)) {
            shiftMonth = JSON.parseArray(workTime, EmpScheduleItemDto.class);
        }
        return shiftMonth;
    }

    private List<EmpScheduleItemVo> getScheduleMonthVo(String workTime) {
        List<EmpScheduleItemVo> shiftMonth = Lists.newArrayList();
        if (StringUtil.isNotBlank(workTime) && !"[]".equals(workTime)) {
            shiftMonth = JSON.parseArray(workTime, EmpScheduleItemVo.class);
        }
        return shiftMonth;
    }

    /**
     * 查询指定订单工序班次已排班员工
     *
     * @param workDate 排班日
     * @param process  工序订单班次
     * @param userInfo 用户
     * @return
     */
    @Override
    public List<ProcessEmpDto> getEmpInfoByProcess(Long workDate, List<ProcessEmpInfoItemDto> process, UserInfo userInfo) {
        if (CollectionUtils.isEmpty(process)) {
            return Lists.newArrayList();
        }
        String tenantId = userInfo.getTenantId();
        Short year = getYear(workDate, true);
        Short month = getMonth(workDate, true);
        List<WaEmpScheduleDo> empShifts = waEmpScheduleDo.getEmpSchedules(tenantId, Collections.singletonList(year), Collections.singletonList(month), null);
        List<String> keys = process.stream().map(p -> String.format("%s_%s_%s", p.getProcessId(), p.getOrderId(), p.getShiftId())).distinct().collect(Collectors.toList());
        Map<String, List<EmpInfoDTO>> processEmpMap = new HashMap<>();
        for (WaEmpScheduleDo empSchedule : empShifts) {
            List<EmpScheduleItemVo> schedules = getScheduleMonthVo(empSchedule.getWorkTime());
            List<ScheduleItemVo> items = schedules.stream().filter(schedule -> schedule.getDate() != null && schedule.getDate().equals(workDate))
                    .map(EmpScheduleItemVo::getSchedules).flatMap(List::stream).filter(shift -> keys.contains(String.format("%s_%s_%s", shift.getSalaryProcessId(), shift.getLeafNumberId(), shift.getShiftId()))).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(items)) {
                continue;
            }
            EmpInfoDTO empInfo = new EmpInfoDTO();
            empInfo.setEmpId(empSchedule.getEmpId());
            empInfo.setWorkno(empSchedule.getWorkNo());
            empInfo.setName(empSchedule.getEmpName());
            for (ScheduleItemVo item : items) {
                String key = String.format("%s_%s_%s", item.getSalaryProcessId(), item.getLeafNumberId(), item.getShiftId());
                List<EmpInfoDTO> list = new ArrayList<>();
                if (processEmpMap.containsKey(key)) {
                    list = processEmpMap.get(key);
                }
                list.add(empInfo);
                processEmpMap.put(key, list);
            }
        }
        List<ProcessEmpDto> list = Lists.newArrayList();
        for (Map.Entry<String, List<EmpInfoDTO>> entry : processEmpMap.entrySet()) {
            String[] arr = entry.getKey().split("_");
            Long processId = Long.valueOf(arr[0]);
            Long orderId = Long.valueOf(arr[1]);
            Long shiftId = Long.valueOf(arr[2]);
            list.add(new ProcessEmpDto(processId, orderId, shiftId, entry.getValue()));
        }
        return list;
    }

    /**
     * 获取指定日期范围工序下员工Id集合map
     *
     * @param startTime
     * @param endTime
     * @param tenantId
     * @return
     */
    public Map<String, Set<String>> getProcessEmpMap(Long startTime, Long endTime, List<Long> empIds, String tenantId) {
        Set<Short> monthSet = new LinkedHashSet<>();
        List<Short> years = new ArrayList<>();
        LocalDate start = Instant.ofEpochMilli(startTime).atZone(ZoneId.systemDefault()).toLocalDate().withDayOfMonth(1);
        LocalDate end = Instant.ofEpochMilli(endTime).atZone(ZoneId.systemDefault()).toLocalDate().withDayOfMonth(1);
        while (!start.isAfter(end)) {
            monthSet.add((short) start.getMonthValue());
            start = start.plusMonths(1);
        }
        int startYear = Instant.ofEpochMilli(startTime).atZone(ZoneId.systemDefault()).toLocalDate().getYear();
        int endYear = Instant.ofEpochMilli(endTime).atZone(ZoneId.systemDefault()).toLocalDate().getYear();
        for (int y = startYear; y <= endYear; y++) {
            years.add((short) y);
        }
        List<WaEmpScheduleDo> empSchedules = waEmpScheduleDo.getEmpSchedules(tenantId, years, new ArrayList<>(monthSet), null, empIds);
        Map<String, Set<String>> processEmpMap = new HashMap<>();
        for (WaEmpScheduleDo empSchedule : empSchedules) {
            List<EmpScheduleItemVo> schedules = getScheduleMonthVo(empSchedule.getWorkTime());
            for (EmpScheduleItemVo schedule : schedules) {
                if (schedule.getSchedules() != null && !schedule.getSchedules().isEmpty()) {
                    if (schedule.getDate() * 1000 < startTime || schedule.getDate() * 1000 > endTime)
                        continue;
                    for (ScheduleItemVo item : schedule.getSchedules()) {
                        String key = schedule.getDate() * 1000 + "_" + item.getLeafNumberId() + "_" + item.getSalaryProcessId() + "_" + item.getShiftId();
                        processEmpMap.computeIfAbsent(key, k -> new HashSet<>()).add(empSchedule.getEmpId().toString());
                    }
                }
            }
        }
        return processEmpMap;
    }

    /**
     * 查询员工当天的排班信息
     *
     * @param workDate 排班日
     * @param empId    员工
     * @param userInfo 当前用户
     * @return
     */
    @Override
    public List<ScheduleItemVo> getEmpWorkDateInfo(Long workDate, Long empId, UserInfo userInfo) {
        String tenantId = userInfo.getTenantId();
        Short year = getYear(workDate, true);
        Short month = getMonth(workDate, true);
        List<WaEmpScheduleDo> empShifts = waEmpScheduleDo.getEmpSchedules(tenantId, Collections.singletonList(year), Collections.singletonList(month), Collections.singletonList(empId));
        if (CollectionUtils.isEmpty(empShifts)) {
            return Lists.newArrayList();
        }
        WaEmpScheduleDo empShift = empShifts.get(0);
        List<EmpScheduleItemDto> schedules = getScheduleMonth(empShift.getWorkTime());
        List<ScheduleItemDto> shifts = schedules.stream().filter(schedule -> schedule.getDate() != null && schedule.getDate().equals(workDate)).map(EmpScheduleItemDto::getSchedules).flatMap(List::stream).collect(Collectors.toList());
        List<Long> shiftDefIds = shifts.stream().map(ScheduleItemDto::getShiftId).distinct().collect(Collectors.toList());
        List<MultiShiftSimpleVo> defShifts = getMultiShiftInfos(shiftDefIds);
        Map<Long, MultiShiftSimpleVo> defShiftMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(defShifts)) {
            defShiftMap.putAll(defShifts.stream().collect(Collectors.toMap(MultiShiftSimpleVo::getShiftDefId, Function.identity(), (v1, v2) -> v2)));
        }
        List<ScheduleItemVo> list = Lists.newArrayList();
        for (ScheduleItemDto shift : shifts) {
            if (defShiftMap.containsKey(shift.getShiftId())) {
                MultiShiftSimpleVo vo = defShiftMap.get(shift.getShiftId());
                ScheduleItemVo itemVo = ObjectConverter.convert(shift, ScheduleItemVo.class);
                itemVo.setShiftTimes(vo.getMultiWorkTimes());
                itemVo.setWorkTotalTime(vo.getWorkTotalTime());
                itemVo.setTemporary(vo.isTemporary());
                itemVo.setIsNight(vo.getIsNight());
                itemVo.setDataType(ScheduleDataTypeEnum.PUBLISHED);
                itemVo.setShiftName(vo.getShiftDefName());
                list.add(itemVo);
            }
        }
        return list;
    }

    /**
     * 查询员工排班
     *
     * @param tenantId  租户
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param empIds    员工
     * @return Map
     */
    @Override
    public Map<String, List<EmpMultiShiftInfoDto>> getEmpWfmShiftInfo(String tenantId, Long startDate, Long endDate, List<Long> empIds) {
        List<EmpMultiShiftInfoDto> empShifts = getEmpWfmShiftInfos(tenantId, startDate, endDate, empIds, null, false);
        if (CollectionUtils.isEmpty(empShifts)) {
            return new HashMap<>();
        }
        return empShifts.stream().collect(Collectors.groupingBy(shift -> String.format("%s_%s", shift.getEmpId(), shift.getWorkDate())));
    }

    /**
     * 查询员工排班
     *
     * @param tenantId  租户
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param empIds    员工
     * @return List
     */
    @Override
    public List<EmpMultiShiftInfoDto> getEmpWfmShiftInfos(String tenantId, Long startDate, Long endDate, List<Long> empIds, List<Long> orderIds, boolean actualShift) {
        //解析排班年月按年分组
        Map<Short, List<Short>> yearAndMonthMap = getYearAndMonthMap(startDate, endDate, true);
        //员工排班
        List<WaEmpScheduleDo> empMonthShifts = Lists.newArrayList();
        for (Map.Entry<Short, List<Short>> entry : yearAndMonthMap.entrySet()) {
            Short year = entry.getKey();
            List<Short> months = entry.getValue();
            empMonthShifts.addAll(waEmpScheduleDo.getEmpSchedules(tenantId, Collections.singletonList(year), months, null));
        }
        if (CollectionUtils.isNotEmpty(empIds)) {
            empMonthShifts = empMonthShifts.stream().filter(shift -> empIds.contains(shift.getEmpId())).collect(Collectors.toList());
        }
        List<MultiShiftSimpleVo> defShifts;
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            //查询租户下所有班次
            defShifts = wfmShiftService.getAllShiftDefList(tenantId, startDate, endDate);
        } catch (Exception e) {
            return Lists.newArrayList();
        } finally {
            UserContext.removeSecurityUserInfo();
        }
        Map<Long, MultiShiftSimpleVo> defShiftMap = defShifts.stream().collect(Collectors.toMap(MultiShiftSimpleVo::getShiftDefId, Function.identity(), (v1, v2) -> v2));
        List<EmpMultiShiftInfoDto> list = Lists.newArrayList();
        //班次变更
        Map<String, Long> shiftChangeMap = new HashMap<>();
        if (actualShift) {
            List<Long> empIdList = optimizeEmpIdList(empMonthShifts, empIds);
            shiftChangeMap = wfmShiftService.getShiftChangeMap(tenantId, startDate, endDate, empIdList);
        }
        for (WaEmpScheduleDo empShift : empMonthShifts) {
            Long empId = empShift.getEmpId();
            List<EmpScheduleItemVo> dayShifts = getScheduleMonthVo(empShift.getWorkTime());
            dayShifts = dayShifts.stream().filter(s -> s.getDate() >= startDate && s.getDate() <= endDate).collect(Collectors.toList());
            for (EmpScheduleItemVo dayShift : dayShifts) {
                List<ScheduleItemVo> shifts = dayShift.getSchedules();
                Long workDate = dayShift.getDate();
                if (CollectionUtils.isEmpty(shifts)) {
                    continue;
                }
                if (CollectionUtils.isNotEmpty(orderIds)) {
                    shifts = shifts.stream().filter(shift -> orderIds.contains(shift.getLeafNumberId())).collect(Collectors.toList());
                }
                for (ScheduleItemVo shift : shifts) {
                    Long shiftId = shift.getShiftId();
                    String processShiftKey = String.format("%s_%s_%s_%s_%s", empId, workDate, shift.getSalaryProcessId(), shift.getLeafNumberId(), shiftId);
                    if (shiftChangeMap.containsKey(processShiftKey)) {
                        shiftId = shiftChangeMap.get(processShiftKey);
                        shiftChangeMap.remove(processShiftKey);
                    }
                    if (!defShiftMap.containsKey(shiftId)) {
                        continue;
                    }
                    MultiShiftSimpleVo multiShift = defShiftMap.get(shiftId);
                    EmpMultiShiftInfoDto empMultiShiftInfoDto = ObjectConverter.convert(multiShift, EmpMultiShiftInfoDto.class);
                    empMultiShiftInfoDto.setLeafNumberId(shift.getLeafNumberId());
                    empMultiShiftInfoDto.setLeafNumber(shift.getLeafNumber());
                    empMultiShiftInfoDto.setProductId(shift.getProductId());
                    empMultiShiftInfoDto.setProductName(shift.getProductName());
                    empMultiShiftInfoDto.setProcessId(shift.getSalaryProcessId());
                    empMultiShiftInfoDto.setProcessName(shift.getSalaryProcessName());
                    empMultiShiftInfoDto.setEmpId(empId);
                    empMultiShiftInfoDto.setWorkDate(workDate);
                    list.add(empMultiShiftInfoDto);
                }
            }
        }
        // todo
        //list.addAll(getChangeShiftList(tenantId, shiftChangeMap, defShiftMap));
        return list;
    }

    private List<Long> optimizeEmpIdList(List<WaEmpScheduleDo> empMonthShifts, List<Long> empIds) {
        // 从empMonthShifts提取去重的empId流
        Stream<Long> monthShiftEmpIds = empMonthShifts == null ? Stream.empty() : empMonthShifts.stream().map(WaEmpScheduleDo::getEmpId).distinct();
        // 合并empIds流（去重）
        Stream<Long> empIdsStream = empIds == null ? Stream.empty() : empIds.stream().distinct();
        // 合并两个流并去重后收集为列表
        return Stream.concat(monthShiftEmpIds, empIdsStream).distinct().collect(Collectors.toList());
    }

    private class ShiftDataContext {
        private final Map<Long, WfmOrderInfo> orderMap;
        private final Map<String, WfmProcessInfo> processMap;
        private final Map<Long, WfmProductInfo> productMap;

        // 构造函数处理数据初始化
        public ShiftDataContext(String tenantId, Map<String, Long> shiftChangeMap) {
            // 提取ID列表
            List<String> processIds = new ArrayList<>();
            List<String> orderIds = new ArrayList<>();

            for (Map.Entry<String, Long> entry : shiftChangeMap.entrySet()) {
                String[] arr = entry.getKey().split("_");
                processIds.add(arr[2]);
                orderIds.add(arr[3]);
            }

            // 初始化映射
            this.orderMap = getWfmOrderMap(tenantId, orderIds);
            this.processMap = getWorkingProcessMap(tenantId, processIds);

            // 从工序中提取产品ID并去重
            List<String> productIds = this.processMap.values().stream().map(WfmProcessInfo::getProductId).distinct().collect(Collectors.toList());
            this.productMap = getWfmProductMap(tenantId, productIds);
        }

        public WfmProcessInfo getProcess(String processId) {
            return processMap.get(processId);
        }

        public String getOrderName(Long orderId) {
            if (null == orderId) {
                return "";
            }
            if (hasOrder(orderId)) {
                return orderMap.get(orderId).getBatchNumber();
            }
            return "";
        }

        public String getProcessName(String processId) {
            if (null == processId) {
                return "";
            }
            if (hasProcess(processId)) {
                return processMap.get(processId).getProcessName();
            }
            return "";
        }

        public String getProductName(Long productId) {
            if (null == productId) {
                return "";
            }
            if (hasProduct(productId)) {
                return productMap.get(productId).getProductName();
            }
            return "";
        }

        public boolean hasOrder(Long orderId) {
            return orderMap.containsKey(orderId);
        }

        public boolean hasProcess(String processId) {
            return processMap.containsKey(processId);
        }

        public boolean hasProduct(Long productId) {
            return productMap.containsKey(productId);
        }
    }


    private List<WfmOrderInfo> getWfmOrders(String tenantId, List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return new ArrayList<>();
        }
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId).andNe("deleted", Boolean.TRUE.toString());
        if (CollectionUtils.isNotEmpty(orderIds)) {
            dataFilter.andIn("bid", orderIds.stream().map(String::valueOf).collect(Collectors.toList()));
        }
        return getWfmOrdersByCondition(tenantId, dataFilter);
    }

    private Map<Long, WfmOrderInfo> getWfmOrderMap(String tenantId, List<String> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyMap();
        }
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId).andNe("deleted", Boolean.TRUE.toString());
        if (CollectionUtils.isNotEmpty(orderIds)) {
            dataFilter.andIn("bid", orderIds);
        }
        return getWfmOrdersByCondition(tenantId, dataFilter).stream().collect(Collectors.toMap(order -> Long.parseLong(order.getBid()), Function.identity(), (v1, v2) -> v1));
    }

    private List<WfmOrderInfo> getWfmOrdersByCondition(String tenantId, DataFilter dataFilter) {
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            StopWatch stopWatch = new StopWatch("查询订单");
            stopWatch.start();
            List<WfmOrderInfo> items = DataQuery.identifier(WFM_ORDER_IDENTIFIER).decrypt().specifyLanguage()
                    .queryInvisible().limit(-1, 1).filter(dataFilter, WfmOrderInfo.class).getItems();
            stopWatch.stop();
            log.info("查询订单时长:{}", stopWatch.prettyPrint());
            return items;
        } finally {
            UserContext.removeSecurityUserInfo();
        }
    }

    private Map<Long, WfmProductInfo> getWfmProductMap(String tenantId, List<String> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.emptyMap();
        }
        return getWfmProducts(tenantId, productIds.stream().map(Long::parseLong).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(product -> Long.parseLong(product.getBid()), Function.identity(), (existing, replacement) -> existing));
    }

    private List<WfmProductInfo> getWfmProducts(String tenantId, List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>();
        }
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId).andNe("deleted", Boolean.TRUE.toString());
        if (CollectionUtils.isNotEmpty(productIds)) {
            dataFilter.andIn("bid", productIds.stream().map(String::valueOf).collect(Collectors.toList()));
        }
        return getWfmProductsByCondition(tenantId, dataFilter);
    }

    private List<WfmProductInfo> getWfmProductsByCondition(String tenantId, DataFilter dataFilter) {
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            StopWatch stopWatch = new StopWatch("查询产品");
            stopWatch.start();
            List<WfmProductInfo> items = DataQuery.identifier(WFM_PRODUCT_IDENTIFIER).decrypt().specifyLanguage()
                    .queryInvisible().limit(-1, 1).filter(dataFilter, WfmProductInfo.class).getItems();
            stopWatch.stop();
            log.info("查询产品时长:{}", stopWatch.prettyPrint());
            return items;
        } finally {
            UserContext.removeSecurityUserInfo();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean replaceEmpShift(EmpShiftReplaceDto dto, UserInfo userInfo) throws Exception {
        if (CollectionUtils.isEmpty(dto.getSchedules())) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_EMPTY, null).getMsg());
        }
        Long workDate = Optional.ofNullable(dto.getWorkDate()).orElse(DateUtil.getOnlyDate());
        Short month = getMonth(workDate, true);
        Short year = getYear(workDate, true);
        String tenantId = userInfo.getTenantId();
        Long empId = userInfo.getStaffId();
        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(tenantId, empId);
        if (!Optional.ofNullable(empInfo).isPresent()) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_EXIST, null).getMsg());
        }
        List<ScheduleItemDto> schedules = dto.getSchedules();
        //查排班
        List<WaEmpScheduleDo> publishedAdd = Lists.newArrayList();
        List<WaEmpScheduleDo> publishedUpd = Lists.newArrayList();
        WaEmpScheduleDo monthPublished;
        List<WaEmpScheduleDo> publishedShifts = waEmpScheduleDo.getEmpSchedules(tenantId, Collections.singletonList(year), Collections.singletonList(month), Collections.singletonList(empId));
        if (CollectionUtils.isNotEmpty(publishedShifts)) {
            monthPublished = publishedShifts.get(0);
            List<EmpScheduleItemDto> workTimes = getScheduleMonth(monthPublished.getWorkTime());
            workTimes.add(new EmpScheduleItemDto(workDate, schedules));
            //去重,排序
            workTimes = workTimes.stream().collect(Collectors.collectingAndThen(Collectors.toMap(EmpScheduleItemDto::getDate, Function.identity(), (v1, v2) -> v2), map -> new ArrayList<>(map.values())))
                    .stream().sorted(Comparator.comparing(EmpScheduleItemDto::getDate)).collect(Collectors.toList());
            monthPublished.setWorkTime(JSON.toJSONString(workTimes));
            monthPublished.setUpdater(userInfo.getUserId());
            monthPublished.setUpdateTime(DateUtil.getCurrentTime(true));
            publishedUpd.add(monthPublished);
        } else {
            monthPublished = new WaEmpScheduleDo();
            monthPublished.setScheduleDetailId(snowflakeUtil.createId());
            monthPublished.setTenantId(tenantId);
            monthPublished.setWorkNo(empInfo.getWorkno());
            monthPublished.setEmpName(empInfo.getEmpName());
            monthPublished.setEmpId(empId);
            monthPublished.setMonth(month);
            monthPublished.setYear(year);
            monthPublished.setWorkTime(JSON.toJSONString(Collections.singletonList(new EmpScheduleItemDto(workDate, schedules))));
            monthPublished.setDeleted(0);
            monthPublished.setCreator(userInfo.getUserId());
            monthPublished.setCreateTime(DateUtil.getCurrentTime(true));
            publishedAdd.add(monthPublished);
        }
        List<WaEmpScheduleDraftDo> draftAdd = Lists.newArrayList();
        List<WaEmpScheduleDraftDo> draftUpd = Lists.newArrayList();
        List<WaEmpScheduleDraftDo> draftShifts = waEmpScheduleDraftDo.getEmpSchedules(tenantId, Collections.singletonList(year), Collections.singletonList(month), Collections.singletonList(empId));
        WaEmpScheduleDraftDo monthSchedule;
        if (CollectionUtils.isNotEmpty(draftShifts)) {
            monthSchedule = draftShifts.get(0);
            List<EmpScheduleItemDto> workTimes = getScheduleMonth(monthSchedule.getWorkTime());
            workTimes.add(new EmpScheduleItemDto(workDate, new ArrayList<>()));
            //去重 排序
            workTimes = workTimes.stream().collect(Collectors.collectingAndThen(Collectors.toMap(EmpScheduleItemDto::getDate, Function.identity(), (v1, v2) -> v2), map -> new ArrayList<>(map.values())))
                    .stream().sorted(Comparator.comparing(EmpScheduleItemDto::getDate)).collect(Collectors.toList());
            monthSchedule.setOldWorkTime(monthPublished.getWorkTime());
            monthSchedule.setWorkTime(JSON.toJSONString(workTimes));
            monthSchedule.setUpdater(userInfo.getUserId());
            monthSchedule.setUpdateTime(DateUtil.getCurrentTime(true));
            draftUpd.add(monthSchedule);
        } else {
            monthSchedule = new WaEmpScheduleDraftDo();
            monthSchedule.setScheduleDetailId(snowflakeUtil.createId());
            monthSchedule.setTenantId(tenantId);
            monthSchedule.setEmpId(empId);
            monthSchedule.setWorkNo(empInfo.getWorkno());
            monthSchedule.setEmpName(empInfo.getEmpName());
            monthSchedule.setYear(year);
            monthSchedule.setMonth(month);
            monthSchedule.setWorkTime(JSON.toJSONString(new ArrayList<>()));
            monthSchedule.setOldWorkTime(monthPublished.getWorkTime());
            monthSchedule.setDeleted(0);
            monthSchedule.setCreator(userInfo.getUserId());
            monthSchedule.setCreateTime(DateUtil.getCurrentTime(true));
            draftAdd.add(monthSchedule);
        }
        //更新
        if (CollectionUtils.isNotEmpty(draftUpd)) {
            waEmpScheduleDraftDo.batchUpdate(tenantId, draftUpd);
        }
        if (CollectionUtils.isNotEmpty(publishedUpd)) {
            waEmpScheduleDo.batchUpdate(tenantId, publishedUpd);
        }
        //新增
        if (CollectionUtils.isNotEmpty(draftAdd)) {
            waEmpScheduleDraftDo.batchSave(tenantId, draftAdd);
        }
        if (CollectionUtils.isNotEmpty(publishedAdd)) {
            waEmpScheduleDo.batchSave(tenantId, publishedAdd);
        }
        return Boolean.TRUE;
    }

    @Override
    public List<EmpOrderShiftInfoDto> getEmpScheduleForDispatchWork(DispatchWorkDto dto, UserInfo userInfo) {
        Long workDate = dto.getWorkDate();
        Short year = getYear(workDate, true);
        Short month = getMonth(workDate, true);
        Long orderId = dto.getOrderId();
        String tenantId = userInfo.getTenantId();
        List<WaEmpScheduleDo> empShifts = waEmpScheduleDo.getEmpSchedules(tenantId, Collections.singletonList(year), Collections.singletonList(month), null);
        if (CollectionUtils.isEmpty(empShifts)) {
            return Lists.newArrayList();
        }
        List<MultiShiftSimpleVo> defShifts = wfmShiftService.getAllShiftDefList(tenantId, workDate, workDate);
        Map<Long, MultiShiftSimpleVo> defShiftMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(defShifts)) {
            defShiftMap.putAll(defShifts.stream().collect(Collectors.toMap(MultiShiftSimpleVo::getShiftDefId, Function.identity(), (v1, v2) -> v2)));
        }
        List<Long> empIds = empShifts.stream().map(WaEmpScheduleDo::getEmpId).distinct().collect(Collectors.toList());
        Map<Long, Long> shiftGroupMap = getShiftGroupAndProcessMap(tenantId, workDate, workDate, empIds);
        List<EmpOrderShiftInfoDto> list = Lists.newArrayList();
        for (WaEmpScheduleDo empShift : empShifts) {
            Long empId = empShift.getEmpId();
            List<EmpScheduleItemVo> dayShifts = getScheduleMonthVo(empShift.getWorkTime());
            dayShifts = dayShifts.stream().filter(s -> s.getDate() >= workDate && s.getDate() <= workDate).collect(Collectors.toList());
            for (EmpScheduleItemVo dayShift : dayShifts) {
                List<ScheduleItemVo> shifts = dayShift.getSchedules();
                if (CollectionUtils.isEmpty(shifts)) {
                    continue;
                }
                shifts = shifts.stream().filter(s -> s.getLeafNumberId().equals(orderId)).collect(Collectors.toList());
                for (ScheduleItemVo shift : shifts) {
                    if (!defShiftMap.containsKey(shift.getShiftId())) {
                        continue;
                    }
                    MultiShiftSimpleVo multiShift = defShiftMap.get(shift.getShiftId());
                    EmpOrderShiftInfoDto empMultiShiftInfoDto = ObjectConverter.convert(multiShift, EmpOrderShiftInfoDto.class);
                    empMultiShiftInfoDto.setOrderId(shift.getLeafNumberId());
                    empMultiShiftInfoDto.setLeafNumber(shift.getLeafNumber());
                    empMultiShiftInfoDto.setProductId(shift.getProductId());
                    empMultiShiftInfoDto.setProductName(shift.getProductName());
                    empMultiShiftInfoDto.setProcessId(shift.getSalaryProcessId());
                    empMultiShiftInfoDto.setProcessName(shift.getSalaryProcessName());
                    empMultiShiftInfoDto.setEmpId(empId);
                    empMultiShiftInfoDto.setWorkDate(workDate);
                    empMultiShiftInfoDto.setShiftGroupId(shiftGroupMap.get(shift.getSalaryProcessId()));
                    list.add(empMultiShiftInfoDto);
                }
            }
        }
        return list;
    }

    private Map<Long, Long> getShiftGroupAndProcessMap(String tenantId, Long startDate, Long endDate, List<Long> empIds) {
        List<WaShiftGroupDo> shiftGroups = getShiftGroups(tenantId, startDate, endDate, empIds);
        return shiftGroups.stream().filter(group -> StringUtil.isNotBlank(group.getLeftAssociation())).map(shiftGroup -> {
            Map<Long, Long> map = new HashMap<>();
            String[] processIds = shiftGroup.getLeftAssociation().split(",");
            for (String processId : processIds) {
                map.put(Long.valueOf(processId), shiftGroup.getShiftGroupId());
            }
            return map;
        }).flatMap(map -> map.entrySet().stream()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v2));
    }

    @Override
    public List<WaShiftGroupDo> getShiftGroups(String tenantId, Long startDate, Long endDate, List<Long> empIds) {
        List<WaEmpShiftGroupDo> empShiftGroups = waEmpShiftGroupDo.getEmShiftGroupByDateRange(tenantId, empIds, startDate, endDate, null, null);
        List<Long> shiftGroupIds = empShiftGroups.stream().map(WaEmpShiftGroupDo::getShiftGroupId).distinct().collect(Collectors.toList());
        return waShiftGroupDo.getShiftGroups(tenantId, shiftGroupIds);
    }

    @Override
    public List<EmpOrderShiftInfoDto> getEmpScheduleForDispatchWorkRange(BatchDispatchWorkDateRangeDto dto, UserInfo userInfo) {
        Long startDate = dto.getStartDate();
        Long endDate = dto.getEndDate();
        String tenantId = userInfo.getTenantId();
        //解析排班年月按年分组
        Map<Short, List<Short>> yearAndMonthMap = getYearAndMonthMap(startDate, endDate, true);
        List<WaEmpScheduleDo> empShifts = Lists.newArrayList();
        for (Map.Entry<Short, List<Short>> entry : yearAndMonthMap.entrySet()) {
            Short year = entry.getKey();
            List<Short> months = entry.getValue();
            empShifts.addAll(waEmpScheduleDo.getEmpSchedules(tenantId, Collections.singletonList(year), months, dto.getEmpIds()));
        }
        if (CollectionUtils.isEmpty(empShifts)) {
            return Lists.newArrayList();
        }
        List<Long> orderIds = dto.getOrderIds();
        List<MultiShiftSimpleVo> defShifts = wfmShiftService.getAllShiftDefList(tenantId, startDate, endDate);
        Map<Long, MultiShiftSimpleVo> defShiftMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(defShifts)) {
            defShiftMap.putAll(defShifts.stream().collect(Collectors.toMap(MultiShiftSimpleVo::getShiftDefId, Function.identity(), (v1, v2) -> v2)));
        }
        List<Long> empIds = empShifts.stream().map(WaEmpScheduleDo::getEmpId).distinct().collect(Collectors.toList());
        Map<Long, Long> shiftGroupMap = getShiftGroupAndProcessMap(tenantId, startDate, endDate, empIds);
        List<EmpOrderShiftInfoDto> list = Lists.newArrayList();
        for (WaEmpScheduleDo empShift : empShifts) {
            Long empId = empShift.getEmpId();
            List<EmpScheduleItemVo> dayShifts = getScheduleMonthVo(empShift.getWorkTime());
            dayShifts = dayShifts.stream().filter(s -> s.getDate() >= startDate && s.getDate() <= endDate).collect(Collectors.toList());
            for (EmpScheduleItemVo dayShift : dayShifts) {
                List<ScheduleItemVo> shifts = dayShift.getSchedules();
                if (CollectionUtils.isEmpty(shifts)) {
                    continue;
                }
                if (CollectionUtils.isNotEmpty(orderIds)) {
                    shifts = shifts.stream().filter(s -> orderIds.contains(s.getLeafNumberId())).collect(Collectors.toList());
                }
                for (ScheduleItemVo shift : shifts) {
                    if (!defShiftMap.containsKey(shift.getShiftId())) {
                        continue;
                    }
                    MultiShiftSimpleVo multiShift = defShiftMap.get(shift.getShiftId());
                    EmpOrderShiftInfoDto empMultiShiftInfoDto = ObjectConverter.convert(multiShift, EmpOrderShiftInfoDto.class);
                    empMultiShiftInfoDto.setOrderId(shift.getLeafNumberId());
                    empMultiShiftInfoDto.setLeafNumber(shift.getLeafNumber());
                    empMultiShiftInfoDto.setProductId(shift.getProductId());
                    empMultiShiftInfoDto.setProductName(shift.getProductName());
                    empMultiShiftInfoDto.setProcessId(shift.getSalaryProcessId());
                    empMultiShiftInfoDto.setProcessName(shift.getSalaryProcessName());
                    empMultiShiftInfoDto.setEmpId(empId);
                    empMultiShiftInfoDto.setWorkDate(dayShift.getDate());
                    empMultiShiftInfoDto.setShiftGroupId(shiftGroupMap.get(shift.getSalaryProcessId()));
                    list.add(empMultiShiftInfoDto);
                }
            }
        }
        return list;
    }

    @Override
    public List<CalendarDateDto> getEmpWorkCalendarListByMonth(String tenantId, Long empId, Integer month) {
        Map<String, Long> monthDate = DateUtilExt.getMonthDateByYm(month);
        Long monthBeginTime = monthDate.get("start");
        Long monthEndTime = monthDate.get("end");
        long variable = 3600 * 24 * 7;
        long startDate = DateUtil.getOnlyDate(new Date(monthBeginTime * 1000));
        long endDate = DateUtil.getOnlyDate(new Date(monthEndTime * 1000));
        long start = startDate - variable;
        long end = endDate + variable;
        //特殊日期
        List<WfmCalendarDateDto> specialDate = wfmHolidayService.getCalendarDateList(tenantId, start, end);
        List<CalendarDateDto> dateDtoList = specialDate.stream().map(shift -> new CalendarDateDto(shift.getDateType(), DateUtil.convertDateTimeToStr(shift.getCalendarDate(), "yyyyMMdd", true), CalendarStatusEnum.NORMAL.getIndex(), shift.getCalendarDate()))
                .collect(Collectors.toList());
        //查询班次信息
        List<EmpMultiShiftInfoDto> shiftList = getEmpShiftInfos(tenantId, start, end, Collections.singletonList(empId));
        List<CalendarDateDto> shiftDateDtoList = shiftList.stream().map(shift -> new CalendarDateDto(shift.getDateType(), DateUtil.convertDateTimeToStr(shift.getWorkDate(), "yyyyMMdd", true), CalendarStatusEnum.NORMAL.getIndex(), shift.getWorkDate()))
                .collect(Collectors.toList());
        dateDtoList.addAll(shiftDateDtoList);
        dateDtoList = dateDtoList.stream().collect(Collectors.collectingAndThen(Collectors.toMap(CalendarDateDto::getWorkDateLong, Function.identity(), (v1, v2) -> v2), map -> new ArrayList<>(map.values())))
                .stream().sorted(Comparator.comparing(CalendarDateDto::getWorkDateLong)).collect(Collectors.toList());
        return dateDtoList;
    }

    @Override
    public List<EmpMultiShiftInfoDto> getEmpShiftInfos(String tenantId, Long startDate, Long endDate, List<Long> empIds) {
        //解析排班年月按年分组
        Map<Short, List<Short>> yearAndMonthMap = getYearAndMonthMap(startDate, endDate, true);
        //员工排班
        List<WaEmpScheduleDo> empMonthShifts = Lists.newArrayList();
        for (Map.Entry<Short, List<Short>> entry : yearAndMonthMap.entrySet()) {
            Short year = entry.getKey();
            List<Short> months = entry.getValue();
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("getEmpSchedulesByYearMonthAndEmp");
            empMonthShifts.addAll(waEmpScheduleDo.getEmpSchedules(tenantId, Collections.singletonList(year), months, empIds));
            stopWatch.stop();
            log.info("getEmpSchedulesByYearMonthAndEmpTime:{}", stopWatch.prettyPrint());
        }
        if (CollectionUtils.isNotEmpty(empIds)) {
            empMonthShifts = empMonthShifts.stream().filter(shift -> empIds.contains(shift.getEmpId())).collect(Collectors.toList());
        }
        List<MultiShiftSimpleVo> defShifts;
        try {
            UserInfo userInfo = UserContext.preCheckUser();
            Long userId = null != userInfo ? userInfo.getUserId() : null;
            UserContext.doInitSecurityUserInfo(tenantId, userId == null ? null : userId.toString(), null, null, null, null);
            //查询租户下所有班次
            defShifts = wfmShiftService.getAllShiftDefList(tenantId, startDate, endDate);
        } catch (Exception e) {
            log.error("getAllShiftDefListErr:{}",e.getMessage(), e);
            return Lists.newArrayList();
        } finally {
            UserContext.removeSecurityUserInfo();
        }
        Map<Long, MultiShiftSimpleVo> defShiftMap = defShifts.stream().collect(Collectors.toMap(MultiShiftSimpleVo::getShiftDefId, Function.identity(), (v1, v2) -> v2));
        //班次变更
        List<Long> empIdList = optimizeEmpIdList(empMonthShifts, empIds);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("查询班次变更");
        Map<String, Long> shiftChangeMap = wfmShiftService.getShiftChangeMap(tenantId, startDate, endDate, empIdList);
        stopWatch.stop();
        log.info("查询班次变更时长:{}", stopWatch.prettyPrint());
        List<EmpMultiShiftInfoDto> list = Lists.newArrayList();
        for (WaEmpScheduleDo empShift : empMonthShifts) {
            Long empId = empShift.getEmpId();
            List<EmpScheduleItemVo> dayShifts = getScheduleMonthVo(empShift.getWorkTime());
            dayShifts = dayShifts.stream().filter(s -> s.getDate() >= startDate && s.getDate() <= endDate).collect(Collectors.toList());
            for (EmpScheduleItemVo dayShift : dayShifts) {
                List<ScheduleItemVo> shifts = dayShift.getSchedules();
                Long workDate = dayShift.getDate();
                if (CollectionUtils.isEmpty(shifts)) {
                    continue;
                }
                List<Long> shiftIdList = shifts.stream().map(shift -> {
                    Long shiftId = shift.getShiftId();
                    String processShiftKey = String.format("%s_%s_%s_%s_%s", empId, workDate, shift.getSalaryProcessId(), shift.getLeafNumberId(), shiftId);
                    if (shiftChangeMap.containsKey(processShiftKey)) {
                        shiftId = shiftChangeMap.get(processShiftKey);
                        shiftChangeMap.remove(processShiftKey);
                    }
                    return shiftId;
                }).distinct().collect(Collectors.toList());
                for (Long shiftId : shiftIdList) {
                    if (!defShiftMap.containsKey(shiftId)) {
                        continue;
                    }
                    MultiShiftSimpleVo multiShift = defShiftMap.get(shiftId);
                    EmpMultiShiftInfoDto empMultiShiftInfoDto = ObjectConverter.convert(multiShift, EmpMultiShiftInfoDto.class);
                    empMultiShiftInfoDto.setEmpId(empId);
                    empMultiShiftInfoDto.setWorkDate(workDate);
                    list.add(empMultiShiftInfoDto);
                }
            }
        }
        // todo
        //list.addAll(getChangeShiftList(tenantId, shiftChangeMap, defShiftMap));
        return list;
    }

    private List<EmpMultiShiftInfoDto> getChangeShiftList(String tenantId, Map<String, Long> shiftChangeMap, Map<Long, MultiShiftSimpleVo> defShiftMap) {
        List<EmpMultiShiftInfoDto> list = Lists.newArrayList();
        if (!shiftChangeMap.isEmpty()) {
            ShiftDataContext context = new ShiftDataContext(tenantId, shiftChangeMap);
            for (Map.Entry<String, Long> entry : shiftChangeMap.entrySet()) {
                Long newShiftDefId = entry.getValue();
                if (!defShiftMap.containsKey(newShiftDefId)) {
                    continue;
                }
                String[] arr = entry.getKey().split("_");
                Long empId = Long.parseLong(arr[0]);
                Long workDate = Long.parseLong(arr[1]);
                MultiShiftSimpleVo multiShift = defShiftMap.get(newShiftDefId);
                EmpMultiShiftInfoDto empMultiShiftInfoDto = ObjectConverter.convert(multiShift, EmpMultiShiftInfoDto.class);
                // 获取订单信息
                Long orderId = Long.parseLong(arr[3]);
                empMultiShiftInfoDto.setLeafNumberId(orderId);
                empMultiShiftInfoDto.setLeafNumber(context.getOrderName(orderId));
                // 获取工序信息
                String processId = arr[2];
                empMultiShiftInfoDto.setProcessId(Long.parseLong(processId));
                empMultiShiftInfoDto.setProcessName(context.getProcessName(processId));
                WfmProcessInfo process = context.getProcess(empMultiShiftInfoDto.getProcessId().toString());
                Long productId = null != process ? Long.parseLong(process.getProductId()) : null;
                // 获取产品信息
                empMultiShiftInfoDto.setProductId(productId);
                empMultiShiftInfoDto.setProductName(context.getProductName(productId));
                empMultiShiftInfoDto.setEmpId(empId);
                empMultiShiftInfoDto.setWorkDate(workDate);
                list.add(empMultiShiftInfoDto);
            }
        }
        return list;
    }

    @Override
    public Boolean clearEmpScheduleInvalidShift(String tenantId, Short year, Short month, List<Long> empIds) {
        List<Short> yearList = null;
        if (null != year) {
            yearList = Collections.singletonList(year);
        }
        List<Short> monthList = null;
        if (null != month) {
            monthList = Collections.singletonList(month);
        }
        List<MultiShiftSimpleVo> defShifts = wfmShiftService.getAllShiftDefList(tenantId, null, null);
        Map<Long, MultiShiftSimpleVo> defShiftMap = defShifts.stream().collect(Collectors.toMap(MultiShiftSimpleVo::getShiftDefId, Function.identity(), (v1, v2) -> v2));
        List<WaEmpScheduleDo> empScheduleList = waEmpScheduleDo.getEmpSchedules(tenantId, yearList, monthList, empIds);
        if (CollectionUtils.isNotEmpty(empScheduleList)) {
            List<WaEmpScheduleDo> updateEmpScheduleList = Lists.newArrayList();
            for (WaEmpScheduleDo empSchedule : empScheduleList) {
                List<EmpScheduleItemVo> dayShifts = getScheduleMonthVo(empSchedule.getWorkTime());
                int updateNum = getUpdateNum(dayShifts, defShiftMap);
                if (updateNum > 0) {
                    empSchedule.setWorkTime(FastjsonUtil.toJson(dayShifts));
                    updateEmpScheduleList.add(empSchedule);
                }
            }
            if (CollectionUtils.isNotEmpty(updateEmpScheduleList)) {
                waEmpScheduleDo.batchUpdate(tenantId, updateEmpScheduleList);
            }
        }
        List<WaEmpScheduleDraftDo> empScheduleDraftList = waEmpScheduleDraftDo.getEmpSchedules(tenantId, yearList, monthList, null);
        if (CollectionUtils.isNotEmpty(empScheduleDraftList)) {
            List<WaEmpScheduleDraftDo> updateEmpScheduleDraftList = Lists.newArrayList();
            for (WaEmpScheduleDraftDo empScheduleDraft : empScheduleDraftList) {
                List<EmpScheduleItemVo> workTime = getScheduleMonthVo(empScheduleDraft.getWorkTime());
                int workTimeUpdateNum = getUpdateNum(workTime, defShiftMap);
                List<EmpScheduleItemVo> oldWorkTime = getScheduleMonthVo(empScheduleDraft.getOldWorkTime());
                int oldWorkTimeUpdateNum = getUpdateNum(oldWorkTime, defShiftMap);
                if (workTimeUpdateNum > 0 || oldWorkTimeUpdateNum > 0) {
                    if (workTimeUpdateNum > 0) {
                        empScheduleDraft.setWorkTime(FastjsonUtil.toJson(workTime));
                    }
                    if (oldWorkTimeUpdateNum > 0) {
                        empScheduleDraft.setOldWorkTime(FastjsonUtil.toJson(oldWorkTime));
                    }
                    updateEmpScheduleDraftList.add(empScheduleDraft);
                }
            }
            if (CollectionUtils.isNotEmpty(updateEmpScheduleDraftList)) {
                waEmpScheduleDraftDo.batchUpdate(tenantId, updateEmpScheduleDraftList);
            }
        }
        return true;
    }

    private int getUpdateNum(List<EmpScheduleItemVo> dayShifts, Map<Long, MultiShiftSimpleVo> defShiftMap) {
        int updateNum = 0;
        for (EmpScheduleItemVo dayShift : dayShifts) {
            List<ScheduleItemVo> shifts = dayShift.getSchedules();
            if (CollectionUtils.isEmpty(shifts)) {
                continue;
            }
            List<ScheduleItemVo> filterShifts = shifts.stream().filter(shift -> defShiftMap.containsKey(shift.getShiftId())).collect(Collectors.toList());
            boolean isUpdate = filterShifts.size() != shifts.size();
            if (isUpdate) {
                dayShift.setSchedules(filterShifts);
                updateNum++;
            }
        }
        return updateNum;
    }

    @Override
    public void exportWorkingHourEmpShift(HttpServletResponse response, HttpServletRequest request, EmpSchedulePage dto) throws Exception {
        log.info("Start to exportWorkingHourEmpShift param:[{}]", JSONUtils.ObjectToJson(dto));
        dto.setPageSize(100000);
        PageResult<EmpSchedulePageDto> pageResult = getEmpScheduleList(dto, getUserInfo());
        List<EmpSchedulePageDto> pageList = pageResult.getItems();
        if (null == pageList) {
            pageList = Lists.newArrayList();
        }
        downloadEmpShiftTemplate(dto, response, request, pageList);
    }

    public void resetHttpServletResponse(String fileName, HttpServletResponse response) {
        response.reset();
        // 指定下载的文件名
        response.setHeader("Content-Disposition", "attachment;" + fileName);
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
    }

    public void downloadEmpShiftTemplate(EmpSchedulePage dto, HttpServletResponse response, HttpServletRequest request, List<EmpSchedulePageDto> list) throws Exception {
        Long startDate = dto.getStartDate();
        Long endDate = dto.getEndDate();
        String fileName = XlsUtil.encodeFilename(String.format("按员工排班导出-%s-%s.xls",
                DateUtil.getDateStrByTimesamp(startDate), DateUtil.getDateStrByTimesamp(endDate)), request);
        resetHttpServletResponse(fileName, response);
        OutputStream output = response.getOutputStream();
        DataOutputStream dataOutPut = new DataOutputStream(output);
        // 创建对象
        Workbook wb = new HSSFWorkbook();
        CellStyle cellStyle = wb.createCellStyle();
        // 指定单元格居中对齐
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        // 指定单元格垂直居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 指定当单元格内容显示不下时自动换行
        cellStyle.setWrapText(true);
        // 设置单元格字体
        cellStyle.setFont(getFont(wb, (short) 11, "宋体", null, false, false));
        setCellBorder(cellStyle, BorderStyle.THIN);
        // 创建sheet表
        Sheet sheet = wb.createSheet("sheet1");
        List<Map<String, String>> titleLists = getSheetTimeTitles(startDate, endDate);
        // 设置前八列，设置合并行
        for (int i = 0; i < 7; i++) {
            sheet.setColumnWidth(i, 3000);
            sheet.addMergedRegion(new CellRangeAddress(0, 1, i, i));
        }
        // 创建表格日期表头 第一行
        Row titleRow = sheet.createRow(0);
        CellStyle titleRowStyle = getCellStyle(wb);
        setCellBorder(titleRowStyle, BorderStyle.THIN);
        createFixedHeaders(titleRow, titleRowStyle, new String[]{"姓名", "工号", "部门", "岗位", "工序", "工段", "班组"});
        //第二行
        Row titleRow2 = sheet.createRow(1);
        createFixedHeaders(titleRow2, titleRowStyle, new String[]{"", "", "", "", "", "", ""});
        titleRow2.setHeightInPoints(20f);

        CellStyle fixedDataStyle = wb.createCellStyle();
        fixedDataStyle.setFont(this.getFont(wb, (short) 11, "宋体", null, false, false));
        fixedDataStyle.setAlignment(HorizontalAlignment.CENTER);
        // 指定单元格垂直居中对齐
        fixedDataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 指定当单元格内容显示不下时自动换行
        fixedDataStyle.setWrapText(true);
        setCellBorder(fixedDataStyle, BorderStyle.THIN);
        List<Long> dateList = calculateDateRange(startDate, endDate);
        Map<Long, Integer> dateColMap = calculateMaxSchedulesPerDate(startDate, endDate, list);
        //导入数据
        for (int i = 0; i < list.size(); i++) {
            EmpSchedulePageDto empSchedule = list.get(i);
            Row row = sheet.createRow(2 + i);

            createFixedHeaders(row, fixedDataStyle, new String[]{empSchedule.getEmpName(), empSchedule.getWorkNo(),
                    Optional.ofNullable(empSchedule.getOrganizeTxt()).orElse("-"),
                    Optional.ofNullable(empSchedule.getPostTxt()).orElse("-"),
                    Optional.ofNullable(empSchedule.getWorkingProcessName()).orElse("-"),
                    Optional.ofNullable(empSchedule.getWorkshopSection()).map(DictSimple::getText).orElse("-"),
                    CollectionUtils.isEmpty(empSchedule.getShiftGroups()) ? "-" : empSchedule.getShiftGroups().stream().map(ShiftGroupDto::getShiftGroupName).distinct().collect(Collectors.joining(","))});

            List<EmpScheduleItemVo> shifts = Optional.ofNullable(empSchedule.getSchedules()).orElse(Lists.newArrayList());
            Map<Long, EmpScheduleItemVo> rowDataMap = shifts.stream().collect(Collectors.toMap(EmpScheduleItemVo::getDate, Function.identity(), (v1, v2) -> v1));
            int startCol = 7;
            for (Long dateTime : dateList) {
                Integer colSize = dateColMap.get(dateTime);
                for (int j = 0; j < colSize; j++) {
                    int colIndex = dateList.indexOf(dateTime);
                    Map<String, String> dateTitleMap = titleLists.get(colIndex);
                    sheet.setColumnWidth(startCol, 3500);
                    Cell dateTitleCell1 = titleRow.createCell(startCol + j);
                    if (j == 0) {
                        dateTitleCell1.setCellValue(dateTitleMap.get("week"));
                        dateTitleCell1.setCellStyle(titleRowStyle);
                        Cell dateTitleCell2 = titleRow2.createCell(startCol + j);
                        dateTitleCell2.setCellValue(dateTitleMap.get("day"));
                        dateTitleCell2.setCellStyle(titleRowStyle);
                    } else {
                        dateTitleCell1.setCellValue("");
                        dateTitleCell1.setCellStyle(titleRowStyle);
                        Cell dateTitleCell2 = titleRow2.createCell(startCol + j);
                        dateTitleCell2.setCellValue("");
                        dateTitleCell2.setCellStyle(titleRowStyle);
                    }
                    Cell cell = row.createCell(startCol + j);
                    cell.setCellValue("");
                    EmpScheduleItemVo empScheduleItemVo = rowDataMap.get(dateTime);
                    if (null == empScheduleItemVo) {
                        continue;
                    }
                    List<ScheduleItemVo> empShifts = empScheduleItemVo.getSchedules();
                    if (CollectionUtils.isEmpty(empShifts)) {
                        continue;
                    }
                    if (j > empShifts.size() - 1) {
                        continue;
                    }
                    Optional.ofNullable(empShifts.get(j)).ifPresent(shift -> {
                        String timeStr = shift.getShiftTimes().stream().map(timePair -> {
                            Long startDateTime = dateTime + timePair.doGetRealStartTime() * 60;
                            Long endDateTime = dateTime + timePair.doGetRealEndTime() * 60;
                            String startTimeMin = DateUtil.convertDateTimeToStr(startDateTime, "HH:mm", true);
                            startTimeMin = timePair.getStartTimeBelong() == 1 ? startTimeMin : String.format("%s(%s)", startTimeMin, ShiftTimeBelongTypeEnum.getName(2));
                            String endTimeMin = DateUtil.convertDateTimeToStr(endDateTime, "HH:mm", true);
                            endTimeMin = timePair.getEndTimeBelong() == 1 ? endTimeMin : String.format("%s(%s)", endTimeMin, ShiftTimeBelongTypeEnum.getName(2));
                            return String.format("%s~%s", startTimeMin, endTimeMin);
                        }).collect(Collectors.joining(","));
                        cell.setCellValue(String.format("叶片编号：%s\n计薪工序：%s\n班次：%s", shift.getLeafNumber(), shift.getSalaryProcessName(), timeStr));
                    });
                }
                if (colSize > 1) {
                    if (i == 2) {
                        sheet.addMergedRegion(new CellRangeAddress(0, 0, startCol, startCol + colSize - 1));
                        sheet.addMergedRegion(new CellRangeAddress(1, 1, startCol, startCol + colSize - 1));
                    }
                    for (int j = 0; j < colSize; j++) {
                        if (StringUtil.isBlank(row.getCell(startCol + j).getStringCellValue()) && colSize - j > 1) {
                            sheet.addMergedRegion(new CellRangeAddress(2 + i, 2 + i, startCol + j, startCol + colSize - 1));
                            break;
                        }
                    }
                }
                startCol += colSize;
            }
        }
        sheet.createFreezePane(7, 2, 7, 2);
        int sum = dateColMap.values().stream().mapToInt(Integer::intValue).sum();
        for (int i = 2; i <= list.size() + 2; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                for (int j = 7; j < sum + 7; j++) {
                    Cell cell = row.getCell(j);
                    if (cell == null) {
                        cell = row.createCell(j);
                    }
                    cell.setCellStyle(cellStyle);
                }
            }
        }
        optimizeColumnWidthsWithMergedCells(sheet);
        try {
            dataOutPut.flush();
            wb.write(dataOutPut);
            dataOutPut.close();
        } catch (IOException ie) {
            dataOutPut.close();
            log.error("downloadEmpShiftTemplate io exception:{}", ie.getMessage(), ie);
            throw new Exception("解析生成文件失败");
        } catch (Exception e) {
            log.error("downloadEmpShiftTemplate exception:{}", e.getMessage(), e);
            throw new Exception("导出文件失败");
        }
    }

    private void setCellBorder(CellStyle cellStyle, BorderStyle borderStyle) {
        cellStyle.setBorderTop(borderStyle); // 上边框 - 细线
        cellStyle.setBorderBottom(borderStyle); // 下边框 - 细线
        cellStyle.setBorderLeft(borderStyle); // 左边框 - 细线
        cellStyle.setBorderRight(borderStyle); // 右边框 - 细线
    }

    // 优化列宽计算方法（支持合并单元格）
    private void optimizeColumnWidthsWithMergedCells(Sheet sheet) {
        // 步骤1: 收集所有合并区域信息
        Map<Integer, CellRangeAddress> mergedRegionsMap = new HashMap<>();
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
            // 只记录主单元格（合并区域的第一个单元格）
            mergedRegionsMap.put(mergedRegion.getFirstRow() * 1000 + mergedRegion.getFirstColumn(), mergedRegion);
        }
        // 步骤2: 计算每列最大内容宽度
        int[] colMaxWidths = new int[sheet.getRow(0).getLastCellNum()];
        DataFormatter formatter = new DataFormatter();
        for (Row row : sheet) {
            for (Cell cell : row) {
                int colIndex = cell.getColumnIndex();
                int rowIndex = cell.getRowIndex();
                // 检查是否是合并单元格的一部分
                CellRangeAddress mergedRegion = mergedRegionsMap.get(rowIndex * 1000 + colIndex);
                if (mergedRegion != null) {
                    // 处理合并单元格主单元格
                    int colSpan = mergedRegion.getLastColumn() - mergedRegion.getFirstColumn() + 1;
                    String value = formatter.formatCellValue(cell);
                    int valueLength = value.length();
                    // 将内容长度分配到合并的列中
                    int avgWidthPerCol = (int) Math.ceil((double) valueLength / colSpan);
                    // 更新所有合并列的宽度
                    for (int i = mergedRegion.getFirstColumn(); i <= mergedRegion.getLastColumn(); i++) {
                        if (avgWidthPerCol > colMaxWidths[i]) {
                            colMaxWidths[i] = avgWidthPerCol;
                        }
                    }
                } else {
                    // 检查是否在合并区域内但不是主单元格
                    boolean isInMergedRegion = false;
                    for (CellRangeAddress region : mergedRegionsMap.values()) {
                        if (region.isInRange(rowIndex, colIndex) && (rowIndex != region.getFirstRow() || colIndex != region.getFirstColumn())) {
                            isInMergedRegion = true;
                            break;
                        }
                    }
                    // 仅处理非合并单元格或主单元格
                    if (!isInMergedRegion) {
                        String value = formatter.formatCellValue(cell);
                        int valueLength = value.length();
                        // 考虑数字格式的额外宽度
                        if (cell.getCellType() == CellType.NUMERIC) {
                            valueLength += 3; // 为数字格式增加额外空间
                        }
                        if (valueLength > colMaxWidths[colIndex]) {
                            colMaxWidths[colIndex] = valueLength;
                        }
                    }
                }
            }
        }
        // 步骤3: 应用优化后的列宽
        for (int i = 0; i < colMaxWidths.length; i++) {
            int desiredWidth = colMaxWidths[i];
            // 设置最小宽度
            if (desiredWidth < 8) desiredWidth = 8;
            // 设置最大宽度（Excel限制为255字符）
            if (desiredWidth > 100) desiredWidth = 100;
            // 增加20%的余量并转换为Excel单位
            int excelWidth = (int) ((desiredWidth * 1.2) * 256);
            excelWidth = Math.min(9000, excelWidth);
            sheet.setColumnWidth(i, excelWidth);
        }
    }

    private Map<Long, Integer> calculateMaxSchedulesPerDate(Long startDate, Long endDate, List<EmpSchedulePageDto> empSchedules) {
        Map<Long, Integer> dateMaxSizes = new HashMap<>();
        Long start = startDate;
        Long end = endDate;
        while (start <= end) {
            dateMaxSizes.put(start, 1);// 初始化
            if (CollectionUtils.isNotEmpty(empSchedules)) {
                // 遍历所有员工的排班数据
                for (EmpSchedulePageDto empSchedule : empSchedules) {
                    if (CollectionUtils.isNotEmpty(empSchedule.getSchedules())) {
                        // 遍历该员工的所有排班日期
                        for (EmpScheduleItemVo scheduleItem : empSchedule.getSchedules()) {
                            Long date = scheduleItem.getDate();
                            if (!start.equals(date)) {
                                continue;
                            }
                            int scheduleSize = scheduleItem.getSchedules().size();
                            // 更新该日期的最大班次数
                            dateMaxSizes.merge(date, scheduleSize, Math::max);
                        }
                    }
                }
            }
            start += 86400;
        }
        return dateMaxSizes;
    }

    // 计算日期范围
    private List<Long> calculateDateRange(long startSec, long endSec) {
        LocalDate startDate = Instant.ofEpochSecond(startSec).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDate = Instant.ofEpochSecond(endSec).atZone(ZoneId.systemDefault()).toLocalDate();
        long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        List<Long> dateTimeList = Lists.newArrayList();
        for (int i = 0; i < days; i++) {
            dateTimeList.add(startSec + 86400L * i);
        }
        return dateTimeList;
    }

    private void createFixedHeaders(Row row, CellStyle style, String[] headers) {
        for (int i = 0; i < headers.length; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(style);
        }
    }

    private List<Map<String, String>> getSheetTimeTitles(Long start, Long end) throws Exception {
        Date startDate = new Date(start * 1000);
        Date endDate = new Date(end * 1000);
        List<Map<String, String>> timeTitles = new ArrayList<>();
        int timeDiff = DateUtils.getIntervalDays(startDate, endDate);
        for (int i = 0; i <= timeDiff; i++) {
            Date date = new Date(DateUtil.addDate(startDate.getTime(), i) * 1000);
            Map<String, String> m = new HashMap<>();
            m.put("day", String.valueOf(DateUtils.formatDate(date, "yyyy/MM/dd")));
            String w = DateUtil.getWeekStr(date);
            m.put("week", w.replace("星期", "周"));
            timeTitles.add(m);
        }
        return timeTitles;
    }

    private CellStyle getCellStyle(Workbook wb) {
        // 设置样式
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setFont(this.getFont(wb, (short) 11, "微软雅黑", HSSFColor.HSSFColorPredefined.WHITE.getIndex(), true, false));
        // 指定单元格垂直居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return cellStyle;
    }

    private Font getFont(Workbook wb, short fontSize, String fontName, Short fontColor, boolean isBold, boolean isItalic) {
        // 设置字体
        Font font = wb.createFont();
        font.setBold(isBold);
        font.setItalic(isItalic);
        font.setFontHeightInPoints(fontSize);
        if (fontColor != null) {
            font.setColor(fontColor);
        }
        font.setFontName(fontName);
        return font;
    }

    private UserInfo getUserInfo() {
        SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        UserInfo userInfo = new UserInfo();
        userInfo.setTenantId(securityUserInfo.getTenantId());
        userInfo.setUserId(securityUserInfo.getUserId());
        userInfo.setStaffId(securityUserInfo.getEmpId());
        return userInfo;
    }

    @Override
    @Transactional
    public void alterPublishedEmpSchedule(WfmDateDispatchAddOrRemoveDto dispatch, boolean addOrRemove) {
        val date = dispatch.getDate();
        val empIds = dispatch.getEmpDispatchList().stream().map(it -> it.getEmpId()).collect(Collectors.toList());
        val tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        val dateStrs = sdf.format(new Date(date)).split("-");
        val year = dateStrs[0];
        val month = dateStrs[1];
        List<WaEmpScheduleDo> schedules;
        List<WaEmpScheduleDraftDo> draftSchedules;
        if (!empIds.isEmpty()) {
            schedules = waEmpScheduleDo.getEmpSchedules(tenantId, com.googlecode.totallylazy.Lists.list(Short.valueOf(year)), com.googlecode.totallylazy.Lists.list(Short.valueOf(month)), empIds);
            draftSchedules = waEmpScheduleDraftDo.getEmpSchedules(tenantId, com.googlecode.totallylazy.Lists.list(Short.valueOf(year)), com.googlecode.totallylazy.Lists.list(Short.valueOf(month)), empIds);
        } else {
            schedules = Lists.newArrayList();
            draftSchedules = Lists.newArrayList();
        }
        val missedScheduleEmpIds = Sequences.sequence(empIds).toList();
        val missedDraftScheduleEmpIds = Sequences.sequence(empIds).toList();
        missedScheduleEmpIds.removeAll(schedules.stream().map(it -> it.getEmpId()).collect(Collectors.toList()));
        missedDraftScheduleEmpIds.removeAll(draftSchedules.stream().map(it -> it.getEmpId()).collect(Collectors.toList()));

        val missedEmpIds = Sequences.sequence(missedScheduleEmpIds).join(missedDraftScheduleEmpIds)
                .stream().distinct().collect(Collectors.toList());

        log.info("debug scheduling " + FastjsonUtil.toJson(schedules));
        log.info("debug scheduling draft" + FastjsonUtil.toJson(draftSchedules));

        val missedEmps = sysEmpInfoDo.getEmpInfoByIds(SecurityUserUtil.getSecurityUserInfo().getTenantId(), missedEmpIds);

        missedDraftScheduleEmpIds.forEach(empId -> {
            val emp = missedEmps.stream().filter(it -> empId.equals(it.getEmpid())).findFirst().orElse(null);
            WaEmpScheduleDraftDo empSchedule = new WaEmpScheduleDraftDo();
            empSchedule.setScheduleDetailId(snowflakeUtil.createId());
            empSchedule.setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
            empSchedule.setWorkNo(emp == null ? null : emp.getWorkno());
            empSchedule.setEmpName(emp == null ? null : emp.getEmpName());
            empSchedule.setEmpId(empId);
            empSchedule.setMonth(Short.valueOf(month));
            empSchedule.setYear(Short.valueOf(year));
            empSchedule.setWorkTime("[]");
            empSchedule.setOldWorkTime("[]");
            empSchedule.setDeleted(0);
            empSchedule.setCreator(SecurityUserUtil.getSecurityUserInfo().getUserId());
            empSchedule.setCreateTime(DateUtil.getCurrentTime(true));
            waEmpScheduleDraftDo.batchSave(SecurityUserUtil.getSecurityUserInfo().getTenantId(), Sequences.sequence(empSchedule).toList());
            draftSchedules.add(empSchedule);
        });
        for (WaEmpScheduleDraftDo schedule : draftSchedules) {
            val empId = schedule.getEmpId();
            val workTime = schedule.getOldWorkTime();
            val empDispatch = dispatch.getEmpDispatchList().stream().filter(it -> it.getEmpId().equals(empId)).findFirst().get().getDispatchList().stream()
                    .map(it -> it.getSchedules()).flatMap(it -> it.stream()).collect(Collectors.toList());
            val workTimeList = FastjsonUtil.toList(workTime, WfmEmpDispatchAddOrRemoveDto.DateSchedules.class);
            if (!workTimeList.stream().filter(it -> date.equals(it.getDate() * 1000))
                    .findFirst().isPresent()) {
                workTimeList.add(new WfmEmpDispatchAddOrRemoveDto.DateSchedules(date / 1000, Lists.newArrayList()));
            }
            workTimeList.forEach(it -> {
                if (date.equals(it.getDate() * 1000)) {
                    it.getSchedules().forEach(one -> {
                        one.setDate(date / 1000);
                    });
                    if (addOrRemove) {
                        empDispatch.forEach(one -> {
                            if (!it.getSchedules().contains(one)) {
                                it.getSchedules().add(one);
                            }
                        });
                    } else {
                        empDispatch.forEach(one -> {
                            if (it.getSchedules().contains(one)) {
                                it.getSchedules().remove(one);
                            }
                        });
                    }
                }
            });
            waEmpScheduleDraftDo.updateOne(tenantId, empId, year, month, FastjsonUtil.toJson(workTimeList));
        }

        missedScheduleEmpIds.forEach(empId -> {
            val emp = missedEmps.stream().filter(it -> empId.equals(it.getEmpid())).findFirst().orElse(null);
            WaEmpScheduleDo empSchedule = new WaEmpScheduleDo();
            empSchedule.setScheduleDetailId(snowflakeUtil.createId());
            empSchedule.setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
            empSchedule.setWorkNo(emp == null ? null : emp.getWorkno());
            empSchedule.setEmpName(emp == null ? null : emp.getEmpName());
            empSchedule.setEmpId(empId);
            empSchedule.setMonth(Short.valueOf(month));
            empSchedule.setYear(Short.valueOf(year));
            empSchedule.setWorkTime("[]");
            empSchedule.setDeleted(0);
            empSchedule.setCreator(SecurityUserUtil.getSecurityUserInfo().getUserId());
            empSchedule.setCreateTime(DateUtil.getCurrentTime(true));
            waEmpScheduleDo.batchSave(SecurityUserUtil.getSecurityUserInfo().getTenantId(), Sequences.sequence(empSchedule).toList());
            schedules.add(empSchedule);
        });

        for (WaEmpScheduleDo schedule : schedules) {
            val empId = schedule.getEmpId();
            val workTime = schedule.getWorkTime();
            val empDispatch = dispatch.getEmpDispatchList().stream().filter(it -> it.getEmpId().equals(empId)).findFirst().get().getDispatchList().stream()
                    .map(it -> it.getSchedules()).flatMap(it -> it.stream()).collect(Collectors.toList());
            val workTimeList = FastjsonUtil.toList(workTime, WfmEmpDispatchAddOrRemoveDto.DateSchedules.class);
            if (!workTimeList.stream().filter(it -> date.equals(it.getDate() * 1000))
                    .findFirst().isPresent()) {
                workTimeList.add(new WfmEmpDispatchAddOrRemoveDto.DateSchedules(date / 1000, Lists.newArrayList()));
            }
            workTimeList.forEach(it -> {
                if (date.equals(it.getDate() * 1000)) {
                    it.getSchedules().forEach(one -> {
                        one.setDate(date / 1000);
                    });
                    if (addOrRemove) {
                        empDispatch.forEach(one -> {
                            if (!it.getSchedules().contains(one)) {
                                it.getSchedules().add(one);
                            }
                        });
                    } else {
                        empDispatch.forEach(one -> {
                            if (it.getSchedules().contains(one)) {
                                it.getSchedules().remove(one);
                            }
                        });
                    }
                }
            });
            val result = FastjsonUtil.toJson(workTimeList);
            log.info("debug scheduling updateone" + result);
            waEmpScheduleDo.updateOne(tenantId, empId, year, month, result);
        }
        if (addOrRemove) {
            if (!dispatch.isEmpsDispatchSame()) {
                throw new ServerException("只允许给一个排产进行派工");
            }
            List<String> processIds = Lists.newArrayList();
            dispatch.getEmpDispatchList().stream().forEach(empDispatch ->
                    empDispatch.getDispatchList().stream().forEach(dateSchedule ->
                            dateSchedule.getSchedules().forEach(schedule ->
                                    processIds.add(schedule.getSalaryProcessId()))));
            log.info("dispatch processes: " + FastjsonUtil.toJson(processIds));
            if (processIds.isEmpty()) {
                return;
            }
            val shiftGroupList = waShiftGroupDo.selectAllShiftGroup(SecurityUserUtil.getSecurityUserInfo().getTenantId());
            val shiftGroupIds = shiftGroupList.stream()
                    .filter(it -> StringUtils.isNotEmpty(it.getLeftAssociation()))
                    .filter(it -> Arrays.stream(it.getLeftAssociation().split(",")).anyMatch(processId -> processIds.contains(processId)))
                    .map(it -> it.getShiftGroupId()).distinct().collect(Collectors.toList());
            log.info("mapped shift group id: " + FastjsonUtil.toJson(shiftGroupIds));
            val existedEmpIds = waEmpShiftGroupDo.selectEmpListByDateSlotAndShiftGroupList(SecurityUserUtil.getSecurityUserInfo().getTenantId(), date / 1000, date / 1000, shiftGroupIds);
            log.info("empIds: " + FastjsonUtil.toJson(empIds));
            log.info("existedEmpIds: " + FastjsonUtil.toJson(existedEmpIds));
            empIds.removeAll(existedEmpIds);
            log.info("empIds: " + FastjsonUtil.toJson(empIds));
            List<WaEmpShiftGroupDo> empGroups = Lists.newArrayList();
            for (Long empId : empIds) {
                for (Long shiftGroupId : shiftGroupIds) {
                    WaEmpShiftGroupDo waEmpShiftGroupDo = new WaEmpShiftGroupDo();
                    waEmpShiftGroupDo.setEmpId(empId);
                    waEmpShiftGroupDo.setShiftGroupId(shiftGroupId);
                    waEmpShiftGroupDo.setStartDate(date / 1000);
                    waEmpShiftGroupDo.setEndDate(date / 1000 + 24 * 3600);
                    empGroups.add(waEmpShiftGroupDo);
                }
            }
            val user = SecurityUserUtil.getSecurityUserInfo();
            UserInfo userInfo = new UserInfo();
            userInfo.setUserId(user.getUserId());
            userInfo.setTenantId(user.getTenantId());
            userInfo.setStaffId(user.getEmpId());
            try {
                log.info("add new empGroups: " + FastjsonUtil.toJson(empGroups));
                val result = waEmpShiftGroupService.batchSave(empGroups, userInfo);
                if (!result.isSuccess()) {
                    throw new ServerException(result.getMsg());
                }
            } catch (Exception e) {
                log.error("waEmpShiftGroup batch insert error", e);
                throw new ServerException(e.getMessage());
            }
        }
    }

    @Override
    public List<Long> fetchScheduleDispatched(ScheduleCardDto selectedSchedule) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        val dateStrs = sdf.format(new Date(selectedSchedule.getDate())).split("-");
        val year = dateStrs[0];
        val month = dateStrs[1];

        val orderIdsIn1 = selectedSchedule.getOrderIds()
                .stream().map(it -> "@.schedules[*].leafNumberId ==\"" + it + "\"").collect(Collectors.toList());
        val orderIdsIn2 = selectedSchedule.getOrderIds()
                .stream().map(it -> "@.schedules[*].leafNumberId ==" + it).collect(Collectors.toList());
        val orderIdsIn = "(" + Sequences.sequence(orderIdsIn1).join(orderIdsIn2).stream()
                .collect(Collectors.joining("||")) + ")";

        val processIdsIn1 = selectedSchedule.getProcessIds()
                .stream().map(it -> "@.schedules[*].salaryProcessId ==\"" + it + "\"").collect(Collectors.toList());
        val processIdsIn2 = selectedSchedule.getProcessIds()
                .stream().map(it -> "@.schedules[*].salaryProcessId ==" + it).collect(Collectors.toList());
        val processIdsIn = "(" + Sequences.sequence(processIdsIn1).join(processIdsIn2).stream()
                .collect(Collectors.joining("||")) + ")";
        String sql = "select emp_id from wa_emp_schedule_detail_"
                + SecurityUserUtil.getSecurityUserInfo().getTenantId() + " where year = " + year
                + " and month = " + month + " and jsonb_path_exists(work_time::jsonb, " +
                "'$[*]?( @.date== " + selectedSchedule.getDate() / 1000 +
                " && " + orderIdsIn +
                " && " + processIdsIn +
                " && (@.schedules[*].shiftId == \"" + selectedSchedule.getShiftId() + "\" || @.schedules[*].shiftId == " + selectedSchedule.getShiftId() + ")" +
                ")' " +
                ")";
        return jdbcTemplate.queryForList(sql).stream()
                .map(it -> (Long) it.get("emp_id")).collect(Collectors.toList());
    }
}