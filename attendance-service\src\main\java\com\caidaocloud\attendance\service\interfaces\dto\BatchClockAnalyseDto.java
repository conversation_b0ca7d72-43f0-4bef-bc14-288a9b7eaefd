package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidao1.commons.utils.DateUtil;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("批量打卡分析计算参数DTO")
public class BatchClockAnalyseDto {
    @ApiModelProperty("租户ID， 可选，不传默认当前租户")
    private String belongOrgId;
    @ApiModelProperty("开始日期，格式yyyy-MM-dd日期时间戳")
    private Long startDate;
    @ApiModelProperty("结束日期，格式yyyy-MM-dd日期时间戳")
    private Long endDate;
    @ApiModelProperty("指定的员工ID， 可选，不传默认所有员工")
    private List<Long> empIds;
    @ApiModelProperty("打卡类型：1 GPS签到 2 扫码签到 3 外勤签到  4蓝牙签到  5 WIFI签到  6 补打卡, 可选")
    private Integer type;
    @ApiModelProperty("异步执行时的进程ID，可选，非异步场景无需传值")
    private String progress;
    @ApiModelProperty("是否多节点分析")
    private boolean multinode = false;

    public void doInit(UserInfo userInfo) {
        if (StringUtils.isBlank(this.belongOrgId)) {
            this.belongOrgId = null != userInfo ? userInfo.getTenantId() : UserContext.getTenantId();
        }
    }

    public void doInitDate() {
        if (this.startDate != null) {
            this.startDate = DateUtil.getOnlyDate(new Date(this.startDate * 1000));
        }
        if (this.endDate != null) {
            this.endDate = DateUtil.getOnlyDate(new Date(this.endDate * 1000));
        }
    }

    public static Result<Boolean> validateBatchAnalyseParams(BatchClockAnalyseDto dto, String progress) {
        if (StringUtils.isBlank(progress)) {
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_202837", WebUtil.getRequest()));
        }

        if (!progress.matches("^[a-zA-Z0-9_-]{1,50}$")) {
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_202838", WebUtil.getRequest()));
        }

        if (dto.getStartDate() == null || dto.getEndDate() == null) {
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_202839", WebUtil.getRequest()));
        }

        if (dto.getStartDate() > dto.getEndDate()) {
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_202840", WebUtil.getRequest()));
        }

        // 校验日期范围（最多31天）
        long daysDiff = (dto.getEndDate() - dto.getStartDate()) / 86400 + 1;
        if (daysDiff > 31) {
            return Result.fail(String.format(MessageHandler.getMessage("caidao.exception.error_202841", WebUtil.getRequest()), 31, daysDiff));
        }

        // 校验员工ID列表
        if (dto.getEmpIds() != null && dto.getEmpIds().size() > 1000) {
            return Result.fail(String.format(MessageHandler.getMessage("caidao.exception.error_202842", WebUtil.getRequest()), 500));
        }

        return Result.ok(Boolean.TRUE);
    }

    public void validateAnalyseParams() {
        if (StringUtils.isBlank(this.belongOrgId)) {
            throw new ServerException(ResponseWrap.wrapResult(201924, null).getMsg());
        }
        if (this.startDate == null || this.endDate == null) {
            throw new ServerException(ResponseWrap.wrapResult(202839, null).getMsg());
        }
    }

    public void validateDateRange() {
        try {
            long daysDiff = DateUtilExt.getDifferenceDay(this.startDate, this.endDate) + 1;
            if (daysDiff > 31) {
                throw new ServerException(String.format(ResponseWrap.wrapResult(202841, null).getMsg(), 31, daysDiff));
            }
        } catch (ParseException e) {
            throw new ServerException(ResponseWrap.wrapResult(201922, null).getMsg());
        }
    }
}
