<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.RegisterRecordMapper">
    <sql id="Base_Column_List">
        record_id, empid, register_type, result_desc, result_type, reason, reg_addr, reg_date_time,
        lng, lat, belong_date, mob_device_num, crtuser, crttime, upduser, updtime, normal_addr,
        normal_date, type, ow_rmk, pic_list, his_reg_time, shift_def_id, is_device_error,
        ori_mob_device_num, is_workflow, approval_status, approval_reason, file_path, revoke_reason,
        province, city, start_time, end_time, corpid, belong_org_id, last_approval_time, clock_site_status
    </sql>

    <select id="getRegisterPageList" resultType="java.util.Map">
        select * from (
        select rd.record_id as "recordId",
        rd.type,
        rd.reg_date_time as "regDateTime",
        rd.reg_addr as "regAddr",
        rd.mob_device_num as "mobDeviceNum",
        rd.reason,
        rd.approval_status as "approvalStatus",
        rd.last_approval_time as "approvalTime",
        rd.belong_date as "belongDate",
        rd.register_type as "registerType",
        rd.result_type as "resultType",
        rd.ow_rmk as "owRmk",
        rd.file_path as "files",
        rd.pic_list as "fileNames",
        sei.workno,
        case
        when cei.workno is not null and cei.workno!=''
        then concat(cei.workno, '(', cei.emp_name, ')')
        when sui.empname is not null and sui.empname != ''
        then concat(sui.account, '(', sui.empname, ')')
        else ''
        end AS "userName",
        rd.crttime,
        sei.emp_name as "empName",
        case
        when sco.full_path is not null and sco.full_path != ''
        then concat_ws('/', sco.full_path, sco.shortname)
        else sco.shortname
        end  as   "fullPath",
        sco.shortname,
        sco.shortname as "orgName",
        sei.stats as "empStatus",
        sei.employ_type as "empStyle",
        sei.orgid,
        rd.empid,rd.crtuser,rd.upduser,rd.updtime, rd.clock_site_status "clockSiteStatus",
        rd.source_from_type as "sourceFromType",
        rd.data_type as "dataType",
        rd.lng,
        rd.lat,
        rd.shift_def_id as "shiftDefId"
        FROM wa_register_record rd
        JOIN sys_emp_info sei on rd.empid = sei.empid AND sei.belong_org_id=rd.belong_org_id AND sei.deleted = 0
        LEFT JOIN sys_corp_org sco ON sco.orgid = sei.orgid AND sco.deleted = 0
        LEFT JOIN sys_user_info sui ON sui.userid = rd.crtuser
        LEFT JOIN sys_emp_info cei ON cei.empid=sui.empid
        WHERE rd.corpid = #{corpId} AND rd.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        <choose>
            <when test="startDate != null and endDate != null">
                <choose>
                    <when test="isAnalyze">
                        AND rd.belong_date BETWEEN #{startDate} and #{endDate}
                    </when>
                    <otherwise>
                        AND rd.reg_date_time BETWEEN #{startDate} and #{endDate}
                    </otherwise>
                </choose>
            </when>
        </choose>
        <if test="ifShowAll != null and ifShowAll == true">
            AND (rd.type in(1, 2, 3, 4, 5) OR (rd.type = 6 AND rd.approval_status = 2))
        </if>
        <if test="types != null and types.size > 0">
            AND rd.type in
            <foreach close=")" collection="types" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="keywords != null and keywords != ''">
            AND (sei.emp_name like concat('%', #{keywords}, '%') OR sei.workno like CONCAT('%', #{keywords},'%'))
        </if>
        <if test="datafilter != null and datafilter != ''">
            ${datafilter}
        </if>
        ) t
        <where>
            <if test="filter != null and filter != ''">
                ${filter}
            </if>
        </where>
        order by "regDateTime" desc
    </select>
    <select id="selectKqjRecord" resultType="com.caidao1.integrate.entity.dto.RecordDto">
        SELECT
              empid as "empId",reg_date_time as "registerTime",mob_device_num as "deviceNumber"
        FROM  wa_register_record wrr
        where wrr.belong_org_id =  #{tenantId}
               <if test="empId != null and empId != ''">
                 and wrr.empid = #{empId}
               </if>
              and wrr.source_from_type = 4
              AND wrr.belong_date BETWEEN #{startDate} AND #{endDate}
    </select>
    <select id="getEffectiveRegisterPageList" resultType="java.util.Map">
        with reg_group as (
        select empid,
        belong_date,
        register_type,
        min(reg_date_time) as s_time,
        max(reg_date_time) as e_time
        FROM wa_register_record r
        <where>
            exists(select 1 from sys_emp_info e where e.deleted = 0 and e.empid = r.empid and e.belong_org_id = #{belongOrgId,jdbcType=VARCHAR})
            <choose>
                <when test="startDate != null and endDate != null">
                    AND r.reg_date_time BETWEEN #{startDate} and #{endDate}
                </when>
            </choose>
            <if test="types != null">
                AND r.type in
                <foreach close=")" collection="types" item="listItem" open="(" separator=",">
                    #{listItem}
                </foreach>
            </if>
        </where>
        group by empid, belong_date, register_type
        )
        select t.*
        FROM (
        SELECT rd.record_id as "recordId",
        rd.type,
        rd.reg_date_time as "regDateTime",
        rd.reg_addr as "regAddr",
        rd.mob_device_num as "mobDeviceNum",
        rd.reason,
        rd.approval_status as "approvalStatus",
        rd.belong_date as "belongDate",
        rd.register_type as "registerType",
        rd.result_type as "resultType",
        sei.workno,
        sei.emp_name as "empName",
        case
        when sco.full_path is not null and sco.full_path != ''
        then concat_ws('/', sco.full_path, sco.shortname)
        else sco.shortname
        end  as   "fullPath",
        sco.shortname as "orgName",
        sco.shortname,
        sei.orgid,
        sei.employ_type as "employType",
        sei.hire_date as "hireDate",
        sei.stats as "empStatus",
        rd.normal_date as "normalDate"
        FROM wa_register_record rd
        JOIN reg_group rec ON rd.empid = rec.empid AND rd.belong_date = rec.belong_date AND ((rd.register_type = 1 AND
        rd.reg_date_time = rec.s_time) OR (rd.register_type = 2 AND rd.reg_date_time = rec.e_time))
        JOIN sys_emp_info sei on sei.empid = rd.empid and sei.deleted = 0
        LEFT JOIN sys_corp_org sco ON sco.orgid = sei.orgid AND sco.deleted = 0
        WHERE rd.corpid = #{corpId} and rd.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        <if test="startDate != null and endDate != null">
            AND rd.reg_date_time BETWEEN #{startDate} and #{endDate}
        </if>
        <if test="types != null and types.size > 0">
            AND rd.type in
            <foreach close=")" collection="types" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="keywords != null and keywords != ''">
            AND (sei.emp_name like concat('%', #{keywords}, '%') OR sei.workno like CONCAT('%', #{keywords}, '%'))
        </if>
        ) t
        <where>
            <if test="filter != null and filter != ''">
                ${filter}
            </if>
        </where>
        order by "regDateTime" desc
    </select>
    <select id="getEffectiveRegisterRecordPageList" resultType="java.util.Map">
        select t.*
        FROM (
        SELECT rd.record_id as "recordId",
        rd.type,
        rd.reg_date_time as "regDateTime",
        rd.reg_addr as "regAddr",
        rd.mob_device_num as "mobDeviceNum",
        rd.reason,
        rd.approval_status as "approvalStatus",
        rd.belong_date as "belongDate",
        rd.register_type as "registerType",
        rd.result_type as "resultType",
        rd.ow_rmk as "owRmk",
        rd.file_path as "files",
        rd.pic_list as "fileNames",
        sei.workno,
        sei.emp_name as "empName",
        case
        when sco.full_path is not null and sco.full_path != ''
        then concat_ws('/', sco.full_path, sco.shortname)
        else sco.shortname
        end  as   "fullPath",
        sco.shortname as "orgName",
        sco.shortname,
        sei.orgid,
        sei.employ_type as "empStyle",
        sei.hire_date as "hireDate",
        sei.stats as "empStatus",
        rd.normal_date as "normalDate", rd.clock_site_status clockSiteStatus,
        rd.source_from_type as "sourceFromType",
        rd.data_type as "dataType",
        rd.shift_def_id as "shiftDefId"
        FROM wa_register_record rd
        JOIN sys_emp_info sei on sei.empid = rd.empid and sei.belong_org_id=rd.belong_org_id and sei.deleted = 0
        LEFT JOIN sys_corp_org sco ON sco.orgid = sei.orgid AND sco.deleted = 0
        WHERE rd.corpid = #{corpId} and rd.belong_org_id = #{belongOrgId,jdbcType=VARCHAR} and rd.register_type is not null and
        rd.register_type <![CDATA[<>]]> 0
        and (rd.type in (1, 2, 4, 5) or (rd.type = 6 and rd.approval_status = 2))
        <choose>
            <when test="startDate != null and endDate != null">
                AND rd.reg_date_time BETWEEN #{startDate} and #{endDate}
            </when>
        </choose>
        <if test="keywords != null and keywords != ''">
            AND (sei.emp_name like concat('%', #{keywords}, '%') OR sei.workno like CONCAT('%', #{keywords}, '%'))
        </if>
        <if test="datafilter != null and datafilter != ''">
            ${datafilter}
        </if>
        ) t
        <where>
            <if test="filter != null and filter != ''">
                ${filter}
            </if>
        </where>
        order by "regDateTime" desc
    </select>

    <select id="queryRegisterDetailById" resultType="java.util.Map">
      SELECT
            e.workno,
            e.emp_name                                                         AS "empName",
            co.shortname                                                       AS "shortName",
            case
                when co.full_path is not null and co.full_path != ''
                then concat_ws('/', co.full_path, co.shortname)
                else co.shortname
                end  as   "fullPath",
            e.workplace                                                        AS "workCity",
            e.hire_date                                                        AS "hireDate",
            r.belong_date                                                      AS "belongDate",
            CASE r.register_type WHEN 1 THEN '签到' WHEN 2 THEN '签退' ELSE '' END AS "registerTypeName",
            r.reg_date_time                                                    AS "regDateTime",
            r.reason                                                           AS "reason",
            r.file_path                                                        AS "files",
            CASE
              WHEN r.approval_status = 1 THEN '审批中'
              WHEN r.approval_status = 2 THEN '已通过'
              WHEN r.approval_status = 3 THEN '已拒绝'
              WHEN r.approval_status = 4 THEN '已作废'
              WHEN r.approval_status = 5 THEN '已退回'
              WHEN r.approval_status = 9 THEN '已撤销'   END                   AS "statusName",
            r.pic_list                                                        As "fileNames"
      FROM wa_register_record r
      LEFT JOIN sys_emp_info e ON r.empid = e.empid and e.deleted = 0
      LEFT JOIN sys_corp_org co ON co.orgid = e.orgid
      where r.record_id = #{registerId}
        <if test="corpId != null">
            and r.corpid = #{corpId}
        </if>
    </select>

    <select id="queryDayAnalyzeDetailById" resultType="java.util.Map">
      SELECT
            distinct
            sei.workno as "workNo",
            a.belong_date as "belongDate",
            def.shift_def_name as "shiftDefName",
            COALESCE(f.clock_type,2) as "clockType",
            f.clock_rule as "clockRule",
            a.work_time as "workTime",
            a.actual_work_time as "actualWorkTime",
            def.start_time as "startTime",
	        def.end_time as "endTime",
	        def.on_duty_start_time as "onDutyStartTime",
	        def.on_duty_end_time as "onDutyEndTime",
	        def.off_duty_start_time as "offDutyStartTime",
	        def.off_duty_end_time as "offDutyEndTime",
	        a.reg_signin_time as "regSignInTime",
	        a.reg_signoff_time as "regSignOffTime",
	        r.result_type as "regSignInResult",
	        q.result_type as "regSignOffResult",
	        A.signin_id as "signinId",
	        A.signoff_id as "signoffId",
	        a.belong_org_id as "belongOrgId",
	        COALESCE(a.late_time,0) as "lateTime",
	        COALESCE(a.early_time,0) as "earlyTime",
	        COALESCE(a.kg_work_time,0) as "kgWorkTime",
	        a.empid
      FROM wa_analyze a
      JOIN sys_emp_info sei on sei.empid = a.empid and sei.deleted = 0
      LEFT JOIN wa_shift_def def ON def.shift_def_id = a.shift_def_id
      LEFT JOIN wa_emp_group c ON c.empid=a.empid AND a.belong_date BETWEEN c.start_time AND c.end_time
      LEFT JOIN wa_group d ON d.wa_group_id = c.wa_group_id
      LEFT JOIN wa_parse_group f ON f.parse_group_id = d.parse_group_id
      LEFT JOIN wa_register_record r ON r.record_id = A.signin_id
	  LEFT JOIN wa_register_record q ON q.record_id = A.signoff_id
      where analyze_id = #{analyzeId}
    </select>

    <select id="selectAttendanceRuleByEmpidAndDate" resultType="com.caidao1.wa.mybatis.model.WaParseGroup">
        SELECT wpg.clock_type,
        wpg.clock_rule,
        wpg.field_clock_link_shift
        FROM wa_emp_group eg
        JOIN wa_group wg on eg.wa_group_id = wg.wa_group_id
        JOIN wa_parse_group wpg on wpg.parse_group_id = wg.parse_group_id
        WHERE
        eg.empid = #{empid}
        AND #{date} BETWEEN eg.start_time AND eg.end_time
        <if test="belongOrgId != null">
            AND wg.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        </if>
        LIMIT 1
    </select>

    <select id="selectWaAnalyseListByWaGroup" parameterType="map" resultType="map">
        with emp as (
        select e.*,
        case
        when org.full_path is not null and org.full_path != ''
        then concat_ws('/', org.full_path, org.shortname)
        else org.shortname
        end  as   "full_path",
        org.shortname as "org_name"
        from sys_emp_info e
        left join sys_corp_org org on org.orgid = e.orgid and org.deleted = 0
        <where>
            e.belong_org_id =#{belongid} and e.deleted=0 and ((e.stats <![CDATA[<>]]> 1) or (e.stats = 1 and  #{startDate} <![CDATA[<=]]> e.termination_date))
            <if test="keywords != null and keywords != ''">
                AND (e.workno like concat('%', #{keywords}, '%') or e.emp_name like concat('%', #{keywords}, '%'))
            </if>
            <if test="empIds != null and empIds.size > 0">
                and e.empid in
                <foreach collection="empIds" item="empId" open="(" close=")" separator=",">
                    #{empId}
                </foreach>
            </if>
            <if test="empId != null and empId != ''">
                AND e.empid =#{empId}
            </if>
            and exists(SELECT g.empid from wa_emp_group g where g.empid=e.empid
            <if test="waGroupIds != null and waGroupIds.size>0">
                and g.wa_group_id in
                <foreach close=")" collection="waGroupIds" item="listItem" open="(" separator=",">
                    #{listItem}
                </foreach>
            </if>
            and g.start_time <![CDATA[<=]]> #{endDate} and g.end_time <![CDATA[>=]]> #{startDate})
            <!--<choose>
                <when test="anyEmpid != null">
                    and e.empid = any(${anyEmpid})
                </when>
                <when test="isDefault">
                    <![CDATA[
                and not exists(SELECT g.empid from wa_emp_group g where g.empid=e.empid and g.wa_group_id <> #{waGroupId} and g.start_time <=  #{endDate} and g.end_time >= #{startDate})
              ]]>
                </when>
                <otherwise>
                    <![CDATA[
                and exists(SELECT g.empid from wa_emp_group g where g.empid=e.empid and g.wa_group_id=#{waGroupId} and g.start_time <= #{endDate} and g.end_time >= #{startDate})
              ]]>
                </otherwise>
            </choose>-->
            ${datafilter}
        </where>
        )
        select temp.* from (SELECT t.*,
        (CASE
        WHEN (coalesce(t.late_time, 0) > 0 OR coalesce(t.early_time, 0) > 0 OR coalesce(t.kg_work_time, 0) > 0)
        THEN 1
        ELSE 0 END) AS "analyze_result"
        FROM (
        SELECT
        e.workno,
        e.emp_name,
        e.org_name,
        e.orgid,
        e.full_path AS "fullPath",
        e.employ_type ,
        e.stats as "emp_status",
        e.hire_date,
        def.on_duty_start_time,
        def.on_duty_end_time,
        def.date_type,
        def.shift_def_name,
        def.i18n_shift_def_name,
        def.shift_def_code,
        def.start_time as "shiftStartTime",
        def.end_time as "shiftEndTime",
        a.empid,
        a.late_time,
        a.early_time,
        COALESCE(a.work_time,0) as work_time,
        COALESCE(a.actual_work_time,0) as actual_work_time,
        a.abnormal_appeal_time,
        a.bdk_count,
        a.register_time,
        a.leave_time,
        a.is_exp,
        a.belong_date,
        a.analyze_id,
        a.kg_work_time,
        a.signin_id,
        a.signoff_id,a.shift_def_ids,
        case when a.reg_signin_time is null then '-' else to_char(to_timestamp(a.reg_signin_time), 'hh24:mi') end as "signInTime",
        case when a.reg_signoff_time is null then '-' else to_char(to_timestamp(a.reg_signoff_time), 'hh24:mi') end as "signOffTime",
        a.clock_type,
        CASE WHEN is_kg=1 THEN '是' ELSE '否' end as "isKg",
        CASE WHEN is_shift=1 THEN '是' ELSE '否' end as "isShift",
        COALESCE(cast(level_column_jsonb ->> 'time_duration' as INT4), 0)  as leave_time_duration,
        CASE
        WHEN (COALESCE(cast(travel_column_jsonb ->> 'valid_status' as INT4), 1)) = 1
        THEN COALESCE(cast(travel_column_jsonb ->> 'time_duration' as INT4), 0)
        ELSE 0 END                                                    as travel_time_duration
        ${columns}
        FROM
        wa_analyze a
        JOIN emp e ON a .empid = e.empid
        LEFT JOIN wa_shift_def def on def.shift_def_id = a.shift_def_id
        <where>
            a.belong_org_id =#{belongid}
            <if test="startDate != null and endDate != null">
                and a.belong_date BETWEEN #{startDate} and #{endDate}
            </if>
            <trim prefix=" and (" prefixOverrides="or" suffix=")">
                <if test="lat">
                    or a.late_time &gt; 0
                </if>
                <if test="early">
                    or a.early_time &gt; 0
                </if>
                <if test="kg">
                    or a.kg_work_time &gt; 0
                </if>
                <if test="leavetime">
                    or a.level_column_jsonb is not null and  a.level_column_jsonb -&gt;&gt; 'time_duration' &gt; '0'
                </if>
                <if test="overtime">
                    or a.ot_column_jsob is not null and  a.ot_column_jsob -&gt;&gt; 'time_duration' &gt; '0'
                </if>
            </trim>
        </where>
        <choose>
            <when test="notOrderBy != null and notOrderBy">

            </when>
            <otherwise>
                order by a.belong_date desc ,e.workno
            </otherwise>
        </choose>
        ) as t) as temp
        <where>
            ${filter}
        </where>
    </select>

    <select id="getRegisterRecordListByEmpId" resultType="com.caidao1.wa.mybatis.model.WaRegisterRecord">
        select
        <include refid="Base_Column_List"/>
        from wa_register_record
        where belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        and belong_date between #{startTime} and #{endTime}
        and empid=#{empId}
        <if test="queryApprovalBdk != null and queryApprovalBdk">
           and (type in (1,2,3,4,5) or (type=6 and approval_status=2))
        </if>
        order by reg_date_time asc
    </select>

    <select id="getAllRegisterRecordPageList" resultType="com.caidao1.wa.mybatis.model.WaRegisterRecord">
        SELECT r.record_id,
               r.empid,
               r.register_type,
               r.result_type,
               r.result_desc,
               r.normal_date,
               r.reg_date_time,
               r.normal_addr,
               r.reg_addr,
               r.reason,
               r.belong_date,
               r.shift_def_id,
               r.type,
               r.approval_status
        FROM wa_register_record r
        JOIN sys_emp_info e ON r.empid = e.empid
        WHERE r.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
          <choose>
              <when test="typeList != null and typeList.size > 0">
                  AND r.type in <foreach collection="typeList" open="(" separator="," close=")" item="item">#{item}</foreach>
              </when>
              <otherwise>
                  AND (r.type in (1, 2, 3, 4, 5) OR (r.type = 6 AND r.approval_status in <foreach collection="approvalStatusList" open="(" separator="," close=")" item="item">#{item}</foreach>))
              </otherwise>
          </choose>
          <if test="ifValid != null">
              AND r.if_valid = #{ifValid}
          </if>
          <if test="clockSiteStatus != null">
              and r.clock_site_status = #{clockSiteStatus}
          </if>
          AND r.empid in <foreach collection="empIds" open="(" separator="," close=")" item="item">#{item}</foreach>
        <!--AND (r.reg_date_time BETWEEN #{startDate} AND #{endDate} OR r.belong_date BETWEEN #{startDate} AND #{endDate})-->
        AND r.belong_date BETWEEN #{startDate} AND #{endDate}
        AND (e.termination_date IS NULL OR r.belong_date <![CDATA[<=]]> e.termination_date)
  </select>

    <select id="getAllRegisterRecordPageListNonAttendanceAnalyze" resultType="com.caidao1.wa.mybatis.model.WaRegisterRecord">
        SELECT r.record_id,r.empid,r.register_type,r.result_type,r.result_desc,r.normal_date,
        r.reg_date_time,r.normal_addr,r.reg_addr,r.reason,r.belong_date,r.shift_def_id,r.type,r.approval_status
        FROM wa_register_record r
        WHERE r.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        <choose>
            <when test="typeList != null and typeList.size > 0">
                AND r.type in <foreach collection="typeList" open="(" separator="," close=")" item="item">#{item}</foreach>
            </when>
            <otherwise>
                AND (r.type in (1, 2, 3, 4, 5) OR (r.type = 6 AND r.approval_status in <foreach collection="approvalStatusList" open="(" separator="," close=")" item="item">#{item}</foreach>))
            </otherwise>
        </choose>
        <if test="ifValid != null">
            AND r.if_valid = #{ifValid}
        </if>
        <if test="clockSiteStatus != null">
            and r.clock_site_status = #{clockSiteStatus}
        </if>
        AND r.empid in <foreach collection="empIds" open="(" separator="," close=")" item="item">#{item}</foreach>
        AND r.belong_date BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="getEmpWorkTimeRecordByDay" parameterType="map" resultType="map">
        SELECT r.record_id,
        r.belong_date,
        r.reg_date_time,
        type                                                                                                         as "regtype",
        register_type,
        reg_addr,
        result_desc,
        case result_type when 0 then 1 else result_type end                                                          as "result_type",
        reason,
        r.normal_addr,
        r.normal_date,
        CASE
        WHEN ued.third_id is not null and r.approval_status is null THEN true
        ELSE coalesce(r.is_workflow, false) END                                                                  as "is_workflow",
        CASE
        WHEN ued.third_id is not null and r.approval_status is null THEN ued.status
        ELSE r.approval_status END                                                                               as "approval_status",
        CASE
        WHEN ued.third_id is not null THEN ued.form_data ->> 'reason'
        ELSE r.approval_reason END                                                                               as "approval_reason",
        CASE
        WHEN ued.third_id is not null THEN ued.form_data ->> 'filePath'
        ELSE r.file_path END                                                                                     as "file_path",
        CASE
        WHEN ued.third_id is not null THEN cast(ued.form_data ->> 'regedTime' as INT8)
        ELSE r.reg_date_time END                                                                                 as "regedTime",
        CASE
        WHEN ued.third_id is not null THEN ued.form_data ->> 'regedAddr'
        ELSE r.reg_addr END                                                                                      as "regedAddr",
        ued.third_id,
        CASE
        WHEN ued.third_id is not null THEN 40
        WHEN r.is_workflow THEN 41
        ELSE null END                                                                                            as "func_type",
        ued.form_data_id,
        to_char(to_timestamp(r.his_reg_time), 'yyyy-MM-dd HH24:mi')                                                  as "hisRegTime",
        r.his_reg_time                                                                                               as "hisRegTimeSec",
        to_char(to_timestamp(r.his_reg_time), 'HH24:mi')                                                             as "hisRegTimeMin",
        CASE
        WHEN (wa.is_kg ISNULL OR wa.is_kg = 0) AND wa.early_time = 0 AND wa.late_time = 0 THEN TRUE
        ELSE FALSE END                                                                                           AS status,
        r.type
        from wa_register_record r
        LEFT JOIN sys_emp_info e on r.empid = e.empid and e.deleted = 0
        LEFT JOIN ui_emp_data ued on ued.third_id = r.record_id and ued.third_type = 'registerExp'
        LEFT JOIN wa_analyze wa ON wa.belong_date = r.belong_date AND wa.empid = e.empid
        where
        r.belong_date=#{daytime}
        and r.empid=#{empid} and r.clock_site_status=1
        <choose>
            <when test="includeOutReg != null and includeOutReg == true">
                and (r.type in (1, 2, 4, 5, 3) OR (r.type = 6 AND r.approval_status = 2))
            </when>
            <otherwise>
                and (r.type in (1, 2, 4, 5) OR (r.type = 6 AND r.approval_status = 2))
            </otherwise>
        </choose>
        ORDER BY r.reg_date_time
    </select>
    <select id="getEmpBdkRegisterList" resultType="com.caidao1.wa.mybatis.model.WaRegisterRecord">
        select empid, belong_date as "belongDate"
        from wa_register_record r
        where belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
          and type = 6
          and (approval_status is null or approval_status = 2)
          and empid in <foreach collection="empIdList" open="(" separator="," item="item" close=")">#{item}</foreach>
          and belong_date BETWEEN #{startDate} AND #{endDate}
    </select>

  <select id="selectEmpBdkRegisterList" resultType="com.caidao1.wa.mybatis.model.WaRegisterRecord">
      select empid, belong_date as "belongDate",
      approval_status as "approvalStatus",
      reg_date_time as "regDateTime"
      from wa_register_record r
      where belong_org_id = #{belongOrgId}
      and type = 6
      and empid in <foreach collection="empIdList" open="(" separator="," item="item" close=")">#{item}</foreach>
      and belong_date=#{belongDate}
  </select>

  <select id="getRegisterRecordList" resultType="com.caidao1.wa.mybatis.model.WaRegisterRecord">
      select
      <include refid="Base_Column_List"/>
      from wa_register_record
      where belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
      and reg_date_time between #{startTime} and #{endTime}
      and empid in <foreach collection="empIds" open="(" separator="," close=")" item="item">#{item}</foreach>
      <if test="clockSiteStatus != null">
          and clock_site_status = #{clockSiteStatus}
      </if>
      <choose>
          <when test="types != null and types.size() > 0">
              and (type in <foreach collection="types" item="type" open="(" close=")" separator=",">#{type}</foreach> or (type = 6 and approval_status = 2))
          </when>
          <otherwise>
              and (type in (1, 2, 4, 5) or (type = 6 and approval_status = 2))
          </otherwise>
      </choose>
  </select>

    <select id="getRegisterRecordPageList" resultType="com.caidao1.wa.mybatis.model.WaRegisterRecord">
        select
        <include refid="Base_Column_List"/>
        from wa_register_record
        where belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        and reg_date_time between #{startTime} and #{endTime}
        and empid in <foreach collection="empIds" open="(" separator="," close=")" item="item">#{item}</foreach>
        <if test="clockSiteStatus != null">
            and clock_site_status = #{clockSiteStatus}
        </if>
        <choose>
            <when test="types != null and types.size() > 0">
                and (type in <foreach collection="types" item="type" open="(" close=")" separator=",">#{type}</foreach> or (type = 6 and approval_status = 2))
            </when>
            <otherwise>
                and (type in (1, 2, 4, 5) or (type = 6 and approval_status = 2))
            </otherwise>
        </choose>
    </select>
    
    <select id="selectRegEmpIdList" resultType="java.lang.Long">
        select distinct empid
        from wa_register_record
        where belong_org_id = #{belongOrgId,jdbcType=VARCHAR} and reg_date_time between #{startTime} and #{endTime}
    </select>

  <select id="selectEmpParseGroupListByDate"
          resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpParseGroup">
      SELECT wegv.empid AS "empId",
      wpg.clock_type AS "clockType",
      wpg.clock_rule AS "clockRule"
      FROM wa_emp_group wegv
      JOIN wa_group wg ON wegv.wa_group_id = wg.wa_group_id
      JOIN wa_parse_group wpg ON wg.parse_group_id = wpg.parse_group_id
      WHERE wg.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
      AND #{date} BETWEEN wegv.start_time AND wegv.end_time
      AND wegv.empid in <foreach collection="empIds" open="(" separator="," close=")" item="item">#{item}</foreach>
  </select>

    <select id="selectEmpParseGroupListByDateRange"
            resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpParseGroup">
        SELECT wegv.empid AS "empId",
        wpg.clock_type AS "clockType",
        wpg.clock_rule AS "clockRule",
        wegv.start_time AS "startTime",
        wegv.end_time AS "endTime"
        FROM wa_emp_group wegv
        JOIN wa_group wg ON wegv.wa_group_id = wg.wa_group_id
        JOIN wa_parse_group wpg ON wg.parse_group_id = wpg.parse_group_id
        WHERE wg.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        AND #{startDate} <![CDATA[<=]]> wegv.end_time AND #{endDate}<![CDATA[>=]]> wegv.start_time
        AND wegv.empid in <foreach collection="empIds" open="(" separator="," close=")" item="item">#{item}</foreach>
    </select>

  <delete id="deleteByIds">
      delete from wa_register_record
      where belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
      <if test="ids != null and ids.size() > 0">
          and record_id in
          <foreach collection="ids" item="id" open="(" close=")" separator=",">
              #{id}
          </foreach>
      </if>
  </delete>

  <update id="updateClockSiteStatus">
      update wa_register_record set clock_site_status = #{clockSiteStatus}
      where belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
      and record_id in
      <foreach collection="recordIds" item="recordId" open="(" close=")" separator=",">
          #{recordId}
      </foreach>
  </update>

  <select id="queryWaRegisterRecordByBdkId" resultType="com.caidao1.wa.mybatis.model.WaRegisterRecord">
      select * from wa_register_record r
      where belong_org_id=#{tenantId,jdbcType=VARCHAR} and bdk_record_id=#{recordId}
      and type = 6
  </select>

  <select id="queryWaAnalyseList" parameterType="map" resultType="map">
      with emp as (
      select e.*,org.full_path, org.shortname as orgName
      from sys_emp_info e
      left join sys_corp_org org on org.orgid = e.orgid and org.deleted = 0
      <where>
          e.belong_org_id =#{tenantId} and e.deleted=0 and ((e.stats <![CDATA[<>]]> 42) or (e.stats=42 and  #{startDate} <![CDATA[<=]]> e.termination_date))
          <if test="keywords != null and keywords != ''">
              AND (e.workno like concat('%', #{keywords}, '%') or e.emp_name like concat('%', #{keywords}, '%'))
          </if>
          and exists(SELECT g.empid from wa_emp_group g where g.empid=e.empid
          <if test="waGroupIds != null and waGroupIds.size>0">
              and g.wa_group_id in
              <foreach close=")" collection="waGroupIds" item="listItem" open="(" separator=",">
                  #{listItem}
              </foreach>
          </if>
          and g.start_time <![CDATA[<=]]> #{endDate} and g.end_time <![CDATA[>=]]> #{startDate})
          ${datafilter}
      </where>
      )
      select temp.* from (SELECT t.*,
      (CASE WHEN (coalesce(t.late_time, 0) > 0 OR coalesce(t.early_time, 0) > 0 OR coalesce(t.kg_work_time, 0) > 0)
      THEN 1 ELSE 0 END) AS "analyzeResult"
      FROM (
      SELECT wa.analyze_id "analyzeId",
      wa.belong_date,
      a.empid,
      e.workno,
      e.emp_name "empName",
      e.orgName "orgName",
      e.orgid,
      e.full_path "fullPath",
      e.employ_type "employType",
      e.stats "empStatus",
      e.hire_date "hireDate",
      def.shift_def_name "shiftDefName",
      def.shift_def_code "shiftDefCode",
      wa.belong_date "belongDate",
      wa.clock_type,
      wa.late_time,
      wa.early_time,
      wa.kg_work_time,
      c.leave_name "leaveName",
      a.status "approvalStatus",
      a.crttime "applyDate"
      FROM wa_emp_leave a
      JOIN emp e ON a.empid = e.empid
      LEFT JOIN wa_leave_daytime b ON b.leave_id=a.leave_id
      JOIN wa_leave_type c ON a.leave_type_id=c.leave_type_id
      JOIN wa_analyze wa ON wa.empid=a.empid and wa.belong_date=coalesce(b.real_date,b.leave_date)
      LEFT JOIN wa_shift_def def on def.shift_def_id=wa.shift_def_id
      <where>
          wa.belong_org_id=#{tenantId} and a.status in (1,2)
          <if test="startDate != null and endDate != null">
              and wa.belong_date BETWEEN #{startDate} and #{endDate}
          </if>
      </where>
      UNION ALL
      SELECT wa.analyze_id "analyzeId",
      wa.belong_date,
      a.emp_id "empid",
      e.workno,
      e.emp_name "empName",
      e.orgName "orgName",
      e.orgid,
      e.full_path "fullPath",
      e.employ_type "employType",
      e.stats "empStatus",
      e.hire_date "hireDate",
      def.shift_def_name "shiftDefName",
      def.shift_def_code "shiftDefCode",
      wa.belong_date "belongDate",
      wa.clock_type,
      wa.late_time,
      wa.early_time,
      wa.kg_work_time,
      c.travel_type_name "leaveName",
      a.status "approvalStatus",
      a.create_time "applyDate"
      FROM wa_emp_travel a
      JOIN emp e ON a.emp_id = e.empid
      LEFT JOIN wa_emp_travel_daytime b ON b.travel_id=a.travel_id
      JOIN wa_travel_type c ON a.travel_type_id=c.travel_type_id
      JOIN wa_analyze wa ON wa.empid=a.emp_id and wa.belong_date=coalesce(b.real_date,b.travel_date)
      LEFT JOIN wa_shift_def def on def.shift_def_id=wa.shift_def_id
      <where>
          wa.belong_org_id=#{tenantId} and a.status in (1,2)
          <if test="startDate != null and endDate != null">
              and wa.belong_date BETWEEN #{startDate} and #{endDate}
          </if>
      </where>
      UNION ALL
      SELECT wa.analyze_id "analyzeId",
      wa.belong_date,
      wa.empid,
      e.workno,
      e.emp_name "empName",
      e.orgName "orgName",
      e.orgid,
      e.full_path "fullPath",
      e.employ_type "employType",
      e.stats "empStatus",
      e.hire_date "hireDate",
      def.shift_def_name "shiftDefName",
      def.shift_def_code "shiftDefCode",
      wa.belong_date "belongDate",
      wa.clock_type,
      wa.late_time,
      wa.early_time,
      wa.kg_work_time,
      '补卡' AS "leaveName",
      wrr.approval_status "approvalStatus",
      wrr.crttime "applyDate"
      FROM wa_analyze wa
      JOIN emp e ON wa.empid = e.empid
      JOIN wa_register_record wrr ON wa.belong_date = wrr.belong_date AND wrr.empid=wa.empid
      LEFT JOIN wa_shift_def def ON def.shift_def_id=wa.shift_def_id
      <where>
          wa.belong_org_id=#{tenantId} AND wrr.type=6 AND wrr.approval_status IN (1,2)
          <if test="startDate != null and endDate != null">
              and wa.belong_date BETWEEN #{startDate} AND #{endDate}
          </if>
      </where>
      ) as t
      <choose>
          <when test="notOrderBy != null and notOrderBy">

          </when>
          <otherwise>
              order by t.belong_date desc,t.workno
          </otherwise>
      </choose>) as temp
      <where>
          ${filter}
      </where>
  </select>

  <select id="queryWaAbnormalAnalyseList" resultType="long">
      with emp as (
      select e.*,org.full_path, org.shortname as orgName
      from sys_emp_info e
      left join sys_corp_org org on org.orgid = e.orgid and org.deleted = 0
      <where>
          e.belong_org_id =#{tenantId} and e.deleted=0 and ((e.stats <![CDATA[<>]]> 42) or (e.stats=42 and  #{startDate} <![CDATA[<=]]> e.termination_date))
          and exists(SELECT g.empid from wa_emp_group g where g.empid=e.empid
          <if test="waGroupIds != null and waGroupIds.size > 0">
              and g.wa_group_id in
              <foreach close=")" collection="waGroupIds" item="listItem" open="(" separator=",">
                  #{listItem}
              </foreach>
          </if>
          and g.start_time <![CDATA[<=]]> #{endDate} and g.end_time <![CDATA[>=]]> #{startDate})
      </where>
      )
      select distinct temp.empid from (SELECT t.empid,
      (CASE WHEN (coalesce(t.late_time, 0) > 0 OR coalesce(t.early_time, 0) > 0 OR coalesce(t.kg_work_time, 0) > 0) THEN 1 ELSE 0 END) AS analyzeResult
      FROM (SELECT a.empid, a.late_time, a.early_time, a.kg_work_time
      FROM wa_analyze a
      JOIN emp e ON a.empid = e.empid
      <where>
          a.belong_org_id=#{tenantId}
          <if test="startDate != null and endDate != null">
              and a.belong_date BETWEEN #{startDate} and #{endDate}
          </if>
      </where>
      ) as t) as temp
      <where>
          <if test="analyzeResult != null">
              temp.analyzeResult = #{analyzeResult}
          </if>
      </where>
  </select>

  <select id="selectClockRecords" resultType="com.caidao1.wa.mybatis.model.WaRegisterRecord">
      select * from wa_register_record
      where belong_org_id = #{tenantId}
      <if test="empId != null">
          and empid = #{empId}
      </if>
      <if test="belongDate != null">
          and belong_date = #{belongDate}
      </if>
      <if test="type != null">
          and type = #{type}
      </if>
      <if test="registerType != null">
          and register_type = #{registerType}
      </if>
      <if test="sort != null">
          order by ${sort}
      </if>
  </select>

  <select id="queryAnalyseList" resultType="com.caidao1.wa.mybatis.model.WaAnalyze">
      SELECT *
      FROM wa_analyze
      WHERE belong_org_id = #{tenantId,jdbcType=VARCHAR}
        AND (
            (level_column_jsonb IS NOT NULL AND level_column_jsonb != '{}') OR
            (ot_column_jsob IS NOT NULl AND ot_column_jsob != '{}')
              )
      <if test="empId != null">
          AND r.empid = #{empId}
      </if>
  </select>

  <!-- 支持多个考勤分组ID的员工信息查询 -->
  <select id="searchEmpInfoListMultiGroup" parameterType="map" resultType="map">
      select * from (  SELECT
          e.empid,
          e.workno,
          e.emp_name,
          e.hire_date,
          e.stats as "emp_status",
          e.employ_type,
          e.orgid,
          e.termination_date,
          case
              when sco.full_path is not null and sco.full_path != ''
              then concat_ws('/', sco.full_path, sco.shortname)
              else sco.shortname
          end as "full_path",
      sco.shortname as "org_name",
          wg.wa_group_id,
          wg.wa_group_name,
          concat(e.empid, '_', wg.wa_group_id) as "bid"
      FROM sys_emp_info e
      LEFT JOIN sys_corp_org sco ON sco.orgid = e.orgid and sco.orgtype2=2
      JOIN (select DISTINCT empid,wa_group_id from wa_emp_group   where  start_time <![CDATA[<=]]> #{endDate}
      AND end_time <![CDATA[>=]]> #{startDate}) weg ON weg.empid = e.empid
      JOIN wa_group wg ON wg.wa_group_id = weg.wa_group_id
      <where>
        e.belong_org_id = #{belongid}
          AND e.deleted = 0
          AND ((e.stats <![CDATA[<>]]> 1) OR (e.stats = 1 AND #{startDate} <![CDATA[<=]]> e.termination_date))

          <if test="empIds != null and empIds.size > 0">
              AND e.empid IN
              <foreach collection="empIds" item="empId" open="(" close=")" separator=",">
                  #{empId}
              </foreach>
          </if>

          <if test="waGroupIds != null and waGroupIds.size > 0">
              AND wg.wa_group_id IN
              <foreach collection="waGroupIds" item="waGroupId" open="(" close=")" separator=",">
                  #{waGroupId}
              </foreach>
          </if>

          <if test="keywords != null and keywords != ''">
              AND (e.workno LIKE CONCAT('%', #{keywords}, '%') OR e.emp_name LIKE CONCAT('%', #{keywords}, '%'))
          </if>

          <if test="datafilter != null and datafilter != ''">
              ${datafilter}
          </if>
      </where>
      ) as t
      <where>
          ${filter}
      </where>
  </select>
</mapper>