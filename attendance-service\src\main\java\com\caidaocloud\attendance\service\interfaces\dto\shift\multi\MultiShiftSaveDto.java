package com.caidaocloud.attendance.service.interfaces.dto.shift.multi;

import com.caidao1.commons.utils.JacksonJsonUtil;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiOvertimeDto;
import com.caidaocloud.attendance.core.wa.dto.shift.RestPeriodDto;
import com.caidaocloud.attendance.core.wa.dto.shift.ShiftHalfdayTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ShiftSaveDto;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.WebUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.postgresql.util.PGobject;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * 班次设置（支持一段班或多段班）保存胖DTO
 *
 * <AUTHOR>
 * @Date 2025/1/23
 */
@Data
@ApiModel("班次设置（支持一段班或多段班）保存胖DTO")
public class MultiShiftSaveDto extends ShiftSaveDto {
    @ApiModelProperty("工作日多段上班时间")
    private List<MultiWorkTimeDto> multiWorkTimes;

    @ApiModelProperty("休息日多段打卡时间")
    private List<MultiCheckinTimeDto> multiCheckinTimes;

    @Override
    public WaShiftDef doConvert() {
        WaShiftDef shiftDef = super.doConvert();
        try {
            doSetMultiWorkTimesJsonInfo(shiftDef);
            doSetMultiCheckinTimesJsonInfo(shiftDef);
        } catch (Exception e) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_90014", WebUtil.getRequest()));
        }
        // 计算半天定义时间
        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDef.getDateType()) &&
                ShiftHalfdayTypeEnum.HALF_OF_SHIFT.getIndex().equals(shiftDef.getHalfdayType())) {
            // 半天定义时间=第一段班次的上班时间+班次工时的一半
            BigDecimal workTotalTime = BigDecimal.valueOf(shiftDef.getWorkTotalTime().longValue());
            int halfWotkTime = workTotalTime.divide(BigDecimal.valueOf(2), 0, RoundingMode.HALF_UP).intValue();
            halfWotkTime = shiftDef.getStartTime() + halfWotkTime;
            if (halfWotkTime > 1440) {
                shiftDef.setHalfdayTimeBelong(ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex());
                shiftDef.setHalfdayTime(halfWotkTime - 1440);
            } else {
                shiftDef.setHalfdayTimeBelong(ShiftTimeBelongTypeEnum.TODAY.getIndex());
                shiftDef.setHalfdayTime(halfWotkTime);
            }
        }
        return shiftDef;
    }

    /**
     * 计算休息时长（重写）
     *
     * @param shiftDef
     * @param restList
     */
    @Override
    public void doSetRestTime(WaShiftDef shiftDef, List<RestPeriodDto> restList) {
        Integer iStart = shiftDef.doGetRealStartTime();
        Integer iEnd = shiftDef.doGetRealEndTime();

        int restTotalTime = 0;
        shiftDef.setRestTotalTime(restTotalTime);
        if (iStart == null || iEnd == null || !DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDef.getDateType())) {
            return;
        }
        if (shiftDef.getIsNoonRest() == null || !shiftDef.getIsNoonRest() || CollectionUtils.isEmpty(restList)) {
            return;
        }

        for (RestPeriodDto period : restList) {
            Integer begin = period.doGetRealNoonRestStart();
            Integer end = period.doGetRealNoonRestEnd();
            if (begin == null || end == null) {
                continue;
            }
            if (!(end <= iStart || iEnd <= begin)) {
                restTotalTime += Math.min(end, iEnd) - Math.max(begin, iStart);
            }
        }
        shiftDef.setRestTotalTime(restTotalTime);
    }

    /**
     * 计算工作时长（重写）
     *
     * @param shiftDef
     */
    @Override
    public void doSetWorkTime(WaShiftDef shiftDef) {
        int workTime = 0;
        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDef.getDateType()) &&
                shiftDef.getEndTime() != null && shiftDef.getStartTime() != null) {
            Integer wStart = shiftDef.doGetRealStartTime();
            Integer wEnd = shiftDef.doGetRealEndTime();
            Integer restTime = Optional.ofNullable(shiftDef.getRestTotalTime()).orElse(0);
            workTime = wEnd - wStart - restTime;
        }
        shiftDef.setWorkTotalTime(workTime);
    }

    @Override
    public void doSetIsNight(WaShiftDef shiftDef) {
        if (ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(shiftDef.getStartTimeBelong())
                && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(shiftDef.getEndTimeBelong())) {
            shiftDef.setIsNight(Boolean.TRUE);
        } else {
            shiftDef.setIsNight(Boolean.FALSE);
        }
    }

    @Override
    public void doInitOnDutyTime() {
        List<MultiWorkTimeDto> multiWorkTimeList = this.multiWorkTimes;
        if (CollectionUtils.isNotEmpty(multiWorkTimeList)) {
            multiWorkTimeList.forEach(MultiWorkTimeDto::doInitOnDutyTime);
        }

        if (CollectionUtils.isNotEmpty(this.getMultiCheckinTimes())) {
            List<MultiCheckinTimeDto> multiCheckinTimes = this.getMultiCheckinTimes();
            multiCheckinTimes.forEach(MultiCheckinTimeDto::doInitOnDutyTime);
        }
    }

    /**
     * 初始化历史字段值（兼容一段班对应的历史字段）
     */
    public void initSingleShift() {
        List<MultiWorkTimeDto> multiWorkTimeList = this.multiWorkTimes;
        if (CollectionUtils.isNotEmpty(multiWorkTimeList)) {
            multiWorkTimeList.sort(Comparator.comparing(MultiWorkTimeDto::doGetRealStartTime));
            MultiWorkTimeDto firstWorkTime = multiWorkTimeList.get(0);

            this.setStartTime(firstWorkTime.getStartTime());
            this.setEndTime(firstWorkTime.getEndTime());
            this.setOnDutyStartTime(firstWorkTime.getOnDutyStartTime());
            this.setOnDutyEndTime(firstWorkTime.getOnDutyEndTime());
            this.setOffDutyStartTime(firstWorkTime.getOffDutyStartTime());
            this.setOffDutyEndTime(firstWorkTime.getOffDutyEndTime());
            this.setStartTimeBelong(firstWorkTime.getStartTimeBelong());
            this.setEndTimeBelong(firstWorkTime.getEndTimeBelong());
            this.setOnDutyStartTimeBelong(firstWorkTime.getOnDutyStartTimeBelong());
            this.setOnDutyEndTimeBelong(firstWorkTime.getOnDutyEndTimeBelong());
            this.setOffDutyStartTimeBelong(firstWorkTime.getOffDutyStartTimeBelong());
            this.setOffDutyEndTimeBelong(firstWorkTime.getOffDutyEndTimeBelong());
            this.setIsNight(firstWorkTime.getIsNight());
            this.setWorkTotalTime(firstWorkTime.getWorkTotalTime());
        }

        if (CollectionUtils.isNotEmpty(this.getMultiCheckinTimes())) {
            List<MultiCheckinTimeDto> multiCheckinTimes = this.getMultiCheckinTimes();
            multiCheckinTimes.sort(Comparator.comparing(MultiCheckinTimeDto::doGetRealOnDutyStartTime));
            MultiCheckinTimeDto firstCheckInTime = multiCheckinTimes.get(0);

            this.setOnDutyStartTime(firstCheckInTime.getOnDutyStartTime());
            this.setOnDutyEndTime(firstCheckInTime.getOnDutyEndTime());
            this.setOffDutyStartTime(firstCheckInTime.getOffDutyStartTime());
            this.setOffDutyEndTime(firstCheckInTime.getOffDutyEndTime());
            this.setOnDutyStartTimeBelong(firstCheckInTime.getOnDutyStartTimeBelong());
            this.setOnDutyEndTimeBelong(firstCheckInTime.getOnDutyEndTimeBelong());
            this.setOffDutyStartTimeBelong(firstCheckInTime.getOffDutyStartTimeBelong());
            this.setOffDutyEndTimeBelong(firstCheckInTime.getOffDutyEndTimeBelong());
        }

        this.initOvertime();
    }

    public void doSetMultiWorkTimesJsonInfo(WaShiftDef shiftDef) throws Exception {
        if (null == this.getMultiWorkTimes()) {
            return;
        }
        List<MultiWorkTimeDto> multiWorkTimeList = this.getMultiWorkTimes();
        if (multiWorkTimeList.size() > 1) {
            Integer workTotalTime = 0;
            for (MultiWorkTimeDto it : multiWorkTimeList) {
                it.doSetWorkTime();
                workTotalTime += it.getWorkTotalTime();
            }
            shiftDef.setWorkTotalTime(workTotalTime);
            long kyCount = multiWorkTimeList.stream().filter(it -> it.getIsNight() != null && it.getIsNight()).count();
            shiftDef.setIsNight(kyCount > 0);
        } else {
            // 下面两个字段的值因在前面已经计算过了，因此可直接从shiftDef对象中获取
            MultiWorkTimeDto firstWorkTime = multiWorkTimeList.get(0);
            firstWorkTime.setWorkTotalTime(shiftDef.getWorkTotalTime());
            firstWorkTime.setIsNight(shiftDef.getIsNight());
        }

        if (shiftDef.getWorkTotalTime() > 2880) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_202807", WebUtil.getRequest()));
        }

        if (CollectionUtils.isNotEmpty(multiWorkTimeList)) {
            PGobject pGobject = new PGobject();
            pGobject.setType("jsonb");
            pGobject.setValue(JacksonJsonUtil.beanToJson(multiWorkTimeList));
            shiftDef.setMultiWorkTimes(pGobject);
        } else {
            PGobject pGobject = new PGobject();
            pGobject.setType("jsonb");
            pGobject.setValue("[]");
            shiftDef.setMultiWorkTimes(pGobject);
        }
    }

    public void doSetMultiCheckinTimesJsonInfo(WaShiftDef shiftDef) throws Exception {
        if (null == this.getMultiCheckinTimes()) {
            return;
        }
        List<MultiCheckinTimeDto> checkinTimes = this.getMultiCheckinTimes();
        if (CollectionUtils.isEmpty(checkinTimes)) {
            PGobject pGobject = new PGobject();
            pGobject.setType("jsonb");
            pGobject.setValue("[]");
            shiftDef.setMultiCheckinTimes(pGobject);
            return;
        }
        PGobject pGobject = new PGobject();
        pGobject.setType("jsonb");
        pGobject.setValue(JacksonJsonUtil.beanToJson(checkinTimes));
        shiftDef.setMultiCheckinTimes(pGobject);
    }

    public void checkMultiWorkTime() {
        if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(this.getDateType())) {
            return;
        }
        if (CollectionUtils.isEmpty(this.multiWorkTimes)) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201912", WebUtil.getRequest()));
        }
        this.multiWorkTimes.sort(Comparator.comparing(MultiWorkTimeDto::doGetRealStartTime));
        Integer startTimeBelong = this.multiWorkTimes.get(0).getStartTimeBelong();
        if (!ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(startTimeBelong)) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_202806", WebUtil.getRequest()));
        }
        if (this.multiWorkTimes.size() == 1) {
            MultiWorkTimeDto.checkTime(this.multiWorkTimes.get(0), 1);
            return;
        }
        for (int i = this.multiWorkTimes.size() - 1; i >= 1; i--) {
            // 当前班次校验
            MultiWorkTimeDto current = this.multiWorkTimes.get(i);
            MultiWorkTimeDto.checkTime(current, i + 1);
            // 与上一段班次校验
            MultiWorkTimeDto previous = this.multiWorkTimes.get(i - 1);
            MultiWorkTimeDto.checkTime(current, previous, i + 1);
        }
    }

    public void checkMultiOvertime() {
        if (CollectionUtils.isEmpty(this.getMultiOvertime()) || !DateTypeEnum.DATE_TYP_1.getIndex().equals(this.getDateType())) {
            return;
        }
        List<MultiOvertimeDto> multiOvertimeDtoList = this.getMultiOvertime();
        multiOvertimeDtoList.sort(Comparator.comparing(MultiOvertimeDto::doGetRealOvertimeStartTime));
        for (MultiOvertimeDto overtimeDto : multiOvertimeDtoList) {
            for (MultiWorkTimeDto workTimeDto : this.multiWorkTimes) {
                if (overtimeDto.doGetRealOvertimeStartTime() < workTimeDto.doGetRealEndTime()
                        && overtimeDto.doGetRealOvertimeEndTime() > workTimeDto.doGetRealStartTime()) {
                    throw new ServerException(MessageHandler.getMessage("caidao.exception.error_202821", WebUtil.getRequest()));
                }
            }
        }
    }
}
