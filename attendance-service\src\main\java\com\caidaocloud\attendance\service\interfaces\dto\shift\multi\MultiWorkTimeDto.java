package com.caidaocloud.attendance.service.interfaces.dto.shift.multi;

import com.caidaocloud.attendance.core.wa.dto.shift.MultiWorkTimeBaseDto;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.WebUtil;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel("班次设置-多段班时间设置信息DTO")
public class MultiWorkTimeDto extends MultiWorkTimeBaseDto {
    public void doSetWorkTime() {
        if (this.getEndTime() == null || this.getStartTime() == null) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201912", WebUtil.getRequest()));
        }
        Integer wStart = this.doGetRealStartTime();
        Integer wEnd = this.doGetRealEndTime();

        if (ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(this.getStartTimeBelong())
                && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.getEndTimeBelong())) {
            this.setIsNight(Boolean.TRUE);
        } else {
            this.setIsNight(Boolean.FALSE);
        }
        Integer workTime = wEnd - wStart;
        this.setWorkTotalTime(workTime);
    }

    public static void checkTime(MultiWorkTimeDto dto, int paragraph) {
        if (dto.doGetRealStartTime().equals(dto.doGetRealEndTime())) {
            throw new ServerException(String.format(MessageHandler.getMessage("caidao.exception.error_202730", WebUtil.getRequest()), paragraph));
        }
        if (dto.doGetRealOnDutyStartTime() > dto.doGetRealOnDutyEndTime()) {
            throw new ServerException(String.format(MessageHandler.getMessage("caidao.exception.error_202740", WebUtil.getRequest()), paragraph));
        }
        if (dto.doGetRealOffDutyStartTime() > dto.doGetRealOffDutyEndTime()) {
            throw new ServerException(String.format(MessageHandler.getMessage("caidao.exception.error_202750", WebUtil.getRequest()), paragraph));
        }
        if (dto.doGetRealOffDutyStartTime() < dto.doGetRealOnDutyEndTime()) {
            throw new ServerException(String.format(MessageHandler.getMessage("caidao.exception.error_202760", WebUtil.getRequest()), paragraph));
        }
    }

    public static void checkTime(MultiWorkTimeDto current, MultiWorkTimeDto previous, int currentParagraph) {
        if (current.doGetRealStartTime() < previous.doGetRealEndTime()) {
            throw new ServerException(String.format(MessageHandler.getMessage("caidao.exception.error_202770", WebUtil.getRequest()),
                    currentParagraph, currentParagraph - 1));
        }
        if (current.doGetRealOnDutyStartTime() < previous.doGetRealOffDutyEndTime()) {
            throw new ServerException(String.format(MessageHandler.getMessage("caidao.exception.error_202780", WebUtil.getRequest()),
                    currentParagraph, currentParagraph - 1));
        }
    }

    public void doInitOnDutyTime() {
        // TODO 前一日
        if (ShiftTimeBelongTypeEnum.PRE_DAY.getIndex().equals(this.getOnDutyStartTimeBelong())) {
            Integer onDutyStartTime = this.getOnDutyStartTime() - 1440;
            this.setOnDutyStartTime(onDutyStartTime);
            this.setOnDutyStartTimeBelong(ShiftTimeBelongTypeEnum.TODAY.getIndex());
        }
    }

    public void doInitOnDutyTimeForView() {
        int onDutyStartTime = this.getOnDutyStartTime();
        if (onDutyStartTime >= 0) {
            return;
        }
        onDutyStartTime += 1440;
        this.setOnDutyStartTime(onDutyStartTime);
        this.setOnDutyStartTimeBelong(ShiftTimeBelongTypeEnum.PRE_DAY.getIndex());
    }
}
