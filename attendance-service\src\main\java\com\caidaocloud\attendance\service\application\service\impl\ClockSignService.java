package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.ListTool;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.dto.EmpOverInfo;
import com.caidaocloud.attendance.service.application.dto.clock.ClockAnalyseDataCacheDto;
import com.caidaocloud.attendance.service.application.service.*;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpParseGroup;
import com.caidaocloud.attendance.service.interfaces.dto.BatchClockAnalyseDto;
import com.caidaocloud.attendance.service.interfaces.dto.BatchClockAnalyseResultDto;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ClockSignService implements IClockSignService {
    @Autowired
    private ClockAnalyseService clockAnalyseService;
    @Autowired
    private IRegisterRecordService registerRecordService;
    @Autowired
    private IScheduleQueryService scheduleQueryService;
    @Autowired
    private ILeaveApplyService leaveApplyService;
    @Autowired
    private IOvertimeApplyService overtimeApplyService;

    /**
     * 分析打卡记录
     *
     * @param belongOrgId
     * @param empIds
     * @param date
     * @param type
     */
    @Override
    public void analyseRegisterRecord(String belongOrgId, List<Long> empIds, Long date, Integer type) {
        if (CollectionUtils.isEmpty(empIds)) {
            log.warn("ClockSignService.analyseRegisterRecord fail msg: emp empty");
            return;
        }
        if (StringUtils.isBlank(belongOrgId)) {
            belongOrgId = UserContext.getTenantId();
        }
        List<List<Long>> empIdLists = ListTool.split(empIds, 300);
        for (List<Long> empIdList : empIdLists) {
            try {
                ClockAnalyseDataCacheDto dataCacheDto = ClockAnalyseDataCacheDto.doBuild();
                clockAnalyseService.analyseRegisterRecord(belongOrgId, empIdList, date, type, dataCacheDto);
            } catch (Exception e) {
                log.error("ClockSignService.analyseRegisterRecord fail failDate={}, failEmp={}, msg={}",
                        date, FastjsonUtil.toJsonStr(empIdList), e.getMessage(), e);
            }
        }
    }

    @Override
    public BatchClockAnalyseResultDto analyseByDateRange(BatchClockAnalyseDto dto) {
        long startTime = System.currentTimeMillis();
        log.info("ClockSignService.analyseByDateRange begin belongOrgId={} startDate={} endDate={} empIds={}",
                dto.getBelongOrgId(), dto.getStartDate(), dto.getEndDate(),
                CollectionUtils.isEmpty(dto.getEmpIds()) ? "auto" : dto.getEmpIds().size());

        dto.validateAnalyseParams();
        dto.doInitDate();
        dto.validateDateRange();

        // 获取有效员工ID列表
        List<Long> empIds = getValidEmpIds(dto.getBelongOrgId(), dto.getStartDate(), dto.getEndDate(), dto.getEmpIds());
        if (CollectionUtils.isEmpty(empIds)) {
            log.warn("ClockSignService.analyseByDateRange no valid employees found");
            return BatchClockAnalyseResultDto.doBuild();
        }

        log.info("ClockSignService.analyseByDateRange processing {} employees", empIds.size());

        // 分批处理员工数据
        List<List<Long>> empIdBatches = ListTool.split(empIds, 300);
        int successBatches = 0;
        int failedBatches = 0;

        for (int i = 0; i < empIdBatches.size(); i++) {
            List<Long> empIdBatch = empIdBatches.get(i);
            try {
                log.info("Processing batch {}/{} with {} employees", i + 1, empIdBatches.size(), empIdBatch.size());
                processEmpBatch(dto.getBelongOrgId(), dto.getStartDate(), dto.getEndDate(), empIdBatch, dto.getType());
                successBatches++;
            } catch (Exception e) {
                failedBatches++;
                log.error("Failed to process batch {}/{}, empCount={}, error={}",
                        i + 1, empIdBatches.size(), empIdBatch.size(), e.getMessage(), e);
            }
        }

        long duration = System.currentTimeMillis() - startTime;
        log.info("ClockSignService.analyseByDateRange completed: totalEmps={}, successBatches={}, failedBatches={}, duration={}ms",
                empIds.size(), successBatches, failedBatches, duration);

        return BatchClockAnalyseResultDto.doBuild(empIds);
    }

    private List<Long> getValidEmpIds(String belongOrgId, Long startDate, Long endDate, List<Long> empIds) {
        return CollectionUtils.isEmpty(empIds)
                ? registerRecordService.getRegEmpIdList(belongOrgId, startDate, endDate + 86399)
                : empIds;
    }

    public void processEmpBatch(String belongOrgId, Long startDate, Long endDate, List<Long> empIdList, Integer type) {
        long batchStartTime = System.currentTimeMillis();
        log.info("Processing employee batch: {} employees from {} to {}", empIdList.size(), startDate, endDate);

        List<WaRegisterRecordDo> waRegList = null;
        Map<String, WaShiftDo> empShiftDoMap = null;
        List<EmpParseGroup> empParseList = null;
        ClockAnalyseDataCacheDto dataCacheDto = null;

        try {
            Long minDate = DateUtil.addDate(startDate * 1000, -1);
            Long maxDate = DateUtil.addDate(endDate * 1000, 1);

            // 查询打卡记录
            waRegList = clockAnalyseService.getRegisterRecordList(belongOrgId, empIdList, minDate, maxDate, type);
            if (CollectionUtils.isEmpty(waRegList)) {
                log.warn("No register records found for {} employees in date range", empIdList.size());
                return;
            }
            log.info("Found {} register records for batch", waRegList.size());

            // 查询排班信息
            empShiftDoMap = scheduleQueryService.getEmpCalendarShiftMap(belongOrgId, minDate, maxDate, empIdList);
            if (MapUtils.isEmpty(empShiftDoMap)) {
                log.warn("No shift schedules found for {} employees in date range", empIdList.size());
                return;
            }
            log.info("Found {} shift schedules for batch", empShiftDoMap.size());

            // 查询考勤规则
            empParseList = registerRecordService.selectEmpParseGroupListByDateRange(belongOrgId, empIdList, minDate, maxDate);
            log.info("Found {} attendance rules for batch", CollectionUtils.isEmpty(empParseList) ? 0 : empParseList.size());

            // 创建数据缓存对象
            dataCacheDto = ClockAnalyseDataCacheDto.doBuild(waRegList, empShiftDoMap, empParseList);

            // 如果班次上开启了打卡分析联动加班单据时（设置了任意加班）则去查询加班记录
            List<Long> empIdListByOt = clockAnalyseService.getEmpIdListByOt(empShiftDoMap);
            List<EmpOverInfo> empOtList = overtimeApplyService.getEmpDailyOtList(belongOrgId, empIdListByOt,
                    minDate, maxDate + 86399);
            dataCacheDto.initEmpOtList(empOtList);

            // 查询休假数据
            dataCacheDto.initEmpLeaveMaxTimeMap(leaveApplyService.getEmpLeaveDayTimeExtDtoList(belongOrgId, empIdList,
                    minDate, endDate, empShiftDoMap), empShiftDoMap);

            // 按日期循环分析
            long totalDays = (endDate - startDate) / 86400 + 1;
            int processedDays = 0;

            long clockDate = startDate;
            while (clockDate <= endDate) {
                try {
                    processDateAnalyse(belongOrgId, empIdList, clockDate, type, dataCacheDto);
                    processedDays++;
                } catch (Exception e) {
                    log.error("Failed to process date {}: {}", clockDate, e.getMessage(), e);
                }
                clockDate += 86400;
            }

            long batchDuration = System.currentTimeMillis() - batchStartTime;
            log.info("Batch processing completed: {} employees, {}/{} days processed, duration={}ms",
                    empIdList.size(), processedDays, totalDays, batchDuration);

        } catch (Exception e) {
            long batchDuration = System.currentTimeMillis() - batchStartTime;
            log.error("Employee batch processing failed after {}ms for {} employees: {}",
                    batchDuration, empIdList.size(), e.getMessage(), e);
            throw e;
        } finally {
            if (dataCacheDto != null) {
                ClockAnalyseDataCacheDto.doClear(dataCacheDto);
            }
        }
        log.info("Processing employee batch: {} employees from {} to {} End", empIdList.size(), startDate, endDate);
    }

    public void processDateAnalyse(String belongOrgId, List<Long> empIdList, Long clockDate, Integer type,
                                   ClockAnalyseDataCacheDto dataCacheDto) {
        long dateStartTime = System.currentTimeMillis();
        log.debug("Processing date analysis: clockDate={}, empCount={}", clockDate, empIdList.size());

        try {
            clockAnalyseService.analyseRegisterRecord(belongOrgId, empIdList, clockDate, type, dataCacheDto);

            long dateProcessTime = System.currentTimeMillis() - dateStartTime;
            log.debug("Date analysis completed: clockDate={}, empCount={}, duration={}ms",
                    clockDate, empIdList.size(), dateProcessTime);

        } catch (Exception e) {
            long dateProcessTime = System.currentTimeMillis() - dateStartTime;
            log.error("Date analysis failed: clockDate={}, empCount={}, duration={}ms, error={}",
                    clockDate, empIdList.size(), dateProcessTime, e.getMessage(), e);
            throw e;
        }
    }
}
