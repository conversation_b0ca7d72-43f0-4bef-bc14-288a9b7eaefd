package com.caidaocloud.attendance.service.interfaces.vo.shift;

import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiOvertimeDto;
import com.caidaocloud.attendance.core.wa.dto.shift.RestPeriodDto;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ClockTimeLimitDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.MidwayClockTimeDto;
import com.caidaocloud.attendance.service.interfaces.vo.FlexibleVo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.WebUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.postgresql.util.PGobject;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 班次设置详情（一段班）VO
 *
 * <AUTHOR>
 * @Date 2025/1/23
 */
@Data
@ApiModel("班次设置详情（一段班）VO")
public class ShiftVo {
    /******************班次基本配置信息******************/
    // 基本信息
    @ApiModelProperty("班次Id")
    private Integer shiftDefId;
    @ApiModelProperty("日期类型：1、工作日，2休息日，3法定假日")
    private Integer dateType;
    @ApiModelProperty("班次名称")
    private String shiftDefName;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nShiftDefName;
    @ApiModelProperty("班次编码")
    private String shiftDefCode;
    @ApiModelProperty("总工作时长")
    private Integer workTotalTime;
    @ApiModelProperty("代替班次ID, 非工作日出差等业务使用")
    private Integer substituteShift;
    @ApiModelProperty("班次工作时间跨夜标记: true 跨夜、false 不跨夜")
    private Boolean isNight;
    private String belongOrgid;

    // TODO 其他信息（未使用，后续可考虑删除）
    @Deprecated
    private Boolean isDefault;
    @Deprecated
    private Long orgid;
    @Deprecated
    private Long effectStartTime;
    @Deprecated
    private Long effectEndTime;
    @Deprecated
    private Boolean isAdjustWorkHour;
    @Deprecated
    private Object adjustWorkHourJson;
    @Deprecated
    private Boolean isSpecial;
    @Deprecated
    private Integer specialWorkTime;
    @Deprecated
    private Boolean isApplyOvertime;

    // 半天定义规则
    @ApiModelProperty("是否定义半天")
    private Boolean isHalfdayTime;
    @ApiModelProperty("半天定义类型： 1 自定义半天时间点、2 班次工作时长的一半")
    private Integer halfdayType;
    @ApiModelProperty("半天时间")
    private Integer halfdayTime;
    @ApiModelProperty("自定义半天时间点的归属标记：1 当日、2 次日")
    private Integer halfdayTimeBelong;

    // 加班时间规则
    @ApiModelProperty("加班开始时间")
    private Integer overtimeStartTime;
    @ApiModelProperty("加班结束时间")
    private Integer overtimeEndTime;
    @ApiModelProperty("加班开始时间归属标记: 1 当日、2 次日")
    private Integer overtimeStartTimeBelong;
    @ApiModelProperty("加班结束时间归属标记: 1 当日、2 次日")
    private Integer overtimeEndTimeBelong;
    @ApiModelProperty("加班休息时间段(支持多段)")
    private Object overtimeRestPeriods;
    @ApiModelProperty("多段加班")
    private List<MultiOvertimeDto> multiOvertime;
    @ApiModelProperty("任意时间加班")
    private boolean noLimitOtTime;
    @ApiModelProperty("取卡时间范围，单位小时")
    private Integer clockTimeRange;

    // 补卡时间规则
    @ApiModelProperty("补卡时间限制(支持多段)")
    private List<ClockTimeLimitDto> repairRestPeriods;

    @ApiModelProperty("多段打卡时间")
    private Object multiCheckinTimes;

    /******************一段班字段配置信息******************/
    // 班次工作时间设置
    @ApiModelProperty("上班时间")
    private Integer startTime;
    @ApiModelProperty("下班时间")
    private Integer endTime;
    @ApiModelProperty("上班时间归属标记: 1 当日、2 次日")
    private Integer startTimeBelong;
    @ApiModelProperty("下班时间归属标记: 1 当日、2 次日")
    private Integer endTimeBelong;

    // 上下班时间内休息时间规则
    @ApiModelProperty("是否有午休时间：0否，1是")
    private Boolean isNoonRest;
    @ApiModelProperty("休息开始时间")
    private Integer noonRestStart;
    @ApiModelProperty("休息结束时间")
    private Integer noonRestEnd;
    @ApiModelProperty("休息开始时间归属标记：1 当日、2 次日")
    private Integer noonRestStartBelong;
    @ApiModelProperty("休息结束时间归属标记：1 当日、2 次日")
    private Integer noonRestEndBelong;
    @ApiModelProperty("休息时间段(支持多段)")
    private Object restPeriods;
    @ApiModelProperty("总休息时长")
    private Integer restTotalTime;
    @ApiModelProperty("休息时间描述")
    private String restTimeDesc;

    // 打卡规则
    @ApiModelProperty("上班打卡开始时间")
    private Integer onDutyStartTime;
    @ApiModelProperty("上班打卡截止时间")
    private Integer onDutyEndTime;
    @ApiModelProperty("下班打卡开始时间")
    private Integer offDutyStartTime;
    @ApiModelProperty("下班打卡截止时间")
    private Integer offDutyEndTime;
    @ApiModelProperty("上班打卡开始时间归属标记：1 当日、2 次日、3 前日")
    private Integer onDutyStartTimeBelong;
    @ApiModelProperty("上班打卡截止时间归属标记：1 当日、2 次日")
    private Integer onDutyEndTimeBelong;
    @ApiModelProperty("下班打卡开始时间归属标记：1 当日、2 次日")
    private Integer offDutyStartTimeBelong;
    @ApiModelProperty("下班打卡截止时间归属标记：1 当日、2 次日")
    private Integer offDutyEndTimeBelong;
    @ApiModelProperty("中途打卡时间段")
    private List<MidwayClockTimeDto> midwayClockTimes;

    // TODO 弹性打卡设置（老版字段，不再使用，后续可考虑删除）
    @Deprecated
    private Boolean isFlexibleWork;
    @Deprecated
    private Integer flexibleOnDutyStartTime;
    @Deprecated
    private Integer flexibleOnDutyEndTime;
    @Deprecated
    private Integer flexibleOffDutyStartTime;
    @Deprecated
    private Integer flexibleOffDutyEndTime;
    @Deprecated
    private Integer flexibleWorkType;

    // 弹性打卡设置（新版字段）
    @ApiModelProperty("弹性规则 1:晚到晚走，早到早走 2:下班晚走，第二天早到(仅一段班使用)")
    private Integer flexibleWorkRule;
    @ApiModelProperty("上班最多可晚到多少小时(仅一段班使用)")
    private BigDecimal flexibleWorkLate;
    @ApiModelProperty("下班最多可早走多少小时(仅一段班使用)")
    private BigDecimal flexibleWorkEarly;
    @ApiModelProperty("下班晚走,第二天晚到(仅一段班使用)")
    private List<FlexibleVo> flexibleRules;
    @ApiModelProperty("下班晚走,第二天晚到(仅一段班使用)")
    private String flexibleOffWorkRule;
    @ApiModelProperty("弹性分析开关 1:关闭 2:开启(仅一段班使用)")
    private Integer flexibleShiftSwitch;

    public static void doSetRestPeriods(WaShiftDef shiftDef) {
        List<RestPeriodDto> allRestPeriodList = new ArrayList<>();
        if (shiftDef.getNoonRestStart() != null && shiftDef.getNoonRestEnd() != null) {
            RestPeriodDto restPeriodDto = new RestPeriodDto();
            restPeriodDto.setNoonRestStart(shiftDef.getNoonRestStart());
            restPeriodDto.setNoonRestEnd(shiftDef.getNoonRestEnd());
            restPeriodDto.setNoonRestStartBelong(shiftDef.getNoonRestStartBelong());
            restPeriodDto.setNoonRestEndBelong(shiftDef.getNoonRestEndBelong());
            allRestPeriodList.add(restPeriodDto);
        }
        try {
            if (shiftDef.getRestPeriods() != null) {
                PGobject pGobject = (PGobject) shiftDef.getRestPeriods();
                List<RestPeriodDto> restPeriods = new ObjectMapper().readValue(pGobject.getValue(), new TypeReference<List<RestPeriodDto>>() {
                });
                if (CollectionUtils.isNotEmpty(restPeriods)) {
                    restPeriods.forEach(it -> it.doInitTimeBelong(shiftDef));
                    allRestPeriodList.addAll(restPeriods);
                }
            }
            shiftDef.setRestPeriods(allRestPeriodList);
        } catch (Exception ex) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_90014", WebUtil.getRequest()));
        }
    }

    public static void doInitOnDutyTimeForView(WaShiftDef shiftDef) {
        int onDutyStartTime = shiftDef.getOnDutyStartTime();
        if (onDutyStartTime >= 0) {
            return;
        }
        onDutyStartTime += 1440;
        shiftDef.setOnDutyStartTime(onDutyStartTime);
        shiftDef.setOnDutyStartTimeBelong(ShiftTimeBelongTypeEnum.PRE_DAY.getIndex());
    }
}
