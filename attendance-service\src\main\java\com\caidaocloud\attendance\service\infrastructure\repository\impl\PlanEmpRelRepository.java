package com.caidaocloud.attendance.service.infrastructure.repository.impl;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaPlanEmpRelMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpClockPlanRel;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaPlanEmpRelPo;
import com.caidaocloud.attendance.service.domain.entity.WaPlanEmpRel;
import com.caidaocloud.attendance.service.domain.repository.IPlanEmpRelRepository;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.dto.EmpInfoKeyValue;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class PlanEmpRelRepository implements IPlanEmpRelRepository {

    @Resource
    private WaPlanEmpRelMapper waPlanEmpRelMapper;

    @Override
    public int saveBatch(List<WaPlanEmpRel> list) {
        List<WaPlanEmpRelPo> records = ObjectConverter.convertList(list, WaPlanEmpRelPo.class);
        return waPlanEmpRelMapper.saveBatch(records);
    }

    @Override
    public void deleteByPlanId(Long planId, String belongOrgId) {
        waPlanEmpRelMapper.deleteByPlanId(planId, belongOrgId);
    }

    @Override
    public void deleteByIds(String tenantId, List<Long> ids) {
        waPlanEmpRelMapper.deleteBatch(tenantId, ids);
    }

    @Override
    public List<WaPlanEmpRel> getByPlanId(Long planId) {
        List<WaPlanEmpRelPo> list = waPlanEmpRelMapper.queryByPlanId(planId);
        return ObjectConverter.convertList(list, WaPlanEmpRel.class);
    }

    @Override
    public List<EmpInfoKeyValue> getEmployeesByPlanId(String belongOrgId, Long planId) {
        return waPlanEmpRelMapper.queryEmployeesByPlanId(belongOrgId, planId);
    }

    @Override
    public List<EmpInfoKeyValue> getEmployeesByPlanIds(String belongOrgId, List<Long> planIds) {
        return waPlanEmpRelMapper.queryEmployeesByPlanIds(belongOrgId, planIds);
    }

    @Override
    public void updatePlanIdByParams(Long planId, Long userId, Long corpId, String belongOrgId, List<Long> empIds) {
        waPlanEmpRelMapper.updateRelPlanIdByParams(planId, userId, corpId, belongOrgId, empIds);
    }

    @Override
    public EmpClockPlanRel getPlanEmpRelById(Long id, String belongOrgId) {
        return waPlanEmpRelMapper.queryPlanEmpRelById(id, belongOrgId);
    }

    @Override
    public List<EmpClockPlanRel> getEmpClockPlanByPeriod(Long empId, Long id, String belongOrgId, Long startTime, Long endTime) {
        return waPlanEmpRelMapper.queryEmpClockPlanByPeriod(empId, id, belongOrgId, startTime, endTime);
    }

    @Override
    public void save(WaPlanEmpRelPo waPlanEmpRelPo) {
        waPlanEmpRelMapper.insert(waPlanEmpRelPo);
    }

    @Override
    public void update(WaPlanEmpRelPo waPlanEmpRelPo) {
        waPlanEmpRelMapper.updateByPrimaryKeySelective(waPlanEmpRelPo);
    }

    @Override
    public AttendancePageResult<EmpClockPlanRel> getEmpClockPlanList(AttendanceBasePage basePage, String belongOrgId,
                                                                     Long planId, String filter, String effectiveStatus) {
        PageBean pageBean = PageUtil.getPageBean(basePage);
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        if (StringUtils.isNotBlank(basePage.getKeywords())) {
            basePage.setKeywords("('"+org.apache.commons.lang3.StringUtils.join(basePage.getKeywords().split(" "), "','")+"')");
        }
        PageList<EmpClockPlanRel> pageList = waPlanEmpRelMapper.queryEmpClockPlanList(myPageBounds, belongOrgId,
                planId, filter, basePage.getKeywords(), effectiveStatus, DateUtil.getOnlyDate());
        return new AttendancePageResult<>(pageList, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
    }

    @Override
    public EmpClockPlanRel getPlanEmpRelByEmpIdAndPeriod(String tenantId, Long empId, Long time) {
        return waPlanEmpRelMapper.queryPlanEmpRelByEmpIdAndPeriod(tenantId, empId, time);
    }

    @Override
    public List<WaPlanEmpRelPo> getByIds(String tenantId, List<Long> ids) {
        return waPlanEmpRelMapper.queryEmpPlanByIds(tenantId, ids);
    }
}
