package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.ICompensatoryPoolRepository;
import com.caidaocloud.attendance.service.domain.repository.IEmpCompensatoryQuotaRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryQuotaPo;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.val;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompensatoryPoolDo {

    private boolean generated = false;

    private String period;

    private String empId;

    private BigDecimal amount;

    private BigDecimal settlement;

    private BigDecimal lastAmount;

    private BigDecimal compensatoryTotal;

    private static final BigDecimal FORTY = new BigDecimal(40);

    private static final BigDecimal ZERO = new BigDecimal(40);

    private static ICompensatoryPoolRepository repository(){
        return SpringUtil.getBean(ICompensatoryPoolRepository.class);
    }

    private static <T> T repository(Class<T> clazz){
        return SpringUtil.getBean(clazz);
    }

    public static List<CompensatoryPoolDo> list(String period, List<String> empIds){
        List<CompensatoryPoolDo> generated = repository().list(period, empIds);
        return Sequences.sequence(empIds).filter(empId->!generated.stream().anyMatch(it->it.getEmpId().equals(empId)))
                .map(empId->new CompensatoryPoolDo(false, period, empId, new BigDecimal(0), new BigDecimal(0),new BigDecimal(0), new BigDecimal(0)))
                .join(generated).toList();
    }

    public static List<CompensatoryPoolDo> generate(String tenantId, String period, List<String> empIds, long startTime, long endTime){
        List<WaEmpCompensatoryQuotaPo> empCompensatoryList = repository(IEmpCompensatoryQuotaRepository.class)
                .groupCompensatoryQuotaByEmpIdsAndTime(tenantId, empIds, startTime, endTime);

        List<WaEmpCompensatoryQuotaPo> empManualCompensatoryList = repository(IEmpCompensatoryQuotaRepository.class).groupManualCompensatoryQuotaByEmpIds(tenantId, empIds);
        Map<String, BigDecimal> empCompensatorySummaries = Maps.map();
        Map<String, BigDecimal> empManualCompensatorySummaries = Maps.map();
        for(WaEmpCompensatoryQuotaPo empCompensatory : empCompensatoryList){
            val left = empCompensatory.getQuotaDay() + empCompensatory.getAdjustQuotaDay() - empCompensatory.getInTransitQuota() - empCompensatory.getUsedDay();
            empCompensatorySummaries.put(String.valueOf(empCompensatory.getEmpId()), new BigDecimal(left).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP));
        }
        for(WaEmpCompensatoryQuotaPo empCompensatory : empManualCompensatoryList){
            val left = empCompensatory.getQuotaDay() + empCompensatory.getAdjustQuotaDay() - empCompensatory.getInTransitQuota() - empCompensatory.getUsedDay();
            empManualCompensatorySummaries.put(String.valueOf(empCompensatory.getEmpId()), new BigDecimal(left).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP));
        }
        String year = period.substring(0, 4);
        String month = period.substring(4);
        if("01".equals(month)){
            year = String.valueOf(Integer.valueOf(year) - 1);
        }
        month = String.valueOf(Integer.valueOf(month) - 1);
        if("0".equals(month)){
            month = "12";
        }
        if(month.length() == 1){
            month = "0"+month;
        }
        val previousPeriod = year + month;
        val previousPool = list(previousPeriod, empIds);
        List<CompensatoryPoolDo> result = empIds.stream().map(empId->{
            BigDecimal previousLeft = previousPool.stream().filter(previous->previous.getEmpId().equals(empId))
                    .findFirst().map(it->it.getAmount()).orElse(new BigDecimal(0));
            BigDecimal compensatoryTotal = new BigDecimal(0);
            if(empCompensatorySummaries.containsKey(empId)){
                compensatoryTotal = empCompensatorySummaries.get(empId);
            }

            BigDecimal manualCompensatory = new BigDecimal(0);
            if(empManualCompensatorySummaries.containsKey(empId)){
                manualCompensatory = empManualCompensatorySummaries.get(empId);
            }

            if(previousLeft.compareTo(ZERO) == 0){
                if(manualCompensatory.compareTo(FORTY) >= 0){
                    return new CompensatoryPoolDo( true, period, empId,
                            compensatoryTotal,
                            manualCompensatory,
                            previousLeft,
                            compensatoryTotal);
                }else if(manualCompensatory.compareTo(ZERO) > 0){
                    BigDecimal settlement = compensatoryTotal.subtract(new BigDecimal(40).subtract(manualCompensatory));
                    if(settlement.compareTo(ZERO) < 0){
                        settlement = ZERO;
                    }
                    BigDecimal amount = compensatoryTotal.add(manualCompensatory);
                    if(amount.compareTo(FORTY) > 0){
                        amount = FORTY;
                    }

                    return new CompensatoryPoolDo( true, period, empId,
                            settlement,
                            amount,
                            previousLeft,
                            compensatoryTotal);
                }else{
                    BigDecimal amount = previousLeft.add(compensatoryTotal);
                    return new CompensatoryPoolDo( true, period, empId,
                            amount.compareTo(FORTY) >=0 ? FORTY : amount,
                            amount.compareTo(FORTY) >=0 ? amount.subtract(FORTY) : ZERO,
                            previousLeft,
                            compensatoryTotal);
                }
            }else if(previousLeft.compareTo(FORTY) >= 0){
                return new CompensatoryPoolDo( true, period, empId,
                        previousLeft,
                        compensatoryTotal,
                        previousLeft,
                        compensatoryTotal);
            }else{
                BigDecimal amount = previousLeft.add(compensatoryTotal);
                return new CompensatoryPoolDo( true, period, empId,
                        amount.compareTo(FORTY) >=0 ? FORTY : amount,
                        amount.compareTo(FORTY) >=0 ? amount.subtract(FORTY) : ZERO,
                        previousLeft,
                        compensatoryTotal);
            }
        }).collect(Collectors.toList());
        repository().save(period, result);
        return result;
    }

}
