package com.caidaocloud.attendance.service.infrastructure.feign.callback;

import com.caidaocloud.attendance.service.infrastructure.feign.WfmFeignClient;
import com.caidaocloud.attendance.service.infrastructure.feign.wfm.*;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class WfmFeignClientFallBack implements WfmFeignClient {

    @Override
    public Result<List<WfmProcessTreeDetailDto>> getProcessTree(WfmProcessQueryDto wfmProcessQueryDto) {
        return Result.fail();
    }

    @Override
    public Result<List<WfmKeyValuePair>> getAbnormalWorkHoursTypeKeyValueList() {
        return Result.fail();
    }

    @Override
    public Result<List<GetSpecifiedProcessDto>> getProcessesStartedToday() {
        return Result.fail();
    }

    @Override
    public Result<OrgScopeProductOrgIdsDto> getAuthedProductsOrgList(String orgAuthCode, String userId) {
        return Result.fail();
    }

//    @Override
//    public Result<OrgScopeProductIdsDto> getAuthedProductIdsByDataScope(WfmOrgDataScopeEnum orgAuthCode) {
//        return Result.fail();
//    }

    @Override
    public Result<OrgScopeProductIdsDto> getAuthedProductIds(String orgAuthCode, String userId) {
        return Result.fail();
    }

    @Override
    public Result<OrgScopeProcessIdsDto> getAuthedProcessIdsByOrgScope(WfmOrgDataScopeEnum orgAuthCode) {
        return Result.fail();
    }

    @Override
    public Result<SchedulingAndProcessStatusVo> fetchSchedulingAndProcessStatus(SchedulingAndProcessStatusDto schedulingAndProcessStatusDto) {
        return Result.fail();
    }
}
