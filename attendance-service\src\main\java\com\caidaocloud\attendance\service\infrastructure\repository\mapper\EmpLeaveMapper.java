package com.caidaocloud.attendance.service.infrastructure.repository.mapper;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveDo;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveDaytimeDo;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveDetailDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpFixLeaveTypePo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface EmpLeaveMapper {
    PageList<WaLeaveDaytimeDo> selectDayTimePageList(@Param("myPageBounds") MyPageBounds myPageBounds,
                                          @Param("tenantId") String tenantId,
                                          @Param("startTime") Long startTime,
                                          @Param("endTime") Long endTime,
                                          @Param("statusList") List<Integer> statusList);

    PageList<WaEmpLeaveDo> getEmpLeaveList(@Param("myPageBounds") MyPageBounds myPageBounds,
                                           @Param("belongOrgId") String belongOrgId,
                                           @Param("empid") Long empid,
                                           @Param("status") Integer status,
                                           @Param("wfFuncId") Integer wfFuncId,
                                           @Param("keywords") String keywords);

    List<WaLeaveDaytimeDo> getEmpLeaveDaytimeList(@Param("empid") Long empid,
                                                  @Param("daytime") Long daytime,
                                                  @Param("statusList") List<Integer> statusList);

    List<WaLeaveDaytimeDo> getEmpLeaveDay(@Param("empid") Long empid,
                                          @Param("startDate") Long startDate,
                                          @Param("endDate") Long endDate,
                                          @Param("statusList") List<Integer> statusList);

    List<WaLeaveDetailDo> listLeaveDetail(@Param("startDate") Long startDate,
                                          @Param("endDate") Long endDate,
                                          @Param("empIdList") List<Long> empIdList);

    List<EmpFixLeaveTypePo> getEmpFixLeaveTypes(Long empid, String belongOrgId);

    void updateHomeLeaveInfo(@Param("homeLeaveType") String homeLeaveType,
                             @Param("marriageStatus") String marriageStatus,
                             @Param("leaveId") Integer leaveId);

    Map getHomeLeaveTypeAndMarriageStatus(@Param("leaveId") Long leaveId);

    List<Map> getEmpLeaveByEmpid(Map params);

    List<Map> selectEmpLeaveDayTimeList(Map params);

    List<Map<String, String>> getEmpNamesByLeaveIds(List<Integer> list);

    List<Map> queryEmpLeaveDayTimeByEmpId(Map params);
}
