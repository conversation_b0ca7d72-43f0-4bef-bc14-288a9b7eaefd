<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpLeaveMapper">
    <select id="selectDayTimePageList" resultType="com.caidaocloud.attendance.service.domain.entity.WaLeaveDaytimeDo">
        select wel.leave_id,
        wel.empid,
        wel.status,
        wel.crttime AS create_time,
        wld.leave_daytime_id,
        wld.leave_date,
        wld.shalf_day,
        wld.ehalf_day,
        wld.start_time,
        wld.end_time,
        wld.period_type,
        wld.time_unit,
        wld.time_duration,
        wld.date_type,
        wld.cancel_time_duration
        from attendance.wa_emp_leave wel
        join attendance.wa_leave_daytime wld on wld.leave_id = wel.leave_id
        where wel.tenant_id = #{tenantId}
        and wel.status in <foreach collection="statusList" item="item" open="(" separator="," close=")">#{item}</foreach>
        and wld.leave_date between #{startTime} and #{endTime}
        and wld.time_duration > 0
        and wld.time_duration != wld.cancel_time_duration
        order by wld.leave_daytime_id
    </select>

    <select id="getEmpLeaveList" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveDo">
        select wel.leave_id as "leaveId",
               wlt.leave_name         as "leaveName",
               wel.crttime            as "applyTime",
               wel.time_slot          as "timeSlot",
                CASE
                WHEN (welt.period_type = 1 or welt.period_type = 4) THEN to_char(
                to_timestamp(coalesce(welt.shift_start_time, welt.start_time)), 'yyyy-MM-dd')
                ELSE
                to_char(to_timestamp(coalesce(welt.shift_start_time, welt.start_time)),
                'yyyy-MM-dd hh24:mi') END     AS "startTimeTxt",

                CASE
                WHEN (welt.period_type = 1 or welt.period_type = 4) THEN to_char(
                to_timestamp(coalesce(welt.shift_end_time, welt.end_time)),
                'yyyy-MM-dd')
                ELSE
                to_char(to_timestamp(coalesce(welt.shift_end_time, welt.end_time)),
                'yyyy-MM-dd hh24:mi') END     AS "endTimeTxt",
               wel.status,
               CASE
                   WHEN wel.status = 0
                       THEN '暂存'
                   WHEN wel.status = 1
                       THEN '审批中'
                   WHEN wel.status = 2
                       THEN '已通过'
                   WHEN wel.status = 3
                       THEN '已拒绝'
                   WHEN wel.status = 4
                       THEN '已作废'
                   WHEN wel.status = 5
                       THEN '已退回'
                   WHEN wel.status = 9
                       THEN '已撤销' END AS "statusName"
               <if test=" wfFuncId != null">
                   ,fp.proc_inst_id        as "procInstId",
                   fp.business_key        as "businessKey"
               </if>
        from wa_emp_leave wel
                 join wa_emp_leave_time welt ON wel.leave_id = welt.leave_id
                 join wa_leave_type wlt on wel.leave_type_id = wlt.leave_type_id
                 <if test="wfFuncId != null">
                     left join lateral (select proc_inst_id, start_time, business_key
                     from wf_func_process wfp
                     JOIN wf_config wfc ON wfp.wf_config_id = wfc.wf_config_id
                     where wfc.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
                     and business_key = wel.leave_id || '_' || #{wfFuncId}
                     order by start_time desc
                     limit 1) fp on true
                 </if>
        where wlt.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
          and wel.empid = #{empid}
          <if test=" status != null">
              and wel.status = #{status}
          </if>
          <if test="keywords != null and keywords != ''">
              and wlt.leave_name like concat('%', #{keywords}, '%')
          </if>
        order by wel.crttime desc
    </select>

    <select id="getEmpLeaveDaytimeList" resultType="com.caidaocloud.attendance.service.domain.entity.WaLeaveDaytimeDo">
      select ltype.leave_name as "leaveName",
             ltype.i18n_leave_name as "i18nLeaveName",
             lt.status        as "status",
             lt.leave_id      as "leaveId",
             ld.leave_date    as "leaveDate",
             ld.period_type   as "periodType",
             ld.shalf_day     as "shalfDay",
             ld.ehalf_day     as "ehalfDay",
             ld.start_time    as "startTime",
             ld.end_time      as "endTime",
             ld.time_duration as "timeDuration",
             ld.time_unit     as "timeUnit",
             ld.use_shift_def_id as "useShiftDefId",
             lt.crttime       as "applyTime",
             welt.shift_start_time as "shiftStartTime",
             welt.shift_end_time   as "shiftEndTime",
             def.leave_type_def_code as "leaveTypeCode",
             ld.cancel_time_duration as "cancelTimeDuration"
      from wa_leave_daytime ld
             JOIN wa_emp_leave lt on lt.leave_id = ld.leave_id
             JOIN wa_leave_type ltype on ltype.leave_type_id = lt.leave_type_id
             JOIN wa_emp_leave_time welt on welt.leave_id = ld.leave_id
             JOIN wa_leave_type_def def on leave_type_def_id = ltype.leave_type
      where lt.empid = #{empid} and ld.leave_date = #{daytime} and ld.time_duration!=0 and ltype.is_emp_show=true
          and lt.status IN <foreach collection="statusList" open="(" separator="," close=")" item="item">#{item}</foreach>
    </select>

    <select id="getEmpLeaveDay" resultType="com.caidaocloud.attendance.service.domain.entity.WaLeaveDaytimeDo">
        select
        ltype.leave_name as "leaveName",
        lt.status        as "status",
        lt.leave_id      as "leaveId",
        ld.leave_date    as "leaveDate",
        ld.period_type   as "periodType",
        ld.shalf_day     as "shalfDay",
        ld.ehalf_day     as "ehalfDay",
        ld.start_time    as "startTime",
        ld.end_time      as "endTime",
        ld.time_duration as "timeDuration",
        ld.time_unit     as "timeUnit",
        lt.crttime       as "applyTime"
        from wa_leave_daytime ld
        JOIN wa_emp_leave lt on lt.leave_id = ld.leave_id
        JOIN wa_leave_type ltype on ltype.leave_type_id = lt.leave_type_id
        where lt.empid = #{empid} and ld.leave_date between #{startDate} and #{endDate} and ld.time_duration!=0
        and lt.status IN <foreach collection="statusList" open="(" separator="," close=")" item="item">#{item}</foreach>
    </select>



    <select id="listLeaveDetail" resultType="com.caidaocloud.attendance.service.domain.entity.WaLeaveDetailDo">
        select
        ld.leave_date    as "leaveDate",
        ld.time_duration as "timeDuration",
        ld.time_unit     as "timeUnit",
        lt.empid         as "empId",
        ld.cancel_time_duration as "cancelTimeDuration"
        from wa_leave_daytime ld
        JOIN wa_emp_leave lt on lt.leave_id = ld.leave_id
        JOIN wa_leave_type ltype on ltype.leave_type_id = lt.leave_type_id
        where lt.empid IN <foreach collection="empIdList" open="(" separator="," close=")" item="empId">#{empId}</foreach>
        and ld.leave_date between #{startDate} and #{endDate} and ld.time_duration!=0
        and lt.status = 2
    </select>








    <select id="getEmpFixLeaveTypes" resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpFixLeaveTypePo">
        SELECT lt.leave_type_id,
        lt.leave_name as leave_type_name
        FROM wa_emp_group_view egv
        JOIN wa_group wg ON egv.wa_group_id = wg.wa_group_id
        JOIN wa_leave_type lt ON wg.belong_orgid = lt.belong_orgid AND lt.leave_type_id = ANY(wg.leave_type_ids)
        JOIN sys_emp_info ei ON egv.empid = ei.empid and ei.deleted = 0
        WHERE ei.empid = #{empid} AND lt.belong_orgid = #{belongOrgId,jdbcType=VARCHAR} AND lt.quota_type = 3;
    </select>

    <select id="getLeaveDetailById" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveDo">
        SELECT ei.workno,ei.emp_name            AS "empName",
               lt.leave_name          AS "leaveName",
               welt.start_time        AS "startTime",
               welt.end_time          AS "endTime",
               welt.shift_start_time  AS "shiftStartTime",
               welt.shift_end_time    AS "shiftEndTime",
               welt.shalf_day         AS "shalfDay",
               welt.ehalf_day         AS "ehalfDay",
               welt.period_type       AS "periodType",
               el.time_unit           AS "timeUnit",
               el.total_time_duration AS "totalTimeDuration",
               el.time_slot           AS "timeSlot",
               el.status,
               CASE
                   WHEN el.status = 0
                       THEN '暂存'
                   WHEN el.status = 1
                       THEN '审批中'
                   WHEN el.status = 2
                       THEN '已通过'
                   WHEN el.status = 3
                       THEN '已拒绝'
                   WHEN el.status = 4
                       THEN '已作废'
                   WHEN el.status = 5
                       THEN '已退回'
                   WHEN el.status = 9
                       THEN '已撤销' END AS "statusName",
               el.reason,
               el.revoke_reason       as "revokeReason",
               wf.url                 as "files",
               wf.file_name           as "fileNames", el.crttime,
               ei.hire_date           as "hireDate",
               dic.dict_chn_name      as "workCity",
               co.shortname           as "orgName",
               tt.dict_chn_name       as "marriage",
               lt.leave_type_id       as "leaveType",
               co.full_path           as "fullPath",
               el.leave_status        as "leaveStatus",
               el.updtime             as "updtime"
        FROM wa_emp_leave el
                 JOIN wa_leave_type lt ON el.leave_type_id = lt.leave_type_id
                 JOIN wa_emp_leave_time welt ON welt.leave_id = el.leave_id
                 JOIN sys_emp_info ei ON el.empid = ei.empid
                 LEFT JOIN sys_parm_dict dic ON ei.workplace = dic.dict_id
                 LEFT JOIN wa_leave_file wf ON wf.leave_id = el.leave_id
                 LEFT JOIN sys_corp_org co ON co.orgid = ei.orgid
                 LEFT JOIN sys_emp_privacy va ON va.empid = ei.empid
                 LEFT JOIN sys_parm_dict tt ON va.marriage = tt.dict_id
        WHERE el.leave_id = #{leaveId}
          AND ei.corpid = #{corpid} and ei.deleted = 0
    </select>

    <update id="updateHomeLeaveInfo">
        update wa_emp_leave set home_leave_type = #{homeLeaveType}, marriage_status = #{marriageStatus}
        where leave_id = #{leaveId}
    </update>

    <select id="getHomeLeaveTypeAndMarriageStatus" resultType="java.util.Map">
        select home_leave_type as homeLeaveType, marriage_status as marriageStatus from wa_emp_leave where leave_id = #{leaveId}
    </select>

    <select id="getEmpLeaveByEmpid" parameterType="hashmap" resultType="map">
        SELECT a.leave_id,a.empid,a.time_slot,a.leave_type_id,b.leave_date,b.start_time,b.end_time,b.cancel_time_duration,
        coalesce(b.apply_time_duration, b.time_duration) - COALESCE (b.cancel_time_duration, 0) as time_duration,
        CASE WHEN (before_adjust_time_duration IS NOT NULL AND before_adjust_time_duration > 0) THEN
        COALESCE (before_adjust_time_duration, 0) - COALESCE (b.cancel_time_duration, 0) ELSE before_adjust_time_duration END AS before_adjust_time_duration,
        b.period_type,b.date_type,b.shalf_day,b.ehalf_day,b.time_unit,a.province,a.city,c.leave_type,c.link_outside_sign,d.leave_type_def_code,
        c.is_rest_day,c.is_legal_holiday,a.crttime as "createTime",a.status,b.use_shift_def_id "useShiftDefId"
        <choose>
            <when test="includeInProgress != null and includeInProgress">
                ,coalesce(b.real_date,b.leave_date) as real_date
            </when>
            <otherwise>
                ,b.real_date
            </otherwise>
        </choose>
        from wa_emp_leave a left join wa_leave_daytime b on a.leave_id=b.leave_id
        JOIN wa_leave_type c ON a.leave_type_id = c.leave_type_id
        JOIN wa_leave_type_def d on c.leave_type = d.leave_type_def_id
        <if test="leaveTypeIsNotAnalyze != null">
            and c.is_not_analyze = #{leaveTypeIsNotAnalyze}
        </if>
        left join sys_emp_info e on e.empid=a.empid and e.deleted = 0
        where
              a.status in <foreach collection="approvalStatusList" open="(" separator="," close=")" item="item">#{item}</foreach>
          and e.belong_org_id=#{belongid} and b.time_duration &gt; 0 and b.real_date &gt; 0
        <if test="anyEmpids2 != null ">
            and a.empid = any(${anyEmpids2})
        </if>
        <if test="startDate != null and endDate != null">
            and ((b.real_date BETWEEN  #{startDate} AND #{endDate}) or (b.leave_date BETWEEN  #{startDate} AND #{endDate}))
        </if>
        AND (e.termination_date IS NULL OR coalesce(b.real_date, b.leave_date) <![CDATA[<=]]> e.termination_date)
        order by b.leave_date,b.start_time
    </select>

    <select id="selectEmpLeaveDayTimeList" parameterType="hashmap" resultType="map">
        SELECT a.empid,
               b.leave_date,
               b.start_time,
               b.end_time,
               b.period_type,
               b.date_type,
               b.shalf_day,
               b.ehalf_day,
               b.time_unit,
               b.cancel_time_duration,
               coalesce(b.apply_time_duration, b.time_duration) - COALESCE(b.cancel_time_duration, 0) as time_duration,
               CASE
                   WHEN (before_adjust_time_duration IS NOT NULL AND before_adjust_time_duration > 0) THEN
                       COALESCE(before_adjust_time_duration, 0) - COALESCE(b.cancel_time_duration, 0)
                   ELSE before_adjust_time_duration END                                               as before_adjust_time_duration,
               coalesce(b.real_date, b.leave_date)                                                    as real_date,
               b.use_shift_def_id as "useShiftDefId"
        from wa_emp_leave a
                 join wa_leave_daytime b on a.leave_id = b.leave_id
        where a.tenant_id = #{belongid}
          and a.status in <foreach collection="approvalStatusList" open="(" separator="," close=")" item="item">#{item}</foreach>
          and a.empid in <foreach collection="empIdList" open="(" separator="," close=")" item="item">#{item}</foreach>
          and b.time_duration <![CDATA[>]]> 0
          <if test="startDate != null and endDate != null">
            and b.leave_date BETWEEN  #{startDate} AND #{endDate}
          </if>
          <if test="onlyValid != null and onlyValid = true">
              and b.time_duration != b.cancel_time_duration
          </if>
        order by b.leave_date desc, b.start_time desc
    </select>

    <select id="getEmpNamesByLeaveIds" resultType="map">
        select
            concat(sei.workno, '(', sei.emp_name, ')') as empName,
            wlt.leave_name                             as leaveName
        from wa_emp_leave wel
        join wa_leave_type wlt on wel.leave_type_id = wlt.leave_type_id
        join sys_emp_info sei on sei.empid = wel.empid
        where leave_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryEmpLeaveDayTimeByEmpId" parameterType="hashmap" resultType="map">
        SELECT b.leave_daytime_id,
               a.empid,
               a.leave_type_id,
               b.leave_date,
               coalesce(b.apply_time_duration, b.time_duration) as original_time_duration,
               b.cancel_time_duration,
               coalesce(b.apply_time_duration, b.time_duration) - COALESCE (b.cancel_time_duration, 0) as time_duration,
               b.date_type,
               b.time_unit,
               b.real_date,
               c.leave_type,
               a.status,
               b.real_date,
               c.paid_leave
        from wa_emp_leave a
        left join wa_leave_daytime b on a.leave_id=b.leave_id
        join wa_leave_type c ON a.leave_type_id = c.leave_type_id
        where c.belong_orgid=#{tenantId} and b.time_duration &gt; 0 and b.real_date &gt; 0
        and a.status in
        <foreach collection="statusList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        <if test="anyEmpIds != null ">
            and a.empid = any(${anyEmpIds})
        </if>
        <if test="startDate != null and endDate != null">
            and ((b.real_date BETWEEN  #{startDate} AND #{endDate}) or (b.leave_date BETWEEN  #{startDate} AND #{endDate}))
        </if>
        order by b.leave_date,b.start_time
    </select>
</mapper>