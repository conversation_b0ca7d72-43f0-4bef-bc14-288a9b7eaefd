package com.caidaocloud.attendance.service.application.dto.clock;

import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.wa.dto.EmpOverInfo;
import com.caidaocloud.attendance.core.wa.dto.WaLeaveDaytimeExtDto;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpParseGroup;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.stream.Collectors;

@Data
public class ClockAnalyseDataCacheDto {
    private List<WaRegisterRecordDo> waRegList;
    private Map<String, WaShiftDo> empShiftDoMap;
    private List<EmpParseGroup> empParseList;
    private List<EmpOverInfo> empOtList;
    private Map<String, Long> empLeaveDayTimeMap;// key=empId+"_"+leaveDate, value=Max(leaveEndTime)

    /**
     * 获取指定员工在指定日期的最大休假结束时间
     *
     * @param empId     员工ID
     * @param leaveDate 休假日期
     * @return 最大休假结束时间点，如果没有找到则返回null
     */
    public Long doGetMaxLtEndTime(Long empId, Long leaveDate) {
        if (MapUtils.isEmpty(this.empLeaveDayTimeMap) || empId == null || leaveDate == null) {
            return null;
        }
        return this.empLeaveDayTimeMap.get(String.format("%s_%s", empId, leaveDate));
    }

    /**
     * 初始化员工每日最大休假结束时间映射
     * 按员工ID和休假日期分组，计算每个员工每天的最大休假结束时间点
     *
     * @param leaveDaytimeExtDtoList 休假明细数据列表
     * @param empShiftDoMap
     */
    public void initEmpLeaveMaxTimeMap(List<WaLeaveDaytimeExtDto> leaveDaytimeExtDtoList,
                                       Map<String, WaShiftDo> empShiftDoMap) {
        this.empLeaveDayTimeMap = new HashMap<>();

        if (CollectionUtils.isEmpty(leaveDaytimeExtDtoList)) {
            return;
        }

        // 跨夜班休假特殊处理
        if (MapUtils.isNotEmpty(empShiftDoMap)) {
            List<WaLeaveDaytimeExtDto> newLeaveDaytimeExtDtoList = new ArrayList<>();
            for (WaLeaveDaytimeExtDto daytimeExtDto : leaveDaytimeExtDtoList) {
                Long empId = daytimeExtDto.getEmpId();
                Long leaveDate = daytimeExtDto.getLeaveDate();
                Long preLeaveDate = leaveDate - 86400;

                WaShiftDo shiftDefDo;
                if (null == (shiftDefDo = empShiftDoMap.get(empId + "_" + preLeaveDate))) {
                    continue;
                }

                WaShiftDef shiftDef = ObjectConverter.convert(shiftDefDo, WaShiftDef.class);
                WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);

                boolean crossNight = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType());
                if (!crossNight) {
                    continue;
                }
                long shiftEndTime = leaveDate + shiftWorkTime.getEndTime() * 60;

                Long leaveStartTime = daytimeExtDto.getLeaveStartTime();
                Long leaveEndTime = daytimeExtDto.getLeaveEndTime();

                if (leaveStartTime >= shiftEndTime) {
                    continue;
                }
                if (leaveEndTime <= shiftEndTime) {
                    daytimeExtDto.setLeaveDate(preLeaveDate);
                } else {
                    WaLeaveDaytimeExtDto newLeaveDaytimeExtDto = FastjsonUtil.convertObject(daytimeExtDto, WaLeaveDaytimeExtDto.class);
                    newLeaveDaytimeExtDto.setLeaveStartTime(shiftEndTime);
                    newLeaveDaytimeExtDtoList.add(newLeaveDaytimeExtDto);

                    daytimeExtDto.setLeaveEndTime(shiftEndTime);
                    daytimeExtDto.setLeaveDate(preLeaveDate);
                }
            }
            leaveDaytimeExtDtoList.addAll(newLeaveDaytimeExtDtoList);
        }

        leaveDaytimeExtDtoList.stream()
                .filter(this::isValidLeaveRecord)
                .collect(Collectors.groupingBy(
                        dto -> String.format("%s_%s", dto.getEmpId(), dto.getLeaveDate()),
                        Collectors.mapping(
                                WaLeaveDaytimeExtDto::getLeaveEndTime,
                                Collectors.maxBy(Long::compareTo)
                        )
                ))
                .forEach((empIdAndDateKey, maxEndTimeOpt) ->
                        maxEndTimeOpt.ifPresent(aLong -> this.empLeaveDayTimeMap.put(empIdAndDateKey, aLong)));
    }

    /**
     * 检查休假记录是否有效
     * 过滤无效数据和销假记录
     */
    private boolean isValidLeaveRecord(WaLeaveDaytimeExtDto dto) {
        return dto != null
                && dto.getEmpId() != null
                && dto.getLeaveDate() != null
                && dto.getLeaveStartTime() != null
                && dto.getLeaveEndTime() != null
                && !dto.isXj() // 过滤掉销假记录
                && dto.getLeaveEndTime() > dto.getLeaveStartTime(); // 确保时间逻辑正确
    }

    /**
     * 初始化员工加班信息列表
     *
     * @param empOtInfoList 员工加班信息列表
     */
    public void initEmpOtList(List<EmpOverInfo> empOtInfoList) {
        if (CollectionUtils.isEmpty(empOtInfoList)) {
            this.empOtList = new ArrayList<>();
            return;
        }
        this.empOtList = new ArrayList<>(empOtInfoList);
    }

    /**
     * 获取当日加班最早开始时间
     *
     * @param empId 员工ID
     * @param date  日期
     * @return 最早开始时间，如果没有找到则返回null
     */
    public Long doGetMinOtStartTime(Long empId, Long date) {
        if (CollectionUtils.isEmpty(this.empOtList) || empId == null || date == null) {
            return null;
        }

        return this.empOtList.stream()
                .filter(it -> empId.equals(it.getEmpid()) && date.equals(Optional.ofNullable(it.getReal_date()).orElse(it.getRegdate())))
                .map(EmpOverInfo::getStart_time)
                .filter(Objects::nonNull)
                .min(Long::compareTo)
                .orElse(null);
    }

    /**
     * 获取当日加班最晚结束时间
     *
     * @param empId 员工ID
     * @param date  日期
     * @return 最晚结束时间，如果没有找到则返回null
     */
    public Long doGetMaxOtEndTime(Long empId, Long date) {
        if (CollectionUtils.isEmpty(this.empOtList) || empId == null || date == null) {
            return null;
        }

        return this.empOtList.stream()
                .filter(it -> empId.equals(it.getEmpid()) && date.equals(Optional.ofNullable(it.getReal_date()).orElse(it.getRegdate())))
                .map(EmpOverInfo::getEnd_time)
                .filter(Objects::nonNull)
                .max(Long::compareTo)
                .orElse(null);
    }

    public static ClockAnalyseDataCacheDto doBuild(List<WaRegisterRecordDo> waRegList,
                                                   Map<String, WaShiftDo> empShiftDoMap,
                                                   List<EmpParseGroup> empParseList) {
        ClockAnalyseDataCacheDto dataCacheDto = new ClockAnalyseDataCacheDto();
        dataCacheDto.setWaRegList(waRegList);
        dataCacheDto.setEmpShiftDoMap(empShiftDoMap);
        dataCacheDto.setEmpParseList(empParseList);
        return dataCacheDto;
    }

    public static ClockAnalyseDataCacheDto doBuild() {
        return new ClockAnalyseDataCacheDto();
    }

    public static void doClear(ClockAnalyseDataCacheDto dataCacheDto) {
        if (null == dataCacheDto) {
            return;
        }
        if (dataCacheDto.getWaRegList() != null) {
            dataCacheDto.getWaRegList().clear();
        }
        if (dataCacheDto.getEmpShiftDoMap() != null) {
            dataCacheDto.getEmpShiftDoMap().clear();
        }
        if (dataCacheDto.getEmpParseList() != null) {
            dataCacheDto.getEmpParseList().clear();
        }
    }
}
