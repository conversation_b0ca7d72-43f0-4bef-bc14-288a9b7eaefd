package com.caidaocloud.attendance.service.infrastructure.repository.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaEmpShiftMapper;
import com.caidao1.wa.mybatis.model.WaEmpShiftExample;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpShiftMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WorkCalendarMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpShiftPo;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.domain.entity.WaEmpShiftDo;
import com.caidaocloud.attendance.service.domain.repository.IEmpShiftRepository;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/4
 */
@Repository
public class EmpShiftRepository implements IEmpShiftRepository {
    @Resource
    private WaEmpShiftMapper waEmpShiftMapper;
    @Resource
    private WorkCalendarMapper workCalendarMapper;
    @Resource
    private EmpShiftMapper empShiftMapper;

    @Override
    public List<WaEmpShiftDo> getEmpShiftListByWorkCalendarId(String belongOrgId, Integer workCalendarId) {
        return workCalendarMapper.getEmpShiftListByCalendarId(belongOrgId, workCalendarId);
    }

    @Override
    public int deleteByWorkCalendarId(Integer workCalendarId) {
        WaEmpShiftExample example = new WaEmpShiftExample();
        example.createCriteria().andWorkCalendarIdEqualTo(workCalendarId);
        return waEmpShiftMapper.deleteByExample(example);
    }

    @Override
    public List<WaEmpShiftDo> getEmpShiftInfoList(Long corpId, String belongOrgId, List<Long> empIds, Long startTime, Long endTime, Integer workCalendarId) {
        List<Map> list = workCalendarMapper.getEmpShiftRelListByEmpIds(corpId, belongOrgId, empIds, startTime, endTime, workCalendarId);
        return JSON.parseArray(JSON.toJSONString(list), WaEmpShiftDo.class);
    }

    @Override
    public AttendancePageResult<WaEmpShiftDo> getEmpShiftDetailListByCalendarId(AttendanceBasePage basePage,
                                                                                String belongOrgId, Integer workCalendarId,
                                                                                String filter, String effectiveStatus) {
        PageBean pageBean = PageUtil.getPageBean(basePage);
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        if (StringUtils.isNotBlank(basePage.getKeywords())) {
            basePage.setKeywords("('"+org.apache.commons.lang3.StringUtils.join(basePage.getKeywords().split(" "), "','")+"')");
        }
        PageList<WaEmpShiftDo> pageList = workCalendarMapper.getEmpShiftPageListByCalendarId(myPageBounds,
                belongOrgId, workCalendarId, filter, basePage.getKeywords(), effectiveStatus, DateUtil.getOnlyDate());
        return new AttendancePageResult<>(pageList, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
    }

    @Override
    public void saveEmpShift(EmpShiftPo empShiftPo) {
        if (empShiftPo.getEmpShiftId() == null) {
            empShiftMapper.insertSelective(empShiftPo);
        } else {
            empShiftMapper.updateByPrimaryKeySelective(empShiftPo);
        }
    }

    @Override
    public List<EmpShiftPo> getEmpShiftByPeriod(Long empId, Integer empShiftId, String belongOrgId, Long startTime, Long endTime) {
        return empShiftMapper.queryEmpShiftByPeriod(empId, empShiftId, belongOrgId, startTime, endTime);
    }

    @Override
    public void deleteEmpShifts(List<Integer> empShiftIds) {
        if (CollectionUtils.isEmpty(empShiftIds)) {
            return;
        }
        empShiftMapper.deleteByIds(empShiftIds);
    }

    @Override
    public WaEmpShiftDo getEmpShift(Integer empShiftId) {
        EmpShiftPo empShiftPo = empShiftMapper.selectByPrimaryKey(empShiftId);
        if (null != empShiftPo) {
            return ObjectConverter.convert(empShiftPo, WaEmpShiftDo.class);
        }
        return null;
    }

    @Override
    public void batchSave(List<EmpShiftPo> empShifts) {
        empShiftMapper.batchSave(empShifts);
    }

    @Override
    public List<EmpShiftPo> getEmpShiftByIds(List<Integer> empShiftIds) {
        if (CollectionUtils.isEmpty(empShiftIds)) {
            return new ArrayList<>();
        }
        return empShiftMapper.queryEmpShiftByIds(empShiftIds);
    }
}
