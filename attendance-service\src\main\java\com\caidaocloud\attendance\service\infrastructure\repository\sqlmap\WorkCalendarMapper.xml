<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WorkCalendarMapper">
    <select id="getEmpShiftRelListByEmpIds" resultType="java.util.Map">
        SELECT ei.empid,emp_shift_id as "empShiftId",
        ei.workno,
        ei.emp_name as "empName",
        wt.work_calendar_name as "workCalendarName"
        FROM wa_emp_shift es
        JOIN wa_worktime wt on es.work_calendar_id = wt.work_calendar_id
        JOIN sys_emp_info ei on es.empid = ei.empid and ei.deleted = 0
        WHERE ei.corpid = #{corpId}
        and ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        and wt.deleted = 0
        <if test="empIds != null and empIds.size() > 0">
            and es.empid in
            <foreach collection="empIds" item="item" open="(" separator="," close=")">#{item}</foreach>
        </if>
        <if test="startTime != null">
            and es.start_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="endTime != null">
            and es.end_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="workCalendarId != null">
            and es.work_calendar_id <![CDATA[<>]]> #{workCalendarId}
        </if>
    </select>
    <select id="getEmpShiftPageListByCalendarId" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpShiftDo">
        SELECT
        emp_shift_id AS "empShiftId",
        ei.empid,
        ei.workno,
        ei.emp_name AS "empName",
        co.shortname,
        co.shortname AS "orgName",
        case
        when co.full_path is not null and co.full_path != ''
        then concat_ws('/', co.full_path, co.shortname)
        else co.shortname
        end  AS "fullPath",
        es.work_calendar_id AS "workCalendarId",
        wt.work_calendar_name AS "workCalendarName",
        wt.i18n_work_calendar_name AS "i18nWorkCalendarName",
        wt.worktime_type as "worktimeType",
        es.start_time AS "startTime",
        es.end_time AS "endTime",
        es.updtime,
        case
            when sei.workno is not null and sei.workno!=''
                then concat(sei.workno, '(', sei.emp_name, ')')
            when sui.empname is not null and sui.empname != ''
                then concat(sui.account, '(', sui.empname, ')')
            else ''
            end AS "updater",
        ei.stats AS "empStatus",
        ei.employ_type AS "empStyle",
        ei.hire_date AS "hireDate",
        ei.termination_date AS "terminationDate"
        FROM wa_emp_shift es
        LEFT JOIN wa_worktime wt ON es.work_calendar_id = wt.work_calendar_id
        JOIN sys_emp_info ei ON es.empid = ei.empid and ei.deleted = 0
        LEFT JOIN sys_corp_org co ON ei.orgid = co.orgid AND co.deleted = 0
        LEFT JOIN sys_user_info sui ON sui.userid = es.upduser
        LEFT JOIN sys_emp_info sei ON sei.empid=sui.empid
        <where>
            ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR} AND ei.deleted = 0
            <if test="workCalendarId != null">
                AND es.work_calendar_id = #{workCalendarId}
            </if>
            <if test="filter != null and filter != ''">
                ${filter}
            </if>
            <if test="keywords != null and keywords != ''">
                AND (ei.workno in ${keywords} OR ei.emp_name in ${keywords})
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'NotEffective'">
                and es.start_time <![CDATA[>]]> #{nowTime}
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'InEffect'">
                and #{nowTime} <![CDATA[>=]]> es.start_time and #{nowTime} <![CDATA[<=]]> es.end_time
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'Expired'">
                and es.end_time <![CDATA[<]]> #{nowTime}
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'NotEffective+InEffect'">
                and es.end_time <![CDATA[>=]]> #{nowTime}
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'NotEffective+Expired'">
                and (es.start_time <![CDATA[>]]> #{nowTime} or es.end_time <![CDATA[<]]> #{nowTime})
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'InEffect+Expired'">
                and es.start_time <![CDATA[<=]]> #{nowTime}
            </if>
        </where>
        ORDER BY es.updtime DESC
    </select>
    <select id="getEmpShiftListByCalendarId"
            resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpShiftDo">
        SELECT ei.empid,emp_shift_id as "empShiftId",
        ei.workno,
        ei.emp_name as "empName",
        wt.work_calendar_name as "workCalendarName"
        FROM wa_emp_shift es
        JOIN wa_worktime wt on es.work_calendar_id = wt.work_calendar_id
        JOIN sys_emp_info ei on es.empid = ei.empid and ei.deleted = 0
        WHERE ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
            and es.work_calendar_id = #{workCalendarId}
    </select>
    <select id="getWorkCalendarDetailList" resultType="java.util.Map">
        SELECT
          case wd.date_type
           when 1 then sd.shift_def_name
           when 2 then '休息日'
           when 3 then '节假日'
           when 5 then '法定休日'
           when 4
               then '特殊班' end                          as "name",
            to_char(to_timestamp(wd.work_date), 'yyyyMMdd') as "date",
            wd.date_type as "dateType",sd.i18n_shift_def_name "i18nShiftDefName",
            case wd.date_type when 1 then '工作日' when 2 then '休息日' when 3 then '法定假日' when 5 then '法定休日' when 4 then '特殊休日' end as "dateTypeName"
        FROM
        wa_worktime_detail wd
        JOIN wa_shift_def sd ON wd.shift_def_id = sd.shift_def_id
        WHERE
        work_calendar_id = #{workCalendarId}
        AND wd.work_date between #{start} and #{end}
    </select>
    <select id="getEmpCalendarList" resultType="java.util.Map">
        SELECT to_char(to_timestamp(wd.work_date), 'yyyyMMdd') as "date",
               case wd.date_type
               when 1 then sd.shift_def_name
               when 2 then '休息日'
               when 3 then '节假日'
               when 5 then '法定休日'
               when 4
               then '特殊班' end                          as "name",
               sd.shift_def_code                               as "code",
               wd.date_type                                    as "dateType",
               case wd.date_type
                   when 1 then '工作日'
                   when 2 then '休息日'
                   when 3 then '法定假日'
                   when 5 then '法定休日'
                   when 4 then '特殊休日'
                   else '未知' end                               as "dateTypeName"
        FROM wa_emp_shift es
                 JOIN wa_worktime wt ON es.work_calendar_id = wt.work_calendar_id
                 JOIN wa_worktime_detail wd ON wt.work_calendar_id = wd.work_calendar_id
                 JOIN wa_shift_def sd ON wd.shift_def_id = sd.shift_def_id
                 JOIN sys_emp_info ei ON ei.empid = es.empid AND ei.tm_type = 1
        WHERE wt.deleted = 0
          AND work_date BETWEEN #{startDate} AND #{endDate}
          and work_date between es.start_time and es.end_time
          and ((ei.stats <![CDATA[<>]]> 1 and work_date <![CDATA[>=]]> ei.hire_date) or
               (ei.stats = 1 and work_date between ei.hire_date and ei.termination_date))
          AND es.empid = #{empId}
        UNION ALL
        select to_char(to_timestamp(b.work_date), 'yyyyMMdd') as "date",
               case b.date_type
               when 1 then c.shift_def_name
               when 2 then '休息日'
               when 3 then '节假日'
               when 5 then '法定休日'
               when 4
               then '特殊班' end                          as "name",
               c.shift_def_code                               as "code",
               b.date_type                                    as "dateType",
               case b.date_type
                   when 1 then '工作日'
                   when 2 then '休息日'
                   when 3 then '法定假日'
                   when 5 then '法定休日'
                   when 4 then '特殊休日'
                   else '未知' end                              as "dateTypeName"
        from wa_worktime a,
             wa_worktime_detail b,
             wa_shift_def c,
             sys_emp_info d
        where a.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
          and a.is_default = true
          and a.deleted = 0
          and a.work_calendar_id = b.work_calendar_id
          and b.shift_def_id = c.shift_def_id
          and b.work_date BETWEEN #{startDate} AND #{endDate}
          and a.belong_orgid = d.belong_org_id
          and d.tm_type = 1
          and d.empid not in (select empid from wa_emp_shift where b.work_date BETWEEN start_time and end_time)
          AND d.empid = #{empId}
          and ((d.stats <![CDATA[<>]]> 1 and work_date <![CDATA[>=]]> d.hire_date) or
               (d.stats = 1 and work_date between d.hire_date and d.termination_date))
    </select>

    <sql id="Base_Column_List" >
        wd.work_date,
        wd.date_type AS "calendar_date_type",
        sd.shift_def_id,
        sd.date_type,
        is_night,
        shift_def_name,
        shift_def_code,
        sd.start_time,
        sd.end_time,
        sd.is_noon_rest,
        sd.noon_rest_start,
        sd.noon_rest_end,
        sd.rest_total_time,
        sd.work_total_time,
        on_duty_start_time,
        on_duty_end_time,
        off_duty_start_time,
        off_duty_end_time,
        overtime_start_time,
        overtime_end_time,
        sd.belong_orgid,
        sd.crtuser,
        sd.crttime,
        sd.is_default,
        is_halfday_time,
        halfday_time,
        is_flexible_work,
        flexible_on_duty_start_time,
        flexible_on_duty_end_time,
        flexible_off_duty_start_time,
        flexible_off_duty_end_time,
        flexible_work_type,
        sd.rest_periods,
        sd.overtime_rest_periods,
        is_adjust_work_hour,
        adjust_work_hour_json,
        is_special,
        special_work_time,
        is_apply_overtime,
        multi_checkin_times,
        multi_work_times,
        rest_time_desc,
        sd.orgid,
        effect_start_time,
        effect_end_time
    </sql>

    <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaShiftPo">
        <result column="empid" property="empid" jdbcType="BIGINT" />
        <result column="work_date" property="workDate" jdbcType="BIGINT" />
        <result column="calendar_date_type" property="calendarDateType" jdbcType="INTEGER" />
        <result column="shift_def_id" property="shiftDefId" jdbcType="INTEGER" />
        <result column="date_type" property="dateType" jdbcType="INTEGER" />
        <result column="is_night" property="isNight" jdbcType="BIT" />
        <result column="shift_def_name" property="shiftDefName" jdbcType="VARCHAR" />
        <result column="shift_def_code" property="shiftDefCode" jdbcType="VARCHAR" />
        <result column="start_time" property="startTime" jdbcType="INTEGER" />
        <result column="end_time" property="endTime" jdbcType="INTEGER" />
        <result column="is_noon_rest" property="isNoonRest" jdbcType="BIT" />
        <result column="noon_rest_start" property="noonRestStart" jdbcType="INTEGER" />
        <result column="noon_rest_end" property="noonRestEnd" jdbcType="INTEGER" />
        <result column="rest_total_time" property="restTotalTime" jdbcType="INTEGER" />
        <result column="work_total_time" property="workTotalTime" jdbcType="INTEGER" />
        <result column="on_duty_start_time" property="onDutyStartTime" jdbcType="INTEGER" />
        <result column="on_duty_end_time" property="onDutyEndTime" jdbcType="INTEGER" />
        <result column="off_duty_start_time" property="offDutyStartTime" jdbcType="INTEGER" />
        <result column="off_duty_end_time" property="offDutyEndTime" jdbcType="INTEGER" />
        <result column="overtime_start_time" property="overtimeStartTime" jdbcType="INTEGER" />
        <result column="overtime_end_time" property="overtimeEndTime" jdbcType="INTEGER" />
        <result column="belong_orgid" property="belongOrgid" jdbcType="VARCHAR" />
        <result column="crtuser" property="crtuser" jdbcType="BIGINT" />
        <result column="crttime" property="crttime" jdbcType="BIGINT" />
        <result column="is_default" property="isDefault" jdbcType="BIT" />
        <result column="is_halfday_time" property="isHalfdayTime" jdbcType="BIT" />
        <result column="halfday_time" property="halfdayTime" jdbcType="INTEGER" />
        <result column="is_flexible_work" property="isFlexibleWork" jdbcType="BIT" />
        <result column="flexible_on_duty_start_time" property="flexibleOnDutyStartTime" jdbcType="INTEGER" />
        <result column="flexible_on_duty_end_time" property="flexibleOnDutyEndTime" jdbcType="INTEGER" />
        <result column="flexible_off_duty_start_time" property="flexibleOffDutyStartTime" jdbcType="INTEGER" />
        <result column="flexible_off_duty_end_time" property="flexibleOffDutyEndTime" jdbcType="INTEGER" />
        <result column="flexible_work_type" property="flexibleWorkType" jdbcType="INTEGER" />
        <result column="rest_periods" property="restPeriods" jdbcType="OTHER" />
        <result column="overtime_rest_periods" property="overtimeRestPeriods" jdbcType="OTHER" />
        <result column="is_adjust_work_hour" property="isAdjustWorkHour" jdbcType="BIT" />
        <result column="adjust_work_hour_json" property="adjustWorkHourJson" jdbcType="OTHER" />
        <result column="is_special" property="isSpecial" jdbcType="BIT" />
        <result column="special_work_time" property="specialWorkTime" jdbcType="INTEGER" />
        <result column="is_apply_overtime" property="isApplyOvertime" jdbcType="BIT" />
        <result column="multi_checkin_times" property="multiCheckinTimes" jdbcType="OTHER" />
        <result column="multi_work_times" property="multiWorkTimes" jdbcType="OTHER" />
        <result column="rest_time_desc" property="restTimeDesc" jdbcType="VARCHAR" />
        <result column="orgid" property="orgid" jdbcType="BIGINT" />
        <result column="effect_start_time" property="effectStartTime" jdbcType="BIGINT" />
        <result column="effect_end_time" property="effectEndTime" jdbcType="BIGINT" />
    </resultMap>

    <select id="selectWaShiftList" resultMap="BaseResultMap">
        SELECT
          es.empid, <include refid="Base_Column_List" />
        FROM wa_emp_shift es
        JOIN wa_worktime wt ON es.work_calendar_id = wt.work_calendar_id
        JOIN wa_worktime_detail wd ON wt.work_calendar_id = wd.work_calendar_id
        JOIN wa_shift_def sd ON wd.shift_def_id = sd.shift_def_id
        JOIN sys_emp_info ei ON ei.empid = es.empid AND ei.tm_type = 1
        WHERE wt.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        AND wd.work_date BETWEEN #{startDate} AND #{endDate}
        AND wd.work_date BETWEEN es.start_time AND es.end_time
        AND ((ei.stats  <![CDATA[<>]]> 1 AND work_date >= ei.hire_date) OR
        (ei.stats = 1 AND work_date BETWEEN ei.hire_date AND ei.termination_date))
        AND wt.deleted = 0
        UNION ALL
        SELECT
          ei.empid,<include refid="Base_Column_List" />
        FROM wa_worktime wt,
        wa_worktime_detail wd,
        wa_shift_def sd,
        sys_emp_info ei
        WHERE wt.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        AND wt.is_default = true
        AND wt.work_calendar_id = wd.work_calendar_id
        AND wd.shift_def_id = sd.shift_def_id
        AND wd.work_date BETWEEN #{startDate} AND #{endDate}
        AND wt.belong_orgid = ei.belong_org_id
        AND ei.tm_type = 1
        AND ei.empid NOT IN (SELECT empid FROM wa_emp_shift WHERE wd.work_date BETWEEN start_time AND end_time)
        AND ((ei.stats <![CDATA[<>]]> 1 AND work_date >= ei.hire_date) OR
        (ei.stats = 1 AND work_date BETWEEN ei.hire_date AND ei.termination_date))
        AND wt.deleted = 0
    </select>
    <select id="getCalendarGroupPageList"
            resultType="com.caidaocloud.attendance.service.domain.entity.WaCalendarGroupDo">
        select calendar_group_id,calendar_group_id as "calendarGroupId", group_name as "groupName", crttime, i18n_group_name as "i18nGroupName"
        from wa_calendar_group
        where belong_orgid=#{belongOrgId,jdbcType=VARCHAR}
    </select>

    <select id="getEmpShiftChangeListByEmpId" resultType="map">
        select to_char(to_timestamp(wesc.work_date), 'yyyyMMdd') as "date",
               case sd.date_type
                   when 1 then sd.shift_def_name
                   when 2 then '休息日'
                   when 3 then '节假日'
                   when 5 then '法定休日'
                   when 4
                       then '特殊班' end                            as "name",
               sd.shift_def_code                                 as "code",
               sd.date_type                                      as "dateType",
               case sd.date_type
                   when 1 then '工作日'
                   when 2 then '休息日'
                   when 3 then '法定假日'
                   when 5 then '法定休日'
                   when 4 then '特殊休日'
                   else '未知' end                                 as "dateTypeName"
        from wa_emp_shift_change wesc
                 JOIN wa_shift_def sd ON wesc.new_shift_def_id = sd.shift_def_id
        where wesc.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
          and wesc.empid = #{empId}
          and wesc.work_date between #{startDate} and #{endDate}
          and wesc.status = 2
    </select>

    <select id="getEmpShiftChangePageList" resultType="map">
        select * from (
            select
            wesc.status   as "shiftStatus",
            wesc.rec_id                                                                         as "recId",
            sei.empid,
            sei.workno,
            sei.hire_date as "hireDate",
            sei.emp_name                                                                        as "empName",
            case
                when sei2.workno is not null and sei2.workno <![CDATA[<>]]> ''
                    then concat(sei2.workno, '(', sei2.emp_name, ')')
                when sui.empname is not null and sui.empname != ''
                    then concat(sui.account, '(', sui.empname, ')')
                else ''
                end   as "crtEmpName",
            wesc.crttime,
            wesc.work_date                                                                      as "workDate",
            coalesce(wsd.shift_def_code, '') || ' ' || coalesce(wsd.shift_def_name, '')         as "newShiftInfo",
            coalesce(old_wsd.shift_def_code, '') || ' ' || coalesce(old_wsd.shift_def_name, '') as "oldShiftInfo",
            wesc.work_date,
            sei.orgid,
            sei.employ_type,
            coalesce(wsd.shift_def_code, '') "newShiftCode",
            coalesce(wsd.i18n_shift_def_name, '') "newI18nShiftDefName",
            coalesce(old_wsd.shift_def_code, '') "oldShiftCode",
            coalesce(old_wsd.i18n_shift_def_name, '') "oldI18nShiftDefName"
            from wa_emp_shift_change wesc
            join sys_emp_info sei on wesc.empid = sei.empid and sei.deleted = 0
            join wa_shift_def wsd on wsd.shift_def_id = wesc.new_shift_def_id
            left join wa_shift_def old_wsd on old_wsd.shift_def_id = wesc.old_shift_def_id
            left join sys_user_info sui on wesc.crtuser = sui.userid
            left join sys_emp_info sei2 on sui.empid = sei2.empid
            where sei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
            <if test="empid != null">
                and wesc.empid = #{empid}
            </if>
            <if test="shiftStatus != null">
                and wesc.status = #{shiftStatus}
            </if>
            <if test="workDate != null">
                and wesc.work_date = #{workDate}
            </if>
            <if test="keywords != null and keywords != ''">
                and (sei.workno like concat('%', #{keywords}, '%') or sei.emp_name like concat('%', #{keywords}, '%'))
            </if>
            <if test="datafilter != null and datafilter != ''">
                ${datafilter}
            </if>
        ) m
        <where>
            <if test="filter != null and filter != ''">
                ${filter}
            </if>
        </where>
    </select>
    <select id="getEmpCalendarShiftList" resultType="java.util.Map">
        SELECT to_char(to_timestamp(wd.work_date), 'yyyyMMdd') as "date",
               sd.shift_def_name                         as "name",
               sd.i18n_shift_def_name                         as "i18nShiftDefName",
               sd.shift_def_code                               as "code",
               wd.date_type                                    as "dateType",
               sd.shift_def_id as "id"
        FROM wa_emp_shift es
                 JOIN wa_worktime wt ON es.work_calendar_id = wt.work_calendar_id
                 JOIN wa_worktime_detail wd ON wt.work_calendar_id = wd.work_calendar_id
                 JOIN wa_shift_def sd ON wd.shift_def_id = sd.shift_def_id
                 JOIN sys_emp_info ei ON ei.empid = es.empid AND ei.tm_type = 1 and ei.deleted = 0
        WHERE wt.deleted = 0
          AND work_date BETWEEN #{startDate} AND #{endDate}
          and work_date between es.start_time and es.end_time
          and ((ei.stats <![CDATA[<>]]> 1 and work_date <![CDATA[>=]]> coalesce(ei.internship_date, ei.hire_date)) or
               (ei.stats = 1 and work_date between coalesce(ei.internship_date, ei.hire_date) and ei.termination_date))
          AND es.empid = #{empId}
        <if test="dataScope != null and dataScope != ''">
            ${dataScope}
        </if>
        UNION ALL
        select to_char(to_timestamp(b.work_date), 'yyyyMMdd') as "date",
               c.shift_def_name                          as "name",
               c.i18n_shift_def_name                          as "i18nShiftDefName",
               c.shift_def_code                               as "code",
               b.date_type                                    as "dateType",
               c.shift_def_id as "id"
        from wa_worktime a,
             wa_worktime_detail b,
             wa_shift_def c,
             sys_emp_info ei
        where a.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
          and a.is_default = true
          and a.deleted = 0
          and a.work_calendar_id = b.work_calendar_id
          and b.shift_def_id = c.shift_def_id
          and b.work_date BETWEEN #{startDate} AND #{endDate}
          and a.belong_orgid = ei.belong_org_id
          and ei.tm_type = 1
          and ei.empid not in (select empid from wa_emp_shift where b.work_date BETWEEN start_time and end_time)
          AND ei.empid = #{empId}
          and ((ei.stats <![CDATA[<>]]> 1 and work_date <![CDATA[>=]]> coalesce(ei.internship_date, ei.hire_date)) or
               (ei.stats = 1 and work_date between coalesce(ei.internship_date, ei.hire_date) and ei.termination_date))
        <if test="dataScope != null and dataScope != ''">
            ${dataScope}
        </if>
    </select>

    <select id="getChangeShiftDefListByEmpId" resultType="map">
        select to_char(to_timestamp(wesc.work_date), 'yyyyMMdd') as "date",
        sd.shift_def_name                           as "name",
        sd.i18n_shift_def_name                           as "i18nShiftDefName",
        sd.shift_def_code                                 as "code",
        sd.date_type                                      as "dateType",
        sd.shift_def_id as "id"
        from wa_emp_shift_change wesc
        JOIN wa_shift_def sd ON wesc.new_shift_def_id = sd.shift_def_id
        where wesc.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        and wesc.empid = #{empId}
        and wesc.work_date between #{startDate} and #{endDate}
        and wesc.status = 2
    </select>

    <select id="getCalendarListByEmpIds" resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaShiftPo">
        SELECT
        es.empid,
        wd.work_date                    as "workDate",
        wd.date_type                    as "dateType",
        sd.shift_def_id                 as "shiftDefId",
        sd.shift_def_name               as "shiftDefName",
        sd.i18n_shift_def_name          as "i18nShiftDefName",
        sd.shift_def_code               as "shiftDefCode",
        sd.on_duty_start_time           as "onDutyStartTime",
        sd.on_duty_end_time             as "onDutyEndTime",
        sd.off_duty_start_time          as "offDutyStartTime",
        sd.off_duty_end_time            as "offDutyEndTime",
        sd.start_time                   as "startTime",
        sd.end_time                     as "endTime",
        sd.is_noon_rest                 as "isNoonRest",
        sd.noon_rest_start              as "noonRestStart",
        sd.noon_rest_end                as "noonRestEnd",
        sd.is_flexible_work             as "isFlexibleWork",
        sd.flexible_on_duty_start_time  as "flexibleOnDutyStartTime",
        sd.flexible_on_duty_end_time    as "flexibleOnDutyEndTime",
        sd.flexible_off_duty_start_time as "flexibleOffDutyStartTime",
        sd.flexible_off_duty_end_time   as "flexibleOffDutyEndTime",
        sd.flexible_work_type           as "flexibleWorkType",
        sd.work_total_time              as "workTotalTime",
        sd.is_halfday_time              as "isHalfdayTime",
        sd.halfday_time                 as "halfdayTime",
        sd.multi_work_times             as "multiWorkTimes",
        sd.multi_checkin_times          as "multiCheckinTimes",
        sd.start_time_belong            as "startTimeBelong",
        sd.end_time_belong              as "endTimeBelong",
        sd.noon_rest_start_belong       as "noonRestStartBelong",
        sd.noon_rest_end_belong         as "noonRestEndBelong",
        sd.on_duty_start_time_belong    as "onDutyStartTimeBelong",
        sd.on_duty_end_time_belong      as "onDutyEndTimeBelong",
        sd.off_duty_start_time_belong   as "offDutyStartTimeBelong",
        sd.off_duty_end_time_belong     as "offDutyEndTimeBelong",
        sd.overtime_start_time_belong   as "overtimeStartTimeBelong",
        sd.overtime_end_time_belong     as "overtimeEndTimeBelong",
        sd.halfday_time_belong          as "halfdayTimeBelong",
        sd.halfday_type                 as "halfdayType",
        sd.multi_overtime               as "multiOvertime",
        sd.belong_module                as "belongModule",
        sd.clock_analysis_rule          as "clockAnalysisRule"
        FROM wa_emp_shift es
        JOIN wa_worktime wt ON es.work_calendar_id = wt.work_calendar_id
        JOIN wa_worktime_detail wd ON wt.work_calendar_id = wd.work_calendar_id
        JOIN wa_shift_def sd ON wd.shift_def_id = sd.shift_def_id
        JOIN sys_emp_info ei ON ei.empid = es.empid AND ei.tm_type = 1
        WHERE wt.deleted = 0
        AND work_date BETWEEN #{startDate} AND #{endDate}
        and work_date between es.start_time and es.end_time
        and ((ei.stats <![CDATA[<>]]> 1 and work_date <![CDATA[>=]]> ei.hire_date) or
        (ei.stats = 1 and work_date between ei.hire_date and ei.termination_date))
        AND es.empid in <foreach collection="empIds" open="(" separator="," item="item" close=")">#{item}</foreach>
        UNION ALL
        select
        d.empid,
        b.work_date                    as "workDate",
        c.date_type                    as "dateType",
        c.shift_def_id                 as "shiftDefId",
        c.shift_def_name               as "shiftDefName",
        c.i18n_shift_def_name          as "i18nShiftDefName",
        c.shift_def_code               as "shiftDefCode",
        c.on_duty_start_time           as "onDutyStartTime",
        c.on_duty_end_time             as "onDutyEndTime",
        c.off_duty_start_time          as "offDutyStartTime",
        c.off_duty_end_time            as "offDutyEndTime",
        c.start_time                   as "startTime",
        c.end_time                     as "endTime",
        c.is_noon_rest                 as "isNoonRest",
        c.noon_rest_start              as "noonRestStart",
        c.noon_rest_end                as "noonRestEnd",
        c.is_flexible_work             as "isFlexibleWork",
        c.flexible_on_duty_start_time  as "flexibleOnDutyStartTime",
        c.flexible_on_duty_end_time    as "flexibleOnDutyEndTime",
        c.flexible_off_duty_start_time as "flexibleOffDutyStartTime",
        c.flexible_off_duty_end_time   as "flexibleOffDutyEndTime",
        c.flexible_work_type           as "flexibleWorkType",
        c.work_total_time              as "workTotalTime",
        c.is_halfday_time              as "isHalfdayTime",
        c.halfday_time                 as "halfdayTime",
        c.multi_work_times             as "multiWorkTimes",
        c.multi_checkin_times          as "multiCheckinTimes",
        c.start_time_belong            as "startTimeBelong",
        c.end_time_belong              as "endTimeBelong",
        c.noon_rest_start_belong       as "noonRestStartBelong",
        c.noon_rest_end_belong         as "noonRestEndBelong",
        c.on_duty_start_time_belong    as "onDutyStartTimeBelong",
        c.on_duty_end_time_belong      as "onDutyEndTimeBelong",
        c.off_duty_start_time_belong   as "offDutyStartTimeBelong",
        c.off_duty_end_time_belong     as "offDutyEndTimeBelong",
        c.overtime_start_time_belong   as "overtimeStartTimeBelong",
        c.overtime_end_time_belong     as "overtimeEndTimeBelong",
        c.halfday_time_belong          as "halfdayTimeBelong",
        c.halfday_type                 as "halfdayType",
        c.multi_overtime               as "multiOvertime",
        c.belong_module                as "belongModule",
        c.clock_analysis_rule          as "clockAnalysisRule"
        from wa_worktime a,
        wa_worktime_detail b,
        wa_shift_def c,
        sys_emp_info d
        where a.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        and a.is_default = true
        and a.deleted = 0
        and a.work_calendar_id = b.work_calendar_id
        and b.shift_def_id = c.shift_def_id
        and b.work_date BETWEEN #{startDate} AND #{endDate}
        and a.belong_orgid = d.belong_org_id
        and d.tm_type = 1
        and d.empid not in (select empid from wa_emp_shift where b.work_date BETWEEN start_time and end_time)
        and d.empid in <foreach collection="empIds" open="(" separator="," item="item" close=")">#{item}</foreach>
        and ((d.stats <![CDATA[<>]]> 1 and work_date <![CDATA[>=]]> d.hire_date) or
        (d.stats = 1 and work_date between d.hire_date and d.termination_date));
    </select>

    <select id="getEmpShiftChangeList"
            resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaShiftPo">
        select wesc.empid,
        wesc.work_date                  as "workDate",
        sd.date_type                    as "dateType",
        sd.shift_def_id                 as "shiftDefId",
        sd.shift_def_name               as "shiftDefName",
        sd.i18n_shift_def_name          as "i18nShiftDefName",
        sd.on_duty_start_time           as "onDutyStartTime",
        sd.on_duty_end_time             as "onDutyEndTime",
        sd.off_duty_start_time          as "offDutyStartTime",
        sd.off_duty_end_time            as "offDutyEndTime",
        sd.start_time                   as "startTime",
        sd.end_time                     as "endTime",
        sd.is_noon_rest                 as "isNoonRest",
        sd.noon_rest_start              as "noonRestStart",
        sd.noon_rest_end                as "noonRestEnd",
        sd.is_flexible_work             as "isFlexibleWork",
        sd.flexible_on_duty_start_time  as "flexibleOnDutyStartTime",
        sd.flexible_on_duty_end_time    as "flexibleOnDutyEndTime",
        sd.flexible_off_duty_start_time as "flexibleOffDutyStartTime",
        sd.flexible_off_duty_end_time   as "flexibleOffDutyEndTime",
        sd.flexible_work_type           as "flexibleWorkType",
        sd.work_total_time              as "workTotalTime",
        sd.is_halfday_time              as "isHalfdayTime",
        sd.halfday_time                 as "halfdayTime",
        sd.multi_work_times             as "multiWorkTimes",
        sd.multi_checkin_times          as "multiCheckinTimes",
        sd.start_time_belong            as "startTimeBelong",
        sd.end_time_belong              as "endTimeBelong",
        sd.noon_rest_start_belong       as "noonRestStartBelong",
        sd.noon_rest_end_belong         as "noonRestEndBelong",
        sd.on_duty_start_time_belong    as "onDutyStartTimeBelong",
        sd.on_duty_end_time_belong      as "onDutyEndTimeBelong",
        sd.off_duty_start_time_belong   as "offDutyStartTimeBelong",
        sd.off_duty_end_time_belong     as "offDutyEndTimeBelong",
        sd.overtime_start_time_belong   as "overtimeStartTimeBelong",
        sd.overtime_end_time_belong     as "overtimeEndTimeBelong",
        sd.halfday_time_belong          as "halfdayTimeBelong",
        sd.halfday_type                 as "halfdayType",
        sd.multi_overtime               as "multiOvertime",
        sd.belong_module                as "belongModule",
        sd.clock_analysis_rule          as "clockAnalysisRule"
        from wa_emp_shift_change wesc
        join wa_shift_def sd on wesc.new_shift_def_id = sd.shift_def_id
        where wesc.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        and wesc.empid in <foreach collection="empIds" open="(" separator="," item="item" close=")">#{item}</foreach>
        and wesc.work_date between #{startDate} and #{endDate}
        and wesc.status = 2
    </select>

    <select id="syncEmpAttendanceType" resultType="map">
        UPDATE attendance.sys_emp_info ei
        SET attendance_type = ei2.attendance_type
        FROM (
        SELECT ei.empid,
        wt.worktime_type as attendance_type
        FROM attendance.sys_emp_info ei
        LEFT JOIN attendance.wa_emp_shift es ON ei.empid = es.empid
        AND es.start_time &lt;= #{onlyDate}
        AND es.end_time >= #{onlyDate}
        LEFT JOIN attendance.wa_worktime wt ON es.work_calendar_id = wt.work_calendar_id
        WHERE ei.belong_org_id = #{belongOrgId}
        AND ei.deleted = 0
        AND ei.stats != 1
        <if test="empIds != null">
            and ei.empid in <foreach collection="empIds" open="(" separator="," item="item" close=")">#{item}</foreach>
        </if>
        ) ei2
        WHERE ei.empid = ei2.empid;
    </select>
</mapper>