<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpTravelDaytimeMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravelDaytime">
    <id column="travel_daytime_id" jdbcType="BIGINT" property="travelDaytimeId" />
    <result column="travel_id" jdbcType="BIGINT" property="travelId" />
    <result column="travel_date" jdbcType="BIGINT" property="travelDate" />
    <result column="shalf_day" jdbcType="VARCHAR" property="shalfDay" />
    <result column="ehalf_day" jdbcType="VARCHAR" property="ehalfDay" />
    <result column="start_time" jdbcType="INTEGER" property="startTime" />
    <result column="end_time" jdbcType="INTEGER" property="endTime" />
    <result column="period_type" jdbcType="SMALLINT" property="periodType" />
    <result column="time_duration" jdbcType="REAL" property="timeDuration" />
    <result column="time_unit" jdbcType="SMALLINT" property="timeUnit" />
    <result column="date_type" jdbcType="INTEGER" property="dateType" />
    <result column="real_date" jdbcType="BIGINT" property="realDate" />
    <result column="shift_def_id" jdbcType="INTEGER" property="shiftDefId" />
    <result column="apply_time_duration" jdbcType="REAL" property="applyTimeDuration" />
    <result column="before_adjust_time_duration" jdbcType="REAL" property="beforeAdjustTimeDuration" />

    <result column="entity_id" jdbcType="BIGINT" property="entityId" />
    <result column="ext_custom_col" jdbcType="VARCHAR" property="extCustomCol" />
  </resultMap>
  <sql id="Base_Column_List">
    travel_daytime_id, travel_id, travel_date, shalf_day, ehalf_day, start_time, end_time, 
    period_type, time_duration, time_unit, date_type, real_date, shift_def_id, apply_time_duration, 
    before_adjust_time_duration,entity_id, ext_custom_col
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_emp_travel_daytime
    where travel_daytime_id = #{travelDaytimeId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_emp_travel_daytime
    where travel_daytime_id = #{travelDaytimeId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravelDaytime">
    insert into wa_emp_travel_daytime (travel_daytime_id, travel_id, travel_date, 
      shalf_day, ehalf_day, start_time, 
      end_time, period_type, time_duration, 
      time_unit, date_type, real_date, 
      shift_def_id, apply_time_duration, before_adjust_time_duration,entity_id
      , ext_custom_col)
    values (#{travelDaytimeId,jdbcType=BIGINT}, #{travelId,jdbcType=BIGINT}, #{travelDate,jdbcType=BIGINT}, 
      #{shalfDay,jdbcType=VARCHAR}, #{ehalfDay,jdbcType=VARCHAR}, #{startTime,jdbcType=INTEGER}, 
      #{endTime,jdbcType=INTEGER}, #{periodType,jdbcType=SMALLINT}, #{timeDuration,jdbcType=REAL}, 
      #{timeUnit,jdbcType=SMALLINT}, #{dateType,jdbcType=INTEGER}, #{realDate,jdbcType=BIGINT}, 
      #{shiftDefId,jdbcType=INTEGER}, #{applyTimeDuration,jdbcType=REAL}, #{beforeAdjustTimeDuration,jdbcType=REAL}, #{entityId,jdbcType=BIGINT}
      , #{extCustomCol,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravelDaytime">
    insert into wa_emp_travel_daytime
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="travelDaytimeId != null">
        travel_daytime_id,
      </if>
      <if test="travelId != null">
        travel_id,
      </if>
      <if test="travelDate != null">
        travel_date,
      </if>
      <if test="shalfDay != null">
        shalf_day,
      </if>
      <if test="ehalfDay != null">
        ehalf_day,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="periodType != null">
        period_type,
      </if>
      <if test="timeDuration != null">
        time_duration,
      </if>
      <if test="timeUnit != null">
        time_unit,
      </if>
      <if test="dateType != null">
        date_type,
      </if>
      <if test="realDate != null">
        real_date,
      </if>
      <if test="shiftDefId != null">
        shift_def_id,
      </if>
      <if test="applyTimeDuration != null">
        apply_time_duration,
      </if>
      <if test="beforeAdjustTimeDuration != null">
        before_adjust_time_duration,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="extCustomCol != null">
        ext_custom_col,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="travelDaytimeId != null">
        #{travelDaytimeId,jdbcType=BIGINT},
      </if>
      <if test="travelId != null">
        #{travelId,jdbcType=BIGINT},
      </if>
      <if test="travelDate != null">
        #{travelDate,jdbcType=BIGINT},
      </if>
      <if test="shalfDay != null">
        #{shalfDay,jdbcType=VARCHAR},
      </if>
      <if test="ehalfDay != null">
        #{ehalfDay,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=INTEGER},
      </if>
      <if test="periodType != null">
        #{periodType,jdbcType=SMALLINT},
      </if>
      <if test="timeDuration != null">
        #{timeDuration,jdbcType=REAL},
      </if>
      <if test="timeUnit != null">
        #{timeUnit,jdbcType=SMALLINT},
      </if>
      <if test="dateType != null">
        #{dateType,jdbcType=INTEGER},
      </if>
      <if test="realDate != null">
        #{realDate,jdbcType=BIGINT},
      </if>
      <if test="shiftDefId != null">
        #{shiftDefId,jdbcType=INTEGER},
      </if>
      <if test="applyTimeDuration != null">
        #{applyTimeDuration,jdbcType=REAL},
      </if>
      <if test="beforeAdjustTimeDuration != null">
        #{beforeAdjustTimeDuration,jdbcType=REAL},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=BIGINT},
      </if>
      <if test="extCustomCol != null">
        #{extCustomCol,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravelDaytime">
    update wa_emp_travel_daytime
    <set>
      <if test="travelId != null">
        travel_id = #{travelId,jdbcType=BIGINT},
      </if>
      <if test="travelDate != null">
        travel_date = #{travelDate,jdbcType=BIGINT},
      </if>
      <if test="shalfDay != null">
        shalf_day = #{shalfDay,jdbcType=VARCHAR},
      </if>
      <if test="ehalfDay != null">
        ehalf_day = #{ehalfDay,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=INTEGER},
      </if>
      <if test="periodType != null">
        period_type = #{periodType,jdbcType=SMALLINT},
      </if>
      <if test="timeDuration != null">
        time_duration = #{timeDuration,jdbcType=REAL},
      </if>
      <if test="timeUnit != null">
        time_unit = #{timeUnit,jdbcType=SMALLINT},
      </if>
      <if test="dateType != null">
        date_type = #{dateType,jdbcType=INTEGER},
      </if>
      <if test="realDate != null">
        real_date = #{realDate,jdbcType=BIGINT},
      </if>
      <if test="shiftDefId != null">
        shift_def_id = #{shiftDefId,jdbcType=INTEGER},
      </if>
      <if test="applyTimeDuration != null">
        apply_time_duration = #{applyTimeDuration,jdbcType=REAL},
      </if>
      <if test="beforeAdjustTimeDuration != null">
        before_adjust_time_duration = #{beforeAdjustTimeDuration,jdbcType=REAL},
      </if>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=BIGINT},
      </if>
      <if test="extCustomCol != null">
        ext_custom_col = #{extCustomCol,jdbcType=VARCHAR},
      </if>
    </set>
    where travel_daytime_id = #{travelDaytimeId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravelDaytime">
    update wa_emp_travel_daytime
    set travel_id = #{travelId,jdbcType=BIGINT},
      travel_date = #{travelDate,jdbcType=BIGINT},
      shalf_day = #{shalfDay,jdbcType=VARCHAR},
      ehalf_day = #{ehalfDay,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=INTEGER},
      end_time = #{endTime,jdbcType=INTEGER},
      period_type = #{periodType,jdbcType=SMALLINT},
      time_duration = #{timeDuration,jdbcType=REAL},
      time_unit = #{timeUnit,jdbcType=SMALLINT},
      date_type = #{dateType,jdbcType=INTEGER},
      real_date = #{realDate,jdbcType=BIGINT},
      shift_def_id = #{shiftDefId,jdbcType=INTEGER},
      apply_time_duration = #{applyTimeDuration,jdbcType=REAL},
      before_adjust_time_duration = #{beforeAdjustTimeDuration,jdbcType=REAL},
      entity_id = #{entityId,jdbcType=BIGINT},
      ext_custom_col = #{extCustomCol,jdbcType=VARCHAR}
    where travel_daytime_id = #{travelDaytimeId,jdbcType=BIGINT}
  </update>

  <select id="getEmpTravelDaytimeDetailList"
          resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpTravelDaytimeDetail">
    SELECT wet.travel_id ,
    wet.emp_id,
    wet.travel_type_id,
    wet.province,
    wet.city,
    wetd.travel_date,
    wetd.start_time,
    wetd.end_time,
    coalesce(wetd.apply_time_duration, wetd.time_duration) as time_duration,
    wetd.before_adjust_time_duration,
    wetd.period_type,
    wetd.date_type,
    wetd.shalf_day,
    wetd.ehalf_day,
    wetd.time_unit,
    wetd.real_date,
    wtt.acct_time_type,wetd.entity_id
    FROM wa_emp_travel wet
    JOIN wa_emp_travel_daytime wetd on wet.travel_id = wetd.travel_id
    JOIN wa_travel_type wtt ON wet.travel_type_id = wtt.travel_type_id
    left join sys_emp_info e on e.empid=wet.emp_id and e.deleted = 0
    WHERE wet.emp_id = any(${anyEmpIds})
    AND wet.status in <foreach collection="approvalStatusList" open="(" separator="," close=")" item="item">#{item}</foreach>
    AND wet.tenant_id = #{tenantId,jdbcType=VARCHAR}
    AND wetd.time_duration > 0
    AND wetd.real_date > 0
    AND ((wetd.real_date BETWEEN #{startDate} AND #{endDate}) OR (wetd.travel_date BETWEEN #{startDate} AND #{endDate}))
    AND (e.termination_date IS NULL OR coalesce(wetd.real_date, wetd.travel_date) <![CDATA[<=]]> e.termination_date)
    ORDER BY wetd.travel_date, wetd.start_time
  </select>

  <delete id="deleteByTravelId">
    delete from wa_emp_travel_daytime
    <where>
    AND travel_daytime_id IN
      <foreach collection="travelIds" item="travelId" open="(" close=")" separator="," >
        #{travelId,jdbcType=BIGINT}
      </foreach>
    </where>
  </delete>

  <select id="queryEmpTravelDaytimeList" resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpTravelDaytimeDetail">
    SELECT wet.travel_id,
           wet.emp_id,
           wet.travel_type_id,
           wet.province,
           wet.city,
           wetd.travel_date,
           wetd.start_time,
           wetd.end_time,
           coalesce(wetd.apply_time_duration, wetd.time_duration) as time_duration,
           wetd.before_adjust_time_duration,
           wetd.period_type,
           wetd.date_type,
           wetd.shalf_day,
           wetd.ehalf_day,
           wetd.time_unit,
           wetd.real_date,
           wtt.acct_time_type,
           wtt.overtime_rule,
           wtt.auto_transfer_rule,wetd.entity_id,wetd.travel_daytime_id
    FROM wa_emp_travel wet
    JOIN wa_emp_travel_daytime wetd on wet.travel_id = wetd.travel_id
    JOIN wa_travel_type wtt ON wet.travel_type_id = wtt.travel_type_id
    WHERE wet.tenant_id = #{tenantId,jdbcType=VARCHAR}
      <if test="status != null and status.size() > 0">
        AND status in
        <foreach collection="status" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="empIds != null and empIds.size() > 0">
        AND wet.emp_id in
        <foreach collection="empIds" item="empId" open="(" close=")" separator=",">
            #{empId}
        </foreach>
      </if>
    AND wetd.time_duration > 0
    AND ((wetd.real_date BETWEEN #{startDate} AND #{endDate}) OR (wetd.travel_date BETWEEN #{startDate} AND #{endDate}))
  </select>

  <update id="batchUpdate" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravelDaytime">
    <if test="records != null and records.size() > 0">
      <foreach collection="records" item="record" separator=";">
        update wa_emp_travel_daytime
        <set>
          <if test="record.travelId != null">
            travel_id = #{record.travelId,jdbcType=BIGINT},
          </if>
          <if test="record.travelDate != null">
            travel_date = #{record.travelDate,jdbcType=BIGINT},
          </if>
          <if test="record.shalfDay != null">
            shalf_day = #{record.shalfDay,jdbcType=VARCHAR},
          </if>
          <if test="record.ehalfDay != null">
            ehalf_day = #{record.ehalfDay,jdbcType=VARCHAR},
          </if>
          <if test="record.startTime != null">
            start_time = #{record.startTime,jdbcType=INTEGER},
          </if>
          <if test="record.endTime != null">
            end_time = #{record.endTime,jdbcType=INTEGER},
          </if>
          <if test="record.periodType != null">
            period_type = #{record.periodType,jdbcType=SMALLINT},
          </if>
          <if test="record.timeDuration != null">
            time_duration = #{record.timeDuration,jdbcType=REAL},
          </if>
          <if test="record.timeUnit != null">
            time_unit = #{record.timeUnit,jdbcType=SMALLINT},
          </if>
          <if test="record.dateType != null">
            date_type = #{record.dateType,jdbcType=INTEGER},
          </if>
          <if test="record.realDate != null">
            real_date = #{record.realDate,jdbcType=BIGINT},
          </if>
          <if test="record.shiftDefId != null">
            shift_def_id = #{record.shiftDefId,jdbcType=INTEGER},
          </if>
          <if test="record.applyTimeDuration != null">
            apply_time_duration = #{record.applyTimeDuration,jdbcType=REAL},
          </if>
          <if test="record.beforeAdjustTimeDuration != null">
            before_adjust_time_duration = #{record.beforeAdjustTimeDuration,jdbcType=REAL},
          </if>
          <if test="record.entityId != null">
            entity_id = #{record.entityId,jdbcType=BIGINT},
          </if>
        </set>
        where travel_daytime_id = #{record.travelDaytimeId,jdbcType=BIGINT}
      </foreach>
    </if>
  </update>

  <select id="queryEmpTravelDaytimeByTravelId" resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpTravelDaytimeDetail">
    select * from wa_emp_travel_daytime
    where 1=1
    <if test="travelIds!=null and travelIds.size()>0">
    and travel_id in
      <foreach collection="travelIds" item="travelId" separator="," open="(" close=")">
        #{travelId}
      </foreach>
    </if>
  </select>

  <select id="queryEmpTravelDaytime" resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpTravelDaytimeDetail">
    select * from wa_emp_travel_daytime wetd
    join wa_emp_travel wet on wet.travel_id=wetd.travel_id
    join wa_travel_transfer_compensatory wttc on wttc.id=wetd.entity_id
    where wet.tenant_id=#{tenantId,jdbcType=VARCHAR} and wttc.deleted=0 and wttc.status=1
    <if test="travelIds!=null and travelIds.size()>0">
      and wetd.travel_id in
      <foreach collection="travelIds" item="travelId" separator="," open="(" close=")">
        #{travelId}
      </foreach>
    </if>
  </select>

  <select id="queryTravelDayTimeList"  resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpTravelDaytimeDetail">
    select
      wtd.travel_daytime_id,
      wtt.travel_type_name travelType,
      wtt.travel_type_id,
      wtd.travel_id,
      wtd.time_duration,
      wet.time_unit,
      wet.period_type,
      wet.start_time,
      wet.end_time,
      wet.shift_start_time,
      wet.shift_end_time,
      wet.shalf_day,
      wet.ehalf_day,
      wet.travel_mode,
      wet.province,
      wet.county,
      wet.city,
      wet.status,
      wet.last_approval_time,
      wet.reason,
      wet.create_time,
      wtd.travel_date,wtd.real_date
    from wa_emp_travel wet
           join wa_emp_travel_daytime wtd on wtd.travel_id = wet.travel_id
           join sys_emp_info ei on ei.empid = wet.emp_id and ei.deleted = 0
           left join wa_travel_type wtt on wtt.travel_type_id = wet.travel_type_id
    where wet.deleted=0 and ei.belong_org_id = #{tenantId}
      and wet.emp_id = #{empId} and wet.status in (1,2) and wtd.travel_date between #{dayTime} and #{endTime}
  </select>

  <select id="selectTravelDaytimeList"
          resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravelDaytime">
    select wetd.travel_date,
           wetd.start_time,
           wetd.end_time,
           wetd.shalf_day,
           wetd.ehalf_day,
           wetd.period_type,
           wetd.time_duration,
           wetd.time_unit,
           wetd.shift_def_id
    from wa_emp_travel_daytime wetd
           join wa_emp_travel wet on wet.travel_id = wetd.travel_id
    where wet.tenant_id = #{tenantId}
      and wet.emp_id = #{empId}
      and wetd.travel_date between #{startDate} and #{endDate}
      and wet.status in <foreach collection="status" open="(" separator="," close=")" item="item">#{item}</foreach>
      and wetd.period_type in <foreach collection="periodTypes" open="(" separator="," close=")" item="item">#{item}</foreach>
  </select>
</mapper>