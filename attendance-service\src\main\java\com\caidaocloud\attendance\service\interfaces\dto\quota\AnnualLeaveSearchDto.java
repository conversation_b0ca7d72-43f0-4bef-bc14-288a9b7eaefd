package com.caidaocloud.attendance.service.interfaces.dto.quota;

import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 年假明细搜索
 */
@Data
@ApiModel("年假明细搜索")
public class AnnualLeaveSearchDto extends ExportBasePage {
    @ApiModelProperty("员工id")
    private Long empid;

    @ApiModelProperty("员工id")
    private List<Long> empIdList;

    @ApiModelProperty("年份")
    private List<Integer> periodYear;

    @ApiModelProperty("状态：true 生效，false 失效")
    private Boolean annualLeaveStatus;

    @ApiModelProperty("搜索项：姓名")
    private String keywords;

    @ApiModelProperty("搜索项：开始时间")
    private Long startDate;

    @ApiModelProperty("搜索项：结束时间")
    private Long endDate;

    @ApiModelProperty("假期类型")
    private List<Integer> leaveType;
}
