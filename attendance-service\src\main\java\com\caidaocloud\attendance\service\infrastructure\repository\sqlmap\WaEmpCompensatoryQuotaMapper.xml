<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpCompensatoryQuotaMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryQuotaPo">
    <id column="quota_id" jdbcType="BIGINT" property="quotaId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="emp_id" jdbcType="BIGINT" property="empId" />
    <result column="leave_type_id" jdbcType="INTEGER" property="leaveTypeId" />
    <result column="emp_quota_id" jdbcType="INTEGER" property="empQuotaId" />
    <result column="overtime_detail_id" jdbcType="INTEGER" property="overtimeDetailId" />
    <result column="overtime_date" jdbcType="BIGINT" property="overtimeDate" />
    <result column="overtime_type" jdbcType="INTEGER" property="overtimeType" />
    <result column="overtime_duration" jdbcType="REAL" property="overtimeDuration" />
    <result column="overtime_unit" jdbcType="INTEGER" property="overtimeUnit" />
    <result column="working_time" jdbcType="REAL" property="workingTime" />
    <result column="quota_unit" jdbcType="INTEGER" property="quotaUnit" />
    <result column="quota_day" jdbcType="REAL" property="quotaDay" />
    <result column="used_day" jdbcType="REAL" property="usedDay" />
    <result column="in_transit_quota" jdbcType="REAL" property="inTransitQuota" />
    <result column="validity_period_type" jdbcType="INTEGER" property="validityPeriodType" />
    <result column="start_date" jdbcType="BIGINT" property="startDate" />
    <result column="last_date" jdbcType="BIGINT" property="lastDate" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="emp_name" jdbcType="VARCHAR" property="empName" />
    <result column="leave_name" jdbcType="VARCHAR" property="leaveTypeName" />
    <result column="workno" jdbcType="VARCHAR" property="workno" />
    <result column="adjust_quota_day" jdbcType="REAL" property="adjustQuotaDay" />
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />

    <result column="original_quota_day" jdbcType="REAL" property="originalQuotaDay" />
    <result column="i18n_leave_name" jdbcType="VARCHAR" property="i18nLeaveTypeName" />
    <result column="entity_id" jdbcType="VARCHAR" property="entityId" />
  </resultMap>
  <sql id="Base_Column_List">
    quota.quota_id, quota.tenant_id, quota.emp_id, quota.leave_type_id, quota.emp_quota_id,quota.overtime_detail_id, quota.overtime_date,
    quota.overtime_type,
    quota.overtime_duration, quota.overtime_unit, quota.working_time, quota.quota_unit, quota.quota_day, quota.used_day,
    quota.in_transit_quota, quota.validity_period_type, quota.start_date, quota.last_date, quota.status, quota.deleted,
    quota.create_by,
    quota.create_time, quota.update_by, quota.update_time, emp.emp_name, leave_type.leave_name, emp.workno,adjust_quota_day,remark,emp.employ_type,emp.orgid "orgid",
    quota.config_id,quota.data_source,quota.original_quota_day, leave_type.i18n_leave_name,quota.entity_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wa_emp_compensatory_quota quota join sys_emp_info emp on quota.emp_id = emp.empid
    join wa_leave_type leave_type on quota.leave_type_id = leave_type.leave_type_id
    where quota.quota_id = #{quotaId,jdbcType=BIGINT} and emp.deleted = 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_emp_compensatory_quota
    where quota_id = #{quotaId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryQuotaPo">
    insert into wa_emp_compensatory_quota (quota_id, tenant_id, emp_id,
      leave_type_id, emp_quota_id,overtime_detail_id,overtime_date,
      overtime_type, overtime_duration, overtime_unit,
      working_time, quota_unit, quota_day,
      used_day, in_transit_quota, validity_period_type,
      start_date, last_date, status,
      deleted, create_by, create_time,
      update_by, update_time,adjust_quota_day,remark,config_id,data_source,original_quota_day,
      entity_id)
    values (#{quotaId,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{empId,jdbcType=BIGINT},
      #{leaveTypeId,jdbcType=INTEGER}, #{empQuotaId,jdbcType=INTEGER}, #{overtimeDetailId,jdbcType=INTEGER}, #{overtimeDate,jdbcType=BIGINT},
      #{overtimeType,jdbcType=INTEGER}, #{overtimeDuration,jdbcType=REAL}, #{overtimeUnit,jdbcType=INTEGER},
      #{workingTime,jdbcType=REAL}, #{quotaUnit,jdbcType=INTEGER}, #{quotaDay,jdbcType=REAL},
      #{usedDay,jdbcType=REAL}, #{inTransitQuota,jdbcType=REAL}, #{validityPeriodType,jdbcType=INTEGER},
      #{startDate,jdbcType=BIGINT}, #{lastDate,jdbcType=BIGINT}, #{status,jdbcType=INTEGER},
      #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT},
      #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{adjustQuotaDay,jdbcType=REAL},#{remark,jdbcType=VARCHAR},
      #{configId,jdbcType=BIGINT},#{dataSource,jdbcType=VARCHAR},#{originalQuotaDay,jdbcType=REAL},#{entityId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryQuotaPo">
    insert into wa_emp_compensatory_quota
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="quotaId != null">
        quota_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="leaveTypeId != null">
        leave_type_id,
      </if>
      <if test="empQuotaId != null">
        emp_quota_id,
      </if>
      <if test="overtimeDetailId != null">
        overtime_detail_id,
      </if>
      <if test="overtimeDate != null">
        overtime_date,
      </if>
      <if test="overtimeType != null">
        overtime_type,
      </if>
      <if test="overtimeDuration != null">
        overtime_duration,
      </if>
      <if test="overtimeUnit != null">
        overtime_unit,
      </if>
      <if test="workingTime != null">
        working_time,
      </if>
      <if test="quotaUnit != null">
        quota_unit,
      </if>
      <if test="quotaDay != null">
        quota_day,
      </if>
      <if test="usedDay != null">
        used_day,
      </if>
      <if test="inTransitQuota != null">
        in_transit_quota,
      </if>
      <if test="validityPeriodType != null">
        validity_period_type,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="lastDate != null">
        last_date,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="adjustQuotaDay != null">
        adjust_quota_day,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="configId != null">
        config_id,
      </if>
      <if test="dataSource != null">
        data_source,
      </if>
      <if test="originalQuotaDay != null">
        original_quota_day,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="quotaId != null">
        #{quotaId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=BIGINT},
      </if>
      <if test="leaveTypeId != null">
        #{leaveTypeId,jdbcType=INTEGER},
      </if>
      <if test="empQuotaId != null">
        #{empQuotaId,jdbcType=INTEGER},
      </if>
      <if test="overtimeDetailId != null">
        #{overtimeDetailId,jdbcType=INTEGER},
      </if>
      <if test="overtimeDate != null">
        #{overtimeDate,jdbcType=BIGINT},
      </if>
      <if test="overtimeType != null">
        #{overtimeType,jdbcType=INTEGER},
      </if>
      <if test="overtimeDuration != null">
        #{overtimeDuration,jdbcType=REAL},
      </if>
      <if test="overtimeUnit != null">
        #{overtimeUnit,jdbcType=INTEGER},
      </if>
      <if test="workingTime != null">
        #{workingTime,jdbcType=REAL},
      </if>
      <if test="quotaUnit != null">
        #{quotaUnit,jdbcType=INTEGER},
      </if>
      <if test="quotaDay != null">
        #{quotaDay,jdbcType=REAL},
      </if>
      <if test="usedDay != null">
        #{usedDay,jdbcType=REAL},
      </if>
      <if test="inTransitQuota != null">
        #{inTransitQuota,jdbcType=REAL},
      </if>
      <if test="validityPeriodType != null">
        #{validityPeriodType,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=BIGINT},
      </if>
      <if test="lastDate != null">
        #{lastDate,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="adjustQuotaDay != null">
        #{adjustQuotaDay,jdbcType=REAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="dataSource != null">
        #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="originalQuotaDay != null">
        #{originalQuotaDay,jdbcType=REAL},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch">
    <foreach collection="records" item="record" separator=";">
      insert into wa_emp_compensatory_quota
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="record.quotaId != null">
          quota_id,
        </if>
        <if test="record.tenantId != null">
          tenant_id,
        </if>
        <if test="record.empId != null">
          emp_id,
        </if>
        <if test="record.leaveTypeId != null">
          leave_type_id,
        </if>
        <if test="record.empQuotaId != null">
          emp_quota_id,
        </if>
        <if test="record.overtimeDetailId != null">
          overtime_detail_id,
        </if>
        <if test="record.overtimeDate != null">
          overtime_date,
        </if>
        <if test="record.overtimeType != null">
          overtime_type,
        </if>
        <if test="record.overtimeDuration != null">
          overtime_duration,
        </if>
        <if test="record.overtimeUnit != null">
          overtime_unit,
        </if>
        <if test="record.workingTime != null">
          working_time,
        </if>
        <if test="record.quotaUnit != null">
          quota_unit,
        </if>
        <if test="record.quotaDay != null">
          quota_day,
        </if>
        <if test="record.usedDay != null">
          used_day,
        </if>
        <if test="record.inTransitQuota != null">
          in_transit_quota,
        </if>
        <if test="record.validityPeriodType != null">
          validity_period_type,
        </if>
        <if test="record.startDate != null">
          start_date,
        </if>
        <if test="record.lastDate != null">
          last_date,
        </if>
        <if test="record.status != null">
          status,
        </if>
        <if test="record.deleted != null">
          deleted,
        </if>
        <if test="record.createBy != null">
          create_by,
        </if>
        <if test="record.createTime != null">
          create_time,
        </if>
        <if test="record.updateBy != null">
          update_by,
        </if>
        <if test="record.updateTime != null">
          update_time,
        </if>
        <if test="record.adjustQuotaDay != null">
          adjust_quota_day,
        </if>
        <if test="record.remark != null">
          remark,
        </if>
        <if test="record.configId != null">
          config_id,
        </if>
        <if test="record.dataSource != null">
          data_source,
        </if>
        <if test="record.originalQuotaDay != null">
          original_quota_day,
        </if>
        <if test="record.entityId != null">
          entity_id,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="record.quotaId != null">
          #{record.quotaId,jdbcType=BIGINT},
        </if>
        <if test="record.tenantId != null">
          #{record.tenantId,jdbcType=VARCHAR},
        </if>
        <if test="record.empId != null">
          #{record.empId,jdbcType=BIGINT},
        </if>
        <if test="record.leaveTypeId != null">
          #{record.leaveTypeId,jdbcType=INTEGER},
        </if>
        <if test="record.empQuotaId != null">
          #{record.empQuotaId,jdbcType=INTEGER},
        </if>
        <if test="record.overtimeDetailId != null">
          #{record.overtimeDetailId,jdbcType=INTEGER},
        </if>
        <if test="record.overtimeDate != null">
          #{record.overtimeDate,jdbcType=BIGINT},
        </if>
        <if test="record.overtimeType != null">
          #{record.overtimeType,jdbcType=INTEGER},
        </if>
        <if test="record.overtimeDuration != null">
          #{record.overtimeDuration,jdbcType=REAL},
        </if>
        <if test="record.overtimeUnit != null">
          #{record.overtimeUnit,jdbcType=INTEGER},
        </if>
        <if test="record.workingTime != null">
          #{record.workingTime,jdbcType=REAL},
        </if>
        <if test="record.quotaUnit != null">
          #{record.quotaUnit,jdbcType=INTEGER},
        </if>
        <if test="record.quotaDay != null">
          #{record.quotaDay,jdbcType=REAL},
        </if>
        <if test="record.usedDay != null">
          #{record.usedDay,jdbcType=REAL},
        </if>
        <if test="record.inTransitQuota != null">
          #{record.inTransitQuota,jdbcType=REAL},
        </if>
        <if test="record.validityPeriodType != null">
          #{record.validityPeriodType,jdbcType=INTEGER},
        </if>
        <if test="record.startDate != null">
          #{record.startDate,jdbcType=BIGINT},
        </if>
        <if test="record.lastDate != null">
          #{record.lastDate,jdbcType=BIGINT},
        </if>
        <if test="record.status != null">
          #{record.status,jdbcType=INTEGER},
        </if>
        <if test="record.deleted != null">
          #{record.deleted,jdbcType=INTEGER},
        </if>
        <if test="record.createBy != null">
          #{record.createBy,jdbcType=BIGINT},
        </if>
        <if test="record.createTime != null">
          #{record.createTime,jdbcType=BIGINT},
        </if>
        <if test="record.updateBy != null">
          #{record.updateBy,jdbcType=BIGINT},
        </if>
        <if test="record.updateTime != null">
          #{record.updateTime,jdbcType=BIGINT},
        </if>
        <if test="record.adjustQuotaDay != null">
          #{record.adjustQuotaDay,jdbcType=REAL},
        </if>
        <if test="record.remark != null">
          #{record.remark,jdbcType=VARCHAR},
        </if>
        <if test="record.configId != null">
          #{record.configId,jdbcType=BIGINT},
        </if>
        <if test="record.dataSource != null">
          #{record.dataSource,jdbcType=VARCHAR},
        </if>
        <if test="record.originalQuotaDay != null">
          #{record.originalQuotaDay,jdbcType=REAL},
        </if>
        <if test="record.entityId != null">
          #{record.entityId,jdbcType=VARCHAR},
        </if>
      </trim>
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryQuotaPo">
    update wa_emp_compensatory_quota
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=BIGINT},
      </if>
      <if test="leaveTypeId != null">
        leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
      </if>
      <if test="empQuotaId != null">
        emp_quota_id = #{empQuotaId,jdbcType=INTEGER},
      </if>
      <if test="overtimeDetailId != null">
        overtime_detail_id = #{overtimeDetailId,jdbcType=INTEGER},
      </if>
      <if test="overtimeDate != null">
        overtime_date = #{overtimeDate,jdbcType=BIGINT},
      </if>
      <if test="overtimeType != null">
        overtime_type = #{overtimeType,jdbcType=INTEGER},
      </if>
      <if test="overtimeDuration != null">
        overtime_duration = #{overtimeDuration,jdbcType=REAL},
      </if>
      <if test="overtimeUnit != null">
        overtime_unit = #{overtimeUnit,jdbcType=INTEGER},
      </if>
      <if test="workingTime != null">
        working_time = #{workingTime,jdbcType=REAL},
      </if>
      <if test="quotaUnit != null">
        quota_unit = #{quotaUnit,jdbcType=INTEGER},
      </if>
      <if test="quotaDay != null">
        quota_day = #{quotaDay,jdbcType=REAL},
      </if>
      <if test="usedDay != null">
        used_day = #{usedDay,jdbcType=REAL},
      </if>
      <if test="inTransitQuota != null">
        in_transit_quota = #{inTransitQuota,jdbcType=REAL},
      </if>
      <if test="validityPeriodType != null">
        validity_period_type = #{validityPeriodType,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=BIGINT},
      </if>
      <if test="lastDate != null">
        last_date = #{lastDate,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="adjustQuotaDay != null">
        adjust_quota_day = #{adjustQuotaDay,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="configId != null">
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="dataSource != null">
        data_source = #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="originalQuotaDay != null">
        original_quota_day = #{originalQuotaDay,jdbcType=REAL},
      </if>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=VARCHAR},
      </if>
    </set>
    where quota_id = #{quotaId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryQuotaPo">
    update wa_emp_compensatory_quota
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      emp_id = #{empId,jdbcType=BIGINT},
      leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
      emp_quota_id = #{empQuotaId,jdbcType=INTEGER},
      overtime_detail_id = #{overtimeDetailId,jdbcType=INTEGER},
      overtime_date = #{overtimeDate,jdbcType=BIGINT},
      overtime_type = #{overtimeType,jdbcType=INTEGER},
      overtime_duration = #{overtimeDuration,jdbcType=REAL},
      overtime_unit = #{overtimeUnit,jdbcType=INTEGER},
      working_time = #{workingTime,jdbcType=REAL},
      quota_unit = #{quotaUnit,jdbcType=INTEGER},
      quota_day = #{quotaDay,jdbcType=REAL},
      used_day = #{usedDay,jdbcType=REAL},
      in_transit_quota = #{inTransitQuota,jdbcType=REAL},
      validity_period_type = #{validityPeriodType,jdbcType=INTEGER},
      start_date = #{startDate,jdbcType=BIGINT},
      last_date = #{lastDate,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      adjust_quota_day = #{adjustQuotaDay,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      config_id = #{record.configId,jdbcType=BIGINT},
      data_source = #{dataSource,jdbcType=VARCHAR},
      original_quota_day = #{originalQuotaDay,jdbcType=REAL},
      entity_id = #{entityId,jdbcType=VARCHAR}
    where quota_id = #{record.quotaId,jdbcType=BIGINT}
  </update>
  <select id="getEmpCompensatoryQuotaList" resultMap="BaseResultMap">
    SELECT * FROM (
    select
    <include refid="Base_Column_List" />
    from wa_emp_compensatory_quota quota join sys_emp_info emp on quota.emp_id = emp.empid and emp.deleted = 0
    join wa_leave_type leave_type on quota.leave_type_id = leave_type.leave_type_id
    where quota.tenant_id = #{tenantId,jdbcType=VARCHAR}
    and quota.deleted = 0 and quota.status!=9
    <if test="overtimeType != null and overtimeType.size() >0">
      and quota.overtime_type in <foreach collection="overtimeType" item="overtimeTypeItem" open="(" separator="," close=")">#{overtimeTypeItem}</foreach>
    </if>
    <if test="overtimeDate != null">
      and quota.overtime_date = #{overtimeDate,jdbcType=BIGINT}
    </if>
    <if test="status == 0">
      and (quota.status = 2 and #{currentTime,jdbcType=BIGINT} > quota.last_date
      or quota.status in (1,3,5))
    </if>
    <if test="status == 1">
      and quota.status = 2 and quota.last_date > #{currentTime,jdbcType=BIGINT}
    </if>
    <if test="status == 8">
      and quota.status = 8
    </if>
    <if test="keywords != null and keywords != ''">
      and (emp.emp_name like concat('%', #{keywords}, '%') or emp.workno like concat('%', #{keywords}, '%'))
    </if>
    <if test="params.empIdList != null and params.empIdList.size() > 0">
      AND emp.empid IN <foreach collection="params.empIdList" item="emp" open="(" separator="," close=")">#{emp}</foreach>
    </if>
    <if test="params.datafilter != null and params.datafilter != ''">
      ${params.datafilter}
    </if>
    ) AS t
    <where>
      <if test="params.filter != null and params.filter != ''">
        ${params.filter}
      </if>
    </where>
  </select>
  <update id="logicDelete">
    update wa_emp_compensatory_quota set deleted =1 where quota_id = #{quotaId}
  </update>
  <update id="logicDeleteByIds">
    update wa_emp_compensatory_quota set deleted =1 where tenant_id = #{tenantId,jdbcType=VARCHAR}
    and quota_id in <foreach collection="quotaIds" open="(" separator="," close=")" item="item">#{item}</foreach>
  </update>

  <select id="queryEmpQuotaList" resultType="map">
    SELECT
    ep.empid AS "empId",
    COALESCE ( ep.quota_day, 0 ) AS "quota_day",
    COALESCE ( ep.now_quota, 0 ) AS "now_quota",
    COALESCE ( ep.used_day, 0 ) AS "used_day",
    COALESCE ( ep.adjust_quota, 0 ) AS "adjust_quota",
    COALESCE ( ep.fix_used_day, 0 ) AS "fix_used_day",
    COALESCE ( ep.in_transit_quota, 0 ) AS "in_transit_quota",
    ep.if_advance,
    lt.acct_time_type,
    lt.leave_type_id,
    lt.leave_type,
    lt.leave_name,
    lt.i18n_leave_name,
    lt.quota_type,
    ep.start_date,
    ep.last_date
    FROM
    wa_emp_quota ep
    JOIN wa_leave_type lt ON lt.leave_type_id = ep.leave_type_id
    where empId in
    <foreach collection="empids" open="(" separator="," close=")" item="item">#{item}</foreach>
    and #{today} BETWEEN start_date and last_date
    <if test="leaveTypeId != null">
      and lt.leave_type_id = #{leaveTypeId}
    </if>
    <if test="leaveTypeIds != null and leaveTypeIds.size > 0">
      and lt.leave_type_id in <foreach collection="leaveTypeIds" open="(" separator="," close=")" item="item">#{item}</foreach>
    </if>
    UNION ALL
    SELECT
    quota.emp_id AS "empId",
    COALESCE ( quota.quota_day, 0 ) AS "quota_day",
    COALESCE ( quota.quota_day, 0 ) AS "now_quota",
    COALESCE ( quota.used_day, 0 ) AS "used_day",
    COALESCE ( quota.adjust_quota_day, 0) AS "adjust_quota",
    0 AS "fix_used_day",
    quota.in_transit_quota,
    0 AS "if_advance",
    quota_unit AS "acct_time_type",
    lt.leave_type_id,
    lt.leave_type,
    lt.leave_name,
    lt.i18n_leave_name,
    lt.quota_type,
    start_date,
    last_date
    FROM
    wa_emp_compensatory_quota quota
    JOIN wa_leave_type lt ON quota.leave_type_id = lt.leave_type_id
    WHERE
    quota.deleted = 0
    AND quota.status = 2
    AND #{today} BETWEEN quota.start_date AND quota.last_date
    AND emp_id in
    <foreach collection="empids" open="(" separator="," close=")" item="item">#{item}</foreach>
    <if test="leaveTypeId != null">
      AND lt.leave_type_id = #{leaveTypeId}
    </if>
    <if test="leaveTypeIds != null and leaveTypeIds.size > 0">
      and lt.leave_type_id in <foreach collection="leaveTypeIds" open="(" separator="," close=")" item="item">#{item}</foreach>
    </if>
  </select>

  <select id="queryQuotaListByIds" resultMap="BaseResultMap">
    select * from wa_emp_compensatory_quota
    where tenant_id = #{tenantId,jdbcType=VARCHAR} and deleted = 0
    <if test="quotaIds != null and quotaIds.size() > 0">
      and quota_id in
      <foreach collection="quotaIds" open="(" separator="," close=")" item="quotaId">
      #{quotaId}
      </foreach>
    </if>
  </select>

  <select id="queryApplyCompensatoryQuotaList" resultMap="BaseResultMap">
    select wecq.* from wa_emp_compensatory_quota wecq
    join wa_leave_quota_config wlqc on wlqc.config_id=wecq.config_id and wlqc.deleted = 0
    where wecq.tenant_id = #{tenantId,jdbcType=VARCHAR} and wlqc.expire_handle=2 and wecq.deleted = 0
    <if test="quotaIds != null and quotaIds.size() > 0">
      and quota_id in
      <foreach collection="quotaIds" open="(" separator="," close=")" item="quotaId">
        #{quotaId}
      </foreach>
    </if>
    <if test="status != null">
      <if test="status == 0">
        and (status = 2 or status in (1,3,5)) and last_date between #{currentTime,jdbcType=BIGINT} and #{currentTime,jdbcType=BIGINT} + 86399
      </if>
      <if test="status == 1">
        and status = 2 and last_date > #{currentTime,jdbcType=BIGINT}
      </if>
    </if>
  </select>

  <select id="queryEmpCompensatoryQuotaList" resultMap="BaseResultMap">
    select *
    from wa_emp_compensatory_quota
    <where>
        and deleted=0
      <if test="quotaIds != null and quotaIds.size() > 0">
        and quota_id in
        <foreach collection="quotaIds" open="(" close=")" separator="," item="quotaId">
          #{quotaId}
        </foreach>
      </if>
    </where>
  </select>

  <select id="groupCompensatoryQuotaByEmpIdsAndTime" resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryQuotaPo">
    select emp_id, sum(quota_day) quota_day, sum(adjust_quota_day) adjust_quota_day, sum(in_transit_quota) in_transit_quota,sum(used_day) used_day from wa_emp_compensatory_quota quota
    where quota.tenant_id = #{tenantId,jdbcType=VARCHAR}
    and quota.deleted = 0 and quota.status!=9
    and quota.emp_id in
    <foreach collection="empIds" open="(" close=")" separator="," item="empId">
      #{empId,jdbcType=BIGINT}
    </foreach>
    and quota.overtime_date &gt;= #{startTime,jdbcType=BIGINT}
    and quota.overtime_date &lt;= #{endTime,jdbcType=BIGINT}
    group by emp_id
  </select>

  <select id="groupManualCompensatoryQuotaByEmpIds" resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryQuotaPo">
    select emp_id, sum(quota_day) quota_day, sum(adjust_quota_day) adjust_quota_day, sum(in_transit_quota) in_transit_quota,sum(used_day) used_day from wa_emp_compensatory_quota quota
    where quota.tenant_id = #{tenantId,jdbcType=VARCHAR}
    and quota.deleted = 0 and quota.status!=9
    and quota.emp_id in
    <foreach collection="empIds" open="(" close=")" separator="," item="empId">
      #{empId,jdbcType=BIGINT}
    </foreach>
    and quota.start_date &lt;= #{now,jdbcType=BIGINT}
    and quota.last_date &gt;= #{now,jdbcType=BIGINT}
    and data_source = 'MANUAL'
    group by emp_id
  </select>

</mapper>