package com.caidaocloud.attendance.service.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCase;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryQuotaPo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface WaEmpCompensatoryQuotaMapper extends BaseMapper<WaEmpCompensatoryQuotaPo> {
    int deleteByPrimaryKey(Long quotaId);

    int insert(WaEmpCompensatoryQuotaPo record);

    int insertBatch(@Param("records") List<WaEmpCompensatoryQuotaPo> records);

    int insertSelective(WaEmpCompensatoryQuotaPo record);

    WaEmpCompensatoryQuotaPo selectByPrimaryKey(Long quotaId);

    int updateByPrimaryKeySelective(WaEmpCompensatoryQuotaPo record);

    int updateByPrimaryKey(WaEmpCompensatoryQuotaPo record);

    PageList<WaEmpCompensatoryQuotaPo> getEmpCompensatoryQuotaList(
            @Param("myPageBounds") MyPageBounds myPageBounds, @Param("overtimeType") List<Integer> overtimeType,
            @Param("overtimeDate") Long overtimeDate, @Param("status") Integer status, @Param("tenantId") String tenantId,
            @Param("currentTime") Long currentTime, @Param("keywords") String keywords, Map<String, Object> params);

    void logicDelete(Long quotaId);

    void logicDeleteByIds(@Param("tenantId") String tenantId, @Param("quotaIds") List<Long> quotaIds);

    List<Map> queryEmpQuotaList(Map params);

    List<WaEmpCompensatoryQuotaPo> queryQuotaListByIds(@Param("tenantId") String tenantId, @Param("quotaIds") Collection<Long> quotaIds);

    List<WaEmpCompensatoryQuotaPo> queryApplyCompensatoryQuotaList(@Param("tenantId") String tenantId,
                                                                   @Param("quotaIds") Collection<Long> quotaIds,
                                                                   @Param("status") Integer status,
                                                                   @Param("currentTime") Long currentTime);

    List<WaEmpCompensatoryQuotaPo> queryEmpCompensatoryQuotaList(@Param("quotaIds") List<Long> quotaIds);

    List<WaEmpCompensatoryQuotaPo> groupCompensatoryQuotaByEmpIdsAndTime(@Param("tenantId") String tenantId, @Param("empIds") List<Long> empIds,
                                               @Param("startTime") long startTime, @Param("endTime") long endTime);

    List<WaEmpCompensatoryQuotaPo> groupManualCompensatoryQuotaByEmpIds(@Param("tenantId") String tenantId, @Param("empIds") List<Long> empIds,
                                                                         @Param("now") long now);
}