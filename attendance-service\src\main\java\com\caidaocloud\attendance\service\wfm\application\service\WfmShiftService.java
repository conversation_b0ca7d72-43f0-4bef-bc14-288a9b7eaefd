package com.caidaocloud.attendance.service.wfm.application.service;

import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.ioc.dto.CheckMessage;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftBelongModuleEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.attendance.core.wa.vo.BladeShiftPageResultVo;
import com.caidaocloud.attendance.core.wa.vo.MultiShiftSimpleVo;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.enums.ShiftBusinessProcessEnum;
import com.caidaocloud.attendance.service.application.enums.ShiftBusinessTypeEnum;
import com.caidaocloud.attendance.service.application.service.ICacheCommonService;
import com.caidaocloud.attendance.service.application.service.IShiftService;
import com.caidaocloud.attendance.service.application.service.impl.WaCustomizeShiftDefService;
import com.caidaocloud.attendance.service.domain.entity.SysEmpInfoDo;
import com.caidaocloud.attendance.service.domain.entity.WaCustomizeShiftDefDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.infrastructure.util.DateUtil;
import com.caidaocloud.attendance.service.infrastructure.util.FileUtil;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.attendance.service.interfaces.dto.shift.BladeShiftPageQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.CustomizeShiftDefQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ShiftPageDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.multi.*;
import com.caidaocloud.attendance.service.interfaces.vo.shift.*;
import com.caidaocloud.attendance.service.schedule.application.service.dto.EmpWorkInfo;
import com.caidaocloud.attendance.service.schedule.application.service.dto.WfmProcessInfo;
import com.caidaocloud.attendance.service.schedule.application.service.schedule.IEmpScheduleService;
import com.caidaocloud.attendance.service.schedule.application.service.shiftGroup.impl.WaShiftGroupService;
import com.caidaocloud.attendance.service.schedule.interfaces.vo.shiftGroup.WaShiftGroup2Vo;
import com.caidaocloud.attendance.service.wfm.application.dto.WfmOrderInfo;
import com.caidaocloud.attendance.service.wfm.domain.entity.WfmShiftChangeDo;
import com.caidaocloud.attendance.service.wfm.interfaces.dto.*;
import com.caidaocloud.attendance.service.wfm.interfaces.vo.WfmShiftChangeVo;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.*;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import java.io.DataInputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 工时管理-班次设置
 *
 * <AUTHOR>
 * @Date 2025/1/26
 */
@Slf4j
@Service
public class WfmShiftService {
    @Autowired
    private IShiftService shiftService;
    @Autowired
    private WaCustomizeShiftDefService waCustomizeShiftDefService;
    @Autowired
    private WfmShiftChangeDo wfmShiftChangeDo;
    @Autowired
    private SysEmpInfoDo sysEmpInfoDo;
    @Autowired
    private ICacheCommonService cacheCommonService;
    @Autowired
    private WaShiftGroupService waShiftGroupService;
    @Autowired
    private IEmpScheduleService empScheduleService;

    private final static String WORK_PROCESS_IDENTIFIER = "entity.wfm.ProcessManagement";
    private final static String WFM_ORDER_IDENTIFIER = "entity.wfm.OrderManagement";
    private final static String EMP_WORK_INFO_IDENTIFIER = "entity.hr.EmpWorkInfo";
    private static final int MAX_PAGE_SIZE = 5000;

    private final ConcurrentHashMap<String, CacheEntry<List<MultiShiftSimpleVo>>> stdListCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, CacheEntry<List<MultiShiftSimpleVo>>> custListCache = new ConcurrentHashMap<>();
    private static final String STD_LIST_CACHE_KEY = "stdListCacheKey";
    private static final long CACHE_EXPIRATION_TIME = 60 * 60 * 1000;
    private final ReentrantLock cacheLock = new ReentrantLock();
    private final ReentrantLock custCacheLock = new ReentrantLock();


    private final SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    /**
     * 查询班次分页列表（标准班次或自定义班次）
     *
     * @param pageQueryDto
     * @return
     */
    public AttendancePageResult<MultiShiftPageVo> getPageResult(ShiftPageDto pageQueryDto) {
        pageQueryDto.setBelongModule(ShiftBelongModuleEnum.WFM.getCode());
        AttendancePageResult<WaShiftDo> pageResult = shiftService.getPageList(pageQueryDto, UserContext.getAndCheckUser());
        if (null == pageResult || CollectionUtils.isEmpty(pageResult.getItems())) {
            return new AttendancePageResult<>(new ArrayList<>(), pageQueryDto.getPageNo(), pageQueryDto.getPageSize(), 0);
        }
        List<MultiShiftPageVo> voList = ObjectConverter.convertList(pageResult.getItems(), MultiShiftPageVo.class);
        voList.forEach(row -> {
            row.setDateType(DateTypeEnum.getName(Integer.parseInt(row.getDateType().toString())));
            row.doSetMultiWorkTimes();
        });
        return new AttendancePageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    /**
     * 标准班次-保存
     *
     * @param saveDto
     */
    @Transactional
    public Integer save(MultiShiftSlightSaveDto saveDto) {
        try {
            MultiShiftSaveDto dto = FastjsonUtil.convertObject(saveDto, MultiShiftSaveDto.class);
            dto.setBelongModule(ShiftBelongModuleEnum.WFM.getCode());
            return shiftService.saveMultiShiftDef(dto);
        } finally {
            clearStdListCache();
        }
    }

    /**
     * 标准班次-详情
     *
     * @param id
     * @return
     */
    public MultiShiftSlightVo getById(Integer id) {
        MultiShiftVo shiftVo = shiftService.getById(id);
        return FastjsonUtil.convertObject(shiftVo, MultiShiftSlightVo.class);
    }

    public List<WaShiftDo> getListByStTime(String tenantId, ShiftBelongModuleEnum belongModule,
                                           Boolean temporaryShift, Integer startTime, Integer endTime,
                                           Integer startTimeBelong, Integer endTimeBelong) {
        return shiftService.getListByStTime(tenantId, belongModule, temporaryShift, startTime, endTime, startTimeBelong, endTimeBelong);
    }

    /**
     * 标准班次-删除
     *
     * @param id
     */
    @Transactional
    public void deleteById(Integer id) {
        try {
            shiftService.deleteById(id);
        } finally {
            clearStdListCache();
        }
    }

    /**
     * 标准班次-KV下拉列表
     *
     * @return
     */
    public ItemsResult<KeyValue> getKvList() {
        return shiftService.selectShiftDefList(ShiftBelongModuleEnum.WFM.getCode());
    }

    /**
     * 标准班次-列表
     *
     * @return
     */
    public List<MultiShiftSimpleVo> getStdList() {
        CacheEntry<List<MultiShiftSimpleVo>> cacheEntry = stdListCache.get(STD_LIST_CACHE_KEY);
        if (cacheEntry != null && !cacheEntry.isExpired()) {
            List<MultiShiftSimpleVo> value = cacheEntry.getValue();
            log.info("从缓存中获取标准班次列表,条数:{}", value.size());
            return value;
        }
        cacheLock.lock();
        try {
            cacheEntry = stdListCache.get(STD_LIST_CACHE_KEY);
            if (cacheEntry != null && !cacheEntry.isExpired()) {
                return cacheEntry.getValue();
            }
            List<MultiShiftSimpleVo> stdList = shiftService.getStdList(ShiftBelongModuleEnum.WFM);
            stdListCache.put(STD_LIST_CACHE_KEY, new CacheEntry<>(stdList, System.currentTimeMillis() + CACHE_EXPIRATION_TIME));
            return stdList;
        } finally {
            cacheLock.unlock();
        }
    }

    /**
     * 清除标准班次列表缓存
     */
    public void clearStdListCache() {
        cacheLock.lock();
        try {
            stdListCache.remove(STD_LIST_CACHE_KEY);
        } finally {
            cacheLock.unlock();
        }
    }
    /**
     * 清除自定义班次列表缓存
     */
    public void clearCustShiftListCache() {
        custCacheLock.lock();
        try {
            custListCache.clear();
        } finally {
            custCacheLock.unlock();
        }
    }

    private static class CacheEntry<T> {
        private final T value;
        private final long expirationTime;

        public CacheEntry(T value, long expirationTime) {
            this.value = value;
            this.expirationTime = expirationTime;
        }

        public T getValue() {
            return value;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expirationTime;
        }
    }

    /**
     * 自定义班次-保存
     *
     * @param saveDto
     */
    @Transactional
    public Long saveCustShiftDef(CustMultiShiftDefSaveDto saveDto) {
        try {
            return waCustomizeShiftDefService.save(saveDto);
        }finally {
            clearCustShiftListCache();
        }
    }

    /**
     * 自定义班次-保存（叶片工序业务））
     *
     * @param bladeSaveDto
     */
    @Transactional
    public Long saveCustShiftDefForBlade(CustMultiShiftDefForBladeSaveDto bladeSaveDto) {
        try {
            CustMultiShiftDefSaveDto saveDto = ObjectConverter.convert(bladeSaveDto, CustMultiShiftDefSaveDto.class);
            saveDto.setBusinessProcess(ShiftBusinessProcessEnum.BLADE.getCode());
            saveDto.setBelongModule(ShiftBelongModuleEnum.WFM.getCode());
            saveDto.setBusinessType(ShiftBusinessTypeEnum.PROCESS.getCode());
            return waCustomizeShiftDefService.save(saveDto);
        }finally {
            clearCustShiftListCache();
        }
    }

    /**
     * 自定义班次-详情
     *
     * @param waShiftDefId
     * @return
     */
    public CustMultiShiftDefVo getCustShiftDef(Integer waShiftDefId) {
        return waCustomizeShiftDefService.getByShiftId(waShiftDefId);
    }

    /**
     * 自定义班次-详情（叶片工序业务）
     *
     * @param waShiftDefId
     * @return
     */
    public CustMultiShiftDefForBladeVo getCustBladeShiftDef(Integer waShiftDefId) {
        return waCustomizeShiftDefService.getBladeShiftByShiftId(waShiftDefId);
    }

    /**
     * 自定义班次-批量修改班次打卡时间范围
     *
     * @param tenantId
     * @param shiftDefIds
     * @return
     */
    public BatchUpdateShiftResult batchUpdateClockTime(String tenantId, List<Long> shiftDefIds) {
        try {
            BatchUpdateShiftResult result = new BatchUpdateShiftResult();
            List<WaCustomizeShiftDefDo> doList = waCustomizeShiftDefService.getDoList(tenantId,
                    ShiftBelongModuleEnum.WFM.getCode(), null, null);
            if (CollectionUtils.isEmpty(doList)) {
                return result;
            }
            if (CollectionUtils.isNotEmpty(shiftDefIds)) {
                doList = doList.stream().filter(it -> shiftDefIds.contains(it.getShiftDefId())).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(doList)) {
                return result;
            }
            List<Long> successList = new ArrayList<>();
            List<Long> failList = new ArrayList<>();
            for (WaCustomizeShiftDefDo customizeShiftDefDo : doList) {
                CustMultiShiftDefForBladeSaveDto bladeSaveDto = new CustMultiShiftDefForBladeSaveDto();
                bladeSaveDto.setShiftDefId(customizeShiftDefDo.getShiftDefId());
                bladeSaveDto.setShiftDefName(customizeShiftDefDo.getShiftDefName());
                bladeSaveDto.setColorMark(customizeShiftDefDo.getColorMark());
                bladeSaveDto.setBelongDate(customizeShiftDefDo.getBelongDate());
                bladeSaveDto.setBusinessProcessId(customizeShiftDefDo.getBusinessProcessId());
                bladeSaveDto.setBusinessId(customizeShiftDefDo.getBusinessId());
                List<MultiCustomizeShiftDefDto> multiWorkTimes = FastjsonUtil.toArrayList(customizeShiftDefDo.getOriMultiWorkTimes(), MultiCustomizeShiftDefDto.class);
                bladeSaveDto.setMultiWorkTimes(multiWorkTimes);
                try {
                    SpringUtil.getBean(WfmShiftService.class).saveCustShiftDefForBlade(bladeSaveDto);
                    successList.add(customizeShiftDefDo.getShiftDefId());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    failList.add(customizeShiftDefDo.getShiftDefId());
                }
            }
            result.setSuccessList(successList).setFailList(failList);
            return result;
        }finally {
            clearCustShiftListCache();
        }
    }

    /**
     * 自定义班次-导入
     *
     * @param file
     * @param userInfo
     * @return
     */
    public CustShiftImportResult importCustShift(MultipartFile file, UserInfo userInfo) {
        try {
            UserContext.doInitSecurityUserInfo(userInfo.getTenantId(),
                    null != userInfo.getUserId() ? userInfo.getUserId().toString() : null,
                    null != userInfo.getStaffId() ? userInfo.getStaffId().toString() : null,
                    null, null, null);

            return SpringUtil.getBean(WfmShiftService.class).importCustShift(file);
        } catch (Exception e) {
            log.error("async import cust shift error: {}", e.getMessage(), e);
        } finally {
            clearCustShiftListCache();
            UserContext.removeSecurityUserInfo();
        }
        return new CustShiftImportResult();
    }

    /**
     * 自定义班次-导入
     *
     * @param file 自定义班次导入文件
     */
    public CustShiftImportResult importCustShift(MultipartFile file) {
        CustShiftImportResult result = new CustShiftImportResult();

        if (file == null || file.isEmpty()) {
            result.addError(new CheckMessage(0, -1, ResponseWrap.wrapResult(AttendanceCodes.IMPORT_TEMPLATE_FILE, null).getMsg()));
            return result;
        }

        Workbook wb = null;
        try {
            DataInputStream dataInputStream = new DataInputStream(file.getInputStream());
            wb = FileUtil.createWorkbook(file.getOriginalFilename(), dataInputStream);
            if (wb == null) {
                result.addError(new CheckMessage(0, -1, "文件损坏"));
                return result;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        if (null == wb) {
            result.addError(new CheckMessage(0, -1, "文件损坏"));
            return result;
        }

        Sheet sheet = wb.getSheetAt(0);
        int firstRowNum = sheet.getFirstRowNum();
        int lastRowNum = sheet.getLastRowNum();
        if (lastRowNum <= firstRowNum) {
            result.addError(new CheckMessage(0, -1, "文件内容为空"));
            return result;
        }

        // 读取表头
        Row headerRow = sheet.getRow(firstRowNum);
        int cellCount = headerRow.getLastCellNum();
        List<String> headers = new ArrayList<>();
        Map<String, Integer> headerIndexMap = new HashMap<>();
        for (int i = 0; i < cellCount; i++) {
            Cell cell = headerRow.getCell(i);
            String cellValue = (cell != null) ? cell.getStringCellValue().trim() : "";
            headers.add(cellValue);
            if (StringUtils.isNotBlank(cellValue)) {
                headerIndexMap.put(cellValue, i);
            }
        }

        // 读取数据行
        List<String> successList = new ArrayList<>();
        List<String> failList = new ArrayList<>();
        for (int i = firstRowNum + 1; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                result.addError(new CheckMessage(i - 1, -1, "数据为空"));
                continue;
            }


            Map<String, String> dataMap = new HashMap<>();
            for (int j = 0; j < cellCount; j++) {
                Cell cell = row.getCell(j);
                String key = headers.get(j);
                if (StringUtils.isBlank(key)) {
                    continue;
                }
                String value = (cell != null) ? cell.getStringCellValue().trim() : "";
                dataMap.put(key, value);
            }

            if (dataMap.values().stream().noneMatch(StringUtils::isNotBlank)) {
                result.addError(new CheckMessage(row.getRowNum() - 1, -1, "数据为空"));
                continue;
            }

            String shiftDefName = dataMap.get("班次名称");
            if (StringUtils.isBlank(shiftDefName)) {
                result.addError(new CheckMessage(row.getRowNum() - 1, headerIndexMap.get("班次名称"), "未填写班次名称"));
                continue;
            }

            CustMultiShiftDefForBladeSaveDto bladeSaveDto = new CustMultiShiftDefForBladeSaveDto();
            bladeSaveDto.setShiftDefName(shiftDefName);
            bladeSaveDto.setColorMark("#cce6ff");

            // 工时
            List<MultiCustomizeShiftDefDto> multiWorkTimes = new ArrayList<>();

            try {
                String workTime = dataMap.get("标准工时");
                if (StringUtils.isNotBlank(workTime)) {
                    if (workTime.contains(",")) {// 多段标准工时 workTime：当日05:00-当日08:30,当日09:00-次日00:30
                        String[] workTimes = workTime.split(",");
                        for (String itemWorkTime : workTimes) {
                            MultiCustomizeShiftDefDto customizeShiftDefDto = doParseShiftTimeForImport(1, itemWorkTime.trim());
                            if (null != customizeShiftDefDto) {
                                multiWorkTimes.add(customizeShiftDefDto);
                            }
                        }
                    } else { // 一段标准工时 workTime：当日09:00-次日00:30
                        MultiCustomizeShiftDefDto customizeShiftDefDto = doParseShiftTimeForImport(1, workTime.trim());
                        if (null != customizeShiftDefDto) {
                            multiWorkTimes.add(customizeShiftDefDto);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("第{}行数据标准工时填写错误, Error:{}", row.getRowNum() + 1, e.getMessage(), e);
                result.addError(new CheckMessage(row.getRowNum() - 1, -1, "标准工时填写错误"));
                continue;
            }

            try {
                String restTime = dataMap.get("休息工时");
                if (StringUtils.isNotBlank(restTime)) {
                    if (restTime.contains(",")) {// 多段休息工时 restTime：当日09:00-当日10:30,当日16:00-当日17:30
                        String[] restTimes = restTime.split(",");
                        for (String itemRestTime : restTimes) {
                            MultiCustomizeShiftDefDto customizeShiftDefDto = doParseShiftTimeForImport(2, itemRestTime.trim());
                            if (null != customizeShiftDefDto) {
                                multiWorkTimes.add(customizeShiftDefDto);
                            }
                        }
                    } else {// 一段休息工时 restTime：当日16:00-当日17:30
                        MultiCustomizeShiftDefDto customizeShiftDefDto = doParseShiftTimeForImport(2, restTime.trim());
                        if (null != customizeShiftDefDto) {
                            multiWorkTimes.add(customizeShiftDefDto);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("第{}行数据休息工时填写错误, Error:{}", row.getRowNum() + 1, e.getMessage(), e);
                result.addError(new CheckMessage(row.getRowNum() - 1, -1, "休息工时填写错误"));
                continue;
            }

            try {
                String otTime = dataMap.get("加班工时");
                if (StringUtils.isNotBlank(otTime)) {
                    if (otTime.contains(",")) {// 多段加班工时 otTime：当日09:00-当日10:30,当日16:00-当日17:30
                        String[] otTimes = otTime.split(",");
                        for (String itemOtTime : otTimes) {
                            MultiCustomizeShiftDefDto customizeShiftDefDto = doParseShiftTimeForImport(3, itemOtTime.trim());
                            if (null != customizeShiftDefDto) {
                                multiWorkTimes.add(customizeShiftDefDto);
                            }
                        }
                    } else {// 一段加班工时 otTime：当日16:00-当日17:30
                        MultiCustomizeShiftDefDto customizeShiftDefDto = doParseShiftTimeForImport(3, otTime.trim());
                        if (null != customizeShiftDefDto) {
                            multiWorkTimes.add(customizeShiftDefDto);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("第{}行数据加班工时填写错误, Error:{}", row.getRowNum() + 1, e.getMessage(), e);
                result.addError(new CheckMessage(row.getRowNum() - 1, -1, "加班工时填写错误"));
                continue;
            }

            if (CollectionUtils.isEmpty(multiWorkTimes)) {
                failList.add(bladeSaveDto.getShiftDefName());
                result.addError(new CheckMessage(row.getRowNum() - 1, -1, "未填写工时"));
                continue;
            }
            bladeSaveDto.setMultiWorkTimes(multiWorkTimes);

            // 保存
            try {
                Long waShiftDefId = SpringUtil.getBean(WfmShiftService.class).saveCustShiftDefForBlade(bladeSaveDto);
                if (null != waShiftDefId) {
                    successList.add(bladeSaveDto.getShiftDefName());
                } else {
                    failList.add(bladeSaveDto.getShiftDefName());
                    result.addError(new CheckMessage(row.getRowNum() - 1, -1, "保存失败"));
                }
            } catch (Exception e) {
                log.error("第{}行数据保存失败:{}", row.getRowNum() + 1, e.getMessage(), e);
                failList.add(bladeSaveDto.getShiftDefName());
                if (e instanceof ServerException || e instanceof CDException) {
                    result.addError(new CheckMessage(row.getRowNum() - 1, -1, "保存失败，原因：" + e.getMessage()));
                } else {
                    result.addError(new CheckMessage(row.getRowNum() - 1, -1, "保存失败"));
                }
            }
        }
        result.setSuccessList(successList)
                .setFailList(failList);
        return result;
    }

    /**
     * 自定义班次导入时解析相关时间
     *
     * @param workType 工时类型：1 标准工时、2 休息工时 3 加班工时
     * @param workTime 数据格式：当日09:00-次日00:30
     * @return
     */
    private MultiCustomizeShiftDefDto doParseShiftTimeForImport(Integer workType, String workTime) {
        if (StringUtils.isBlank(workTime) || null == workType) {
            return null;
        }

        // 严格验证格式：日期类型HH:mm-日期类型HH:mm
        // 日期类型只能是：当日、次日
        // HH:mm 格式：小时(00-23):分钟(00-59)
        String timePattern = "^(当日|次日)([01]\\d|2[0-3]):([0-5]\\d)-(当日|次日)([01]\\d|2[0-3]):([0-5]\\d)$";
        if (!Pattern.matches(timePattern, workTime)) {
            throw new ServerException("工时格式不正确，应为'日期类型HH:mm-日期类型HH:mm'格式，日期类型只能是'当日'或'次日'，示例：当日09:00-当日18:00 或 当日20:00-次日05:00，实际输入: " + workTime);
        }

        String[] timeParts = workTime.split("-");
        if (timeParts.length != 2) {
            throw new ServerException("工时格式不正确，缺少'-'分隔符: " + workTime);
        }

        MultiCustomizeShiftDefDto shiftDefDto = new MultiCustomizeShiftDefDto();
        shiftDefDto.setWorkType(workType);

        // 开始时间：当日09:00
        String startTimePart = timeParts[0].trim();
        if (!validateTimePartFormat(startTimePart)) {
            throw new ServerException("开始时间格式不正确: " + startTimePart);
        }
        String startDateType = startTimePart.substring(0, 2);
        String startTimeStr = startTimePart.substring(2);

        if (!isValidDateType(startDateType)) {
            throw new ServerException("开始时间日期类型不正确，只能是'当日'或'次日': " + startDateType);
        }

        if (!isValidTimeFormat(startTimeStr)) {
            throw new ServerException("开始时间格式不正确，应为HH:mm格式: " + startTimeStr);
        }

        shiftDefDto.setStartTime(DateUtil.timeStrToMinute(startTimeStr));
        shiftDefDto.setStartTimeBelong(ShiftTimeBelongTypeEnum.getIndexByName(startDateType));

        // 结束时间：次日00:30
        String endTimePart = timeParts[1].trim();
        if (!validateTimePartFormat(endTimePart)) {
            throw new ServerException("结束时间格式不正确: " + endTimePart);
        }
        String endDateType = endTimePart.substring(0, 2);
        String endTimeStr = endTimePart.substring(2);

        if (!isValidDateType(endDateType)) {
            throw new ServerException("结束时间日期类型不正确，只能是'当日'或'次日': " + endDateType);
        }

        if (!isValidTimeFormat(endTimeStr)) {
            throw new ServerException("结束时间格式不正确，应为HH:mm格式: " + endTimeStr);
        }

        shiftDefDto.setEndTime(DateUtil.timeStrToMinute(endTimeStr));
        shiftDefDto.setEndTimeBelong(ShiftTimeBelongTypeEnum.getIndexByName(endDateType));
        return shiftDefDto;
    }

    /**
     * 验证时间部分格式（日期类型+时间）
     */
    private boolean validateTimePartFormat(String timePart) {
        if (StringUtils.isBlank(timePart) || timePart.length() != 7) {
            return false;
        }
        // 前两个字符必须是日期类型，后5个字符必须是HH:mm格式
        String dateType = timePart.substring(0, 2);
        String timeStr = timePart.substring(2);
        return isValidDateType(dateType) && isValidTimeFormat(timeStr);
    }

    /**
     * 验证日期类型是否有效
     */
    private boolean isValidDateType(String dateType) {
        return "当日".equals(dateType) || "次日".equals(dateType);
    }

    /**
     * 验证时间格式是否为HH:mm
     */
    private boolean isValidTimeFormat(String timeStr) {
        if (StringUtils.isBlank(timeStr) || timeStr.length() != 5 || !timeStr.contains(":")) {
            return false;
        }
        String[] parts = timeStr.split(":");
        if (parts.length != 2) {
            return false;
        }
        try {
            int hour = Integer.parseInt(parts[0]);
            int minute = Integer.parseInt(parts[1]);
            return hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 自定义班次-查询班次列表
     *
     * @param belongDate
     * @param businessProcessId
     * @param businessId
     * @return
     */
    public List<MultiShiftSimpleVo> getCustShiftDefList(Long belongDate, String businessProcessId, String businessId) {
        return waCustomizeShiftDefService.getList(CustomizeShiftDefQueryDto.doCreateForBladeProcess(belongDate, businessProcessId, businessId));
    }

    /**
     * 查询班次列表（包含标准班次和自定义班次）
     *
     * @param belongDate
     * @param businessProcessId
     * @param businessId
     * @return
     */
    public List<MultiShiftSimpleVo> getAllShiftDefList(Long belongDate, String businessProcessId, String businessId) {
        List<MultiShiftSimpleVo> dataList = new ArrayList<>();

        StopWatch st = new StopWatch();
        st.start("工时标准班次");
        log.info("wfm std shift query start time={}", System.currentTimeMillis());

        List<MultiShiftSimpleVo> standardList = getStdList();
        if (CollectionUtils.isNotEmpty(standardList)) {
            dataList.addAll(standardList);
        }

        st.stop();
        log.info("wfm std shift query end time={}", System.currentTimeMillis());

        st.start("工时自定义班次");
        log.info("wfm cust shift query start time={}", System.currentTimeMillis());

        List<MultiShiftSimpleVo> custList = getCustShiftDefList(belongDate, businessProcessId, businessId);
        if (CollectionUtils.isNotEmpty(custList)) {
            dataList.addAll(custList);
        }

        st.stop();
        log.info("wfm cust shift query end time={}", System.currentTimeMillis());

        st.start("工时班次排序");
        log.info("wfm shift sort start time={}", System.currentTimeMillis());

        dataList.sort(Comparator.comparing(MultiShiftSimpleVo::getSummary));

        st.stop();
        log.info("wfm shift sort end time={}", System.currentTimeMillis());

        log.info("WfmShiftService getAllShiftDefList time :{}", st.prettyPrint());
        return dataList;
    }

    /**
     * 分页查询班次列表（包含标准班次和自定义班次）
     *
     * @param pageQueryDto
     * @return
     */
    public AttendancePageResult<BladeShiftPageResultVo> getBladeShiftPageList(BladeShiftPageQueryDto pageQueryDto) {
        List<BladeShiftPageResultVo> items = new ArrayList<>();

        pageQueryDto.setBelongModule(ShiftBelongModuleEnum.WFM.getCode());
        pageQueryDto.setAllShift(Boolean.TRUE);
        AttendancePageResult<WaShiftDo> pageResult = shiftService.getPageList(pageQueryDto, UserContext.getAndCheckUser());
        if (null == pageResult || CollectionUtils.isEmpty(pageResult.getItems())) {
            items.add(new BladeShiftPageResultVo(Lists.newArrayList(), Lists.newArrayList()));
            return new AttendancePageResult<>(items, pageQueryDto.getPageNo(), pageQueryDto.getPageSize(), 0);
        }
        List<MultiShiftSimpleVo> voList = shiftService.convertDoToSimpleVo(pageResult.getItems());

        // 高亮显示的班次
        List<MultiShiftSimpleVo> highlightShiftVoList = new ArrayList<>();
        List<Long> highlightShiftDefId = null;
        if (CollectionUtils.isNotEmpty(highlightShiftDefId = pageQueryDto.getHighlightShiftDefId())) {
            List<Integer> waShiftDefIds = highlightShiftDefId.stream().map(it -> Integer.valueOf(String.valueOf(it))).collect(Collectors.toList());
            highlightShiftVoList = shiftService.getListByIds(waShiftDefIds);

            List<Long> finalHighlightShiftDefId = highlightShiftDefId;
            voList.forEach(it -> it.setIfHighlight(finalHighlightShiftDefId.contains(it.getShiftDefId())));
        }

        items.add(new BladeShiftPageResultVo(voList, highlightShiftVoList));
        return new AttendancePageResult<>(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    /**
     * 查询班次列表（包含标准班次和自定义班次）
     *
     * @param tenantId
     * @param startDate 查询自定义班次开始日期（年月日unix时间戳，精确到秒）
     * @param endDate   查询自定义班次结束日期（年月日unix时间戳，精确到秒）
     * @return
     */
    public List<MultiShiftSimpleVo> getAllShiftDefList(String tenantId, Long startDate, Long endDate) {
        List<MultiShiftSimpleVo> dataList = new ArrayList<>();
        // wfm 标准班次
        StopWatch st = new StopWatch();
        st.start("工时标准班次开始查询");
        List<MultiShiftSimpleVo> standardList = getStdList();
        st.stop();
        log.info("工时标准班次查询时间={},获取数据条数,{}", st.prettyPrint(), standardList.size());
        if (CollectionUtils.isNotEmpty(standardList)) {
            dataList.addAll(standardList);
        }
        // wfm 自定义班次
        StopWatch custSt = new StopWatch();
        custSt.start("工时自定义班次开始查询");
        List<MultiShiftSimpleVo> custList = getCustListWithCache(tenantId, ShiftBelongModuleEnum.WFM.getCode(), startDate, endDate);
        custSt.stop();
        log.info("工时自定义班次查询时间={},获取数据条数,{}", custSt.prettyPrint(), custList.size());
        if (CollectionUtils.isNotEmpty(custList)) {
            dataList.addAll(custList);
        }
        return dataList;
    }


    /**
     * 带缓存的获取自定义班次列表方法
     * @param tenantId 租户ID
     * @param belongModule 所属模块
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 自定义班次列表
     */
    private List<MultiShiftSimpleVo> getCustListWithCache(String tenantId, String belongModule, Long startDate, Long endDate) {
        String cacheKey = generateCustCacheKey(tenantId, belongModule, startDate, endDate);
        CacheEntry<List<MultiShiftSimpleVo>> cacheEntry = custListCache.get(cacheKey);
        if (cacheEntry != null && !cacheEntry.isExpired()) {
            List<MultiShiftSimpleVo> value = cacheEntry.getValue();
            log.info("从缓存中获取自定义班次列表,条数:{}", value.size());
            return value;
        }
        custCacheLock.lock();
        try {
            cacheEntry = custListCache.get(cacheKey);
            if (cacheEntry != null && !cacheEntry.isExpired()) {
                return cacheEntry.getValue();
            }
            List<MultiShiftSimpleVo> custList = waCustomizeShiftDefService.getList(tenantId, belongModule, startDate, endDate);
            custListCache.put(cacheKey, new CacheEntry<>(custList, System.currentTimeMillis() + CACHE_EXPIRATION_TIME));
            return custList;
        } finally {
            custCacheLock.unlock();
        }
    }

    /**
     * 生成自定义班次缓存键
     * @param tenantId 租户ID
     * @param belongModule 所属模块
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 缓存键
     */
    private String generateCustCacheKey(String tenantId, String belongModule, Long startDate, Long endDate) {
        return String.format("custListCache_%s_%s_%s_%s", tenantId, belongModule, startDate != null ? startDate : "null", endDate != null ? endDate : "null");
    }

    /**
     * 根据班次ID查询班次列表（包含标准班次和自定义班次）
     *
     * @param shiftDefIds
     * @return
     */
    public List<MultiShiftSimpleVo> getListByIds(List<Long> shiftDefIds) {
        if (CollectionUtils.isEmpty(shiftDefIds)) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201924", WebUtil.getRequest()));
        }
        log.info("WfmShiftService getListByIds shiftDefIds.count={}, time={}", shiftDefIds.size(), System.currentTimeMillis());

        List<Long> distinctShiftDefIds = shiftDefIds.stream().distinct().collect(Collectors.toList());
        log.info("WfmShiftService getListByIds distinctShiftDefIds.count={}, time={}", distinctShiftDefIds.size(), System.currentTimeMillis());
        log.debug("WfmShiftService getListByIds distinctShiftDefIds={}", FastjsonUtil.toJsonStr(distinctShiftDefIds));

        if (distinctShiftDefIds.size() > 2000) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_202805", WebUtil.getRequest()));
        }

        List<Integer> shiftDefIdList = distinctShiftDefIds.stream().map(Long::intValue).collect(Collectors.toList());
        List<List<Integer>> shiftDefIdLists = ListTool.split(shiftDefIdList, 500);

        List<MultiShiftSimpleVo> allShiftVoList = new ArrayList<>();
        for (List<Integer> waShiftDefIdList : shiftDefIdLists) {
            List<Integer> stdShiftDefIdList;// 标准班次ID

            // 自定义班次
            List<MultiShiftSimpleVo> custShiftVoList = waCustomizeShiftDefService.getListByRelShiftIds(waShiftDefIdList);
            if (CollectionUtils.isNotEmpty(custShiftVoList)) {
                List<Integer> custShiftDefIdList = custShiftVoList.stream()
                        .map(it -> it.getShiftDefId().intValue()).distinct().collect(Collectors.toList());

                stdShiftDefIdList = waShiftDefIdList.stream()
                        .filter(id -> !custShiftDefIdList.contains(id)).collect(Collectors.toList());

                allShiftVoList.addAll(custShiftVoList);
            } else {
                stdShiftDefIdList = waShiftDefIdList;
            }

            // 标准班次
            if (CollectionUtils.isNotEmpty(stdShiftDefIdList)) {
                List<MultiShiftSimpleVo> shiftVoList = shiftService.getListByIds(stdShiftDefIdList);
                allShiftVoList.addAll(shiftVoList);
            }
        }

        log.info("WfmShiftService getListByIds end allShiftVoList.count={}, time={}", allShiftVoList.size(), System.currentTimeMillis());
        return allShiftVoList;
    }

    public Boolean saveWfmShiftChange(WfmShiftChangeDto dto) {
        WfmShiftChangeDo data = ObjectConverter.convert(dto, WfmShiftChangeDo.class);
        data.setRecId(snowflakeUtil.createId());
        return wfmShiftChangeDo.save(data);
    }

    public PageResult<WfmShiftChangeVo> getEmpActualScheduleList(EmpActualSchedulePageDto dto) {
        Long startDate = dto.getStartDate();
        if (null == startDate) {
            startDate = com.caidao1.commons.utils.DateUtil.getOnlyDate();
        }
        Long endDate = dto.getEndDate();
        if (null == endDate) {
            endDate = startDate;
        }
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        String tenantId = userInfo.getTenantId();
        //获取数据权限
        String dataFilter = getDataScope(tenantId, userInfo.getUserId(), userInfo.getEmpId(), dto.getShiftGroupIds());
        List<Long> shiftGroupIds = dto.getShiftGroupIds();
        List<Long> empIds = sysEmpInfoDo.getEmpList(tenantId, startDate, endDate, null, shiftGroupIds, null, dataFilter);
        if (CollectionUtils.isNotEmpty(dto.getEmpInfos())) {
            List<Long> anyEmpIds = dto.getEmpInfos().stream().map(EmpInfoDTO::getEmpId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
            empIds = empIds.stream().filter(anyEmpIds::contains).distinct().collect(Collectors.toList());
        }
        PageBean pageBean = PageUtil.getNewPageBean(dto);
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(pageBean.getOrder()));
        String anyEmpIds = empIds.stream().map(String::valueOf).distinct().collect(Collectors.joining(","));
        PageList<WfmShiftChangeDo> pageList = wfmShiftChangeDo.getEmpActualScheduleList(tenantId, startDate * 1000, endDate * 1000, dto.getKeywords(), anyEmpIds, dto.getProcessIds(), null, pageBounds);
        if (CollectionUtils.isEmpty(pageList)) {
            return emptyPageResult(dto);
        }
        return buildFinalResult(tenantId, pageList, dto);
    }

    public String getDataScope(String tenantId, Long userId, Long empId, List<Long> shiftGroupIds) {
        return cacheCommonService.getShiftGroupDataScope(tenantId, userId, "EMP_ACTUAL_SCHEDULE_LIST", empId, shiftGroupIds);
    }

    private PageResult<WfmShiftChangeVo> buildFinalResult(String tenantId, PageList<WfmShiftChangeDo> pageList, EmpActualSchedulePageDto dto) {
        // 工序
        List<Long> processIds = pageList.stream().map(WfmShiftChangeDo::getProcessId).distinct().collect(Collectors.toList());
        Map<Long, WfmProcessInfo> processInfoMap = getWorkingProcessMap(tenantId, processIds);
        // 订单
        Map<Long, WfmOrderInfo> orderInfoMap = getOrderMap(tenantId, extractOrderIds(pageList));
        // 查询租户下所有班次
        List<MultiShiftSimpleVo> shifts = getAllShiftDefList(tenantId, null, null);
        // 班次
        Map<Integer, MultiShiftSimpleVo> shiftMap = shifts.stream().collect(Collectors.toMap(shift -> shift.getShiftDefId().intValue(), Function.identity(), (v1, v2) -> v2));
        // 员工
        Map<Long, EmpWorkInfo> empWorkInfoMap = getEmpInfoMap(tenantId, extractEmpIds(pageList));
        // 工序班组
        Map<Long, WaShiftGroup2Vo> processGroupMap = getShiftGroupMap(tenantId, processIds);
        List<WfmShiftChangeVo> list = new ArrayList<>(pageList.size());
        for (WfmShiftChangeDo shiftChangeDo : pageList) {
            WfmShiftChangeVo vo = ObjectConverter.convert(shiftChangeDo, WfmShiftChangeVo.class);
            Optional.ofNullable(processInfoMap.get(shiftChangeDo.getProcessId())).ifPresent(processInfo -> {
                vo.setProcessName(processInfo.getProcessName());
                // 处理工段
                vo.setWorkshopSectionName(Optional.ofNullable(processInfo.getWorkshopSection()).map(DictSimple::getText).orElse(""));
                // 工序类型
                vo.setWfmTypeName(Optional.ofNullable(processInfo.getWfmType()).map(DictSimple::getText).orElse(""));
            });
            Optional.ofNullable(orderInfoMap.get(shiftChangeDo.getOrderId())).ifPresent(orderInfo -> vo.setOrderNumber(orderInfo.getBladeNumber()));
            Optional.ofNullable(shiftMap.get(shiftChangeDo.getNewShiftDefId())).ifPresent(shift -> {//班次
                vo.setShiftId(shiftChangeDo.getNewShiftDefId());
                vo.setShiftName(shift.getShiftDefName());
                vo.setShiftTimes(shift.getMultiWorkTimes());
            });
            if (null != shiftChangeDo.getOldShiftDefId()) {
                Optional.ofNullable(shiftMap.get(shiftChangeDo.getOldShiftDefId())).ifPresent(shift -> {//原班次
                    vo.setOldShiftId(shiftChangeDo.getOldShiftDefId());
                    vo.setOldShiftName(shift.getShiftDefName());
                    vo.setOldShiftTimes(shift.getMultiWorkTimes());
                });
            }
            Optional.ofNullable(shiftChangeDo.getEmpIdList()).ifPresent(empIdList -> {
                List<EmpInfoDTO> employees = new ArrayList<>(empIdList.size());
                for (Long empId : empIdList) {
                    Optional.ofNullable(empWorkInfoMap.get(empId)).ifPresent(empWorkInfo -> employees.add(ObjectConverter.convert(empWorkInfo, EmpInfoDTO.class)));
                }
                vo.setEmployees(employees);
            });
            Optional.ofNullable(processGroupMap.get(shiftChangeDo.getProcessId())).ifPresent(group -> {
                vo.setShiftGroupName(group.getShiftGroupName());
            });
            vo.setStatusName(ApprovalStatusEnum.getName(shiftChangeDo.getStatus()));
            list.add(vo);
        }
        return new PageResult<>(list, dto.getPageNo(), dto.getPageSize(), pageList.getPaginator().getTotalCount());
    }

    private Map<Long, WaShiftGroup2Vo> getShiftGroupMap(String tenantId, List<Long> processIds) {
        if (null == processIds || processIds.size() == 0) {
            return Collections.emptyMap();
        }
        return convertListToMap(waShiftGroupService.getShiftGroupByLeftAssociation(processIds.stream().map(String::valueOf).collect(Collectors.toList()), tenantId));
    }

    private Map<Long, WaShiftGroup2Vo> convertListToMap(List<WaShiftGroup2Vo> list) {
        Map<Long, WaShiftGroup2Vo> resultMap = new HashMap<>();
        if (list == null) {
            return resultMap;
        }
        for (WaShiftGroup2Vo vo : list) {
            String leftAssociation = vo.getLeftAssociation();
            if (leftAssociation == null || leftAssociation.isEmpty()) {
                continue; // 跳过空值
            }
            // 拆分逗号分隔的字符串
            String[] ids = leftAssociation.split(",");
            for (String idStr : ids) {
                Long id = Long.parseLong(idStr.trim());
                resultMap.put(id, vo); // 每个ID映射到当前Vo对象
            }
        }
        return resultMap;
    }

    private List<String> extractEmpIds(List<WfmShiftChangeDo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(WfmShiftChangeDo::getEmpIds).filter(Objects::nonNull).flatMap(row -> Arrays.stream(row.split(",")))
                .map(String::trim).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
    }

    private Map<Long, EmpWorkInfo> getEmpInfoMap(String tenantId, List<String> empIds) {
        DataFilter filter = DataFilter.eq("tenantId", tenantId).andIn("empId", empIds).andNe("deleted", Boolean.TRUE.toString());
        List<EmpWorkInfo> empList = Lists.newArrayList();
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            empList = DataQuery.identifier(EMP_WORK_INFO_IDENTIFIER).decrypt().dept().specifyLanguage()
                    .queryInvisible().limit(-1, 1).exp()
                    .filter(filter, EmpWorkInfo.class, System.currentTimeMillis())
                    .getItems();
        } catch (Exception e) {
            log.error("Query {} exception:{}", EMP_WORK_INFO_IDENTIFIER, e.getMessage(), e);
        } finally {
            UserContext.removeSecurityUserInfo();
        }
        return empList.stream().collect(Collectors.toMap(emp -> Long.valueOf(emp.getEmpId()), Function.identity(), (v1, v2) -> v2));
    }

    private List<Long> extractOrderIds(List<WfmShiftChangeDo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(WfmShiftChangeDo::getOrderId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    private PageResult<WfmShiftChangeVo> emptyPageResult(EmpActualSchedulePageDto dto) {
        return new PageResult<>(Collections.emptyList(), dto.getPageNo(), dto.getPageSize(), 0);
    }

    private Map<Long, WfmOrderInfo> getOrderMap(String tenantId, List<Long> orderIds) {
        List<WfmOrderInfo> orders = getWfmOrders(tenantId, orderIds);
        return orders.stream().collect(Collectors.toMap(order -> Long.valueOf(order.getBid()), Function.identity(), (v1, v2) -> v2));
    }

    private List<WfmOrderInfo> getWfmOrders(String tenantId, List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return new ArrayList<>();
        }
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            DataFilter dataFilter = DataFilter.eq("tenantId", tenantId).andNe("deleted", Boolean.TRUE.toString());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderIds)) {
                dataFilter = dataFilter.andIn("bid", orderIds.stream().map(String::valueOf).collect(Collectors.toList()));
            }
            return DataQuery.identifier(WFM_ORDER_IDENTIFIER).decrypt().specifyLanguage().queryInvisible().limit(-1, 1).filter(dataFilter, WfmOrderInfo.class).getItems();
        } finally {
            UserContext.removeSecurityUserInfo();
        }
    }

    private Map<Long, WfmProcessInfo> getWorkingProcessMap(String tenantId, List<Long> processIds) {
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            DataFilter processFilter = DataFilter.eq("tenantId", tenantId);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(processIds)) {
                processFilter = processFilter.andIn("bid", processIds.stream().map(String::valueOf).collect(Collectors.toList()));
            }
            PageResult<WfmProcessInfo> pageResult = queryProcesses(processFilter, 1);
            List<WfmProcessInfo> workingProcessList = new ArrayList<>(pageResult.getItems());
            int totalCount = pageResult.getTotal();
            if (totalCount > MAX_PAGE_SIZE) {
                int totalPages = (totalCount + MAX_PAGE_SIZE - 1) / MAX_PAGE_SIZE;
                for (int pageNo = 2; pageNo <= totalPages; pageNo++) {
                    workingProcessList.addAll(queryProcesses(processFilter, pageNo).getItems());
                }
            }
            return workingProcessList.stream().filter(Objects::nonNull)
                    .collect(Collectors.toMap(info -> Long.parseLong(info.getBid()), Function.identity(),
                            (existing, replacement) -> existing, () -> new HashMap<>(Math.max((int) (workingProcessList.size() / 0.75f) + 1, 16))));
        } catch (Exception e) {
            log.error("Query {} failed for tenantId: {}, processIds: {}", WORK_PROCESS_IDENTIFIER, tenantId, processIds, e);
            return Collections.emptyMap();
        } finally {
            UserContext.removeSecurityUserInfo();
        }
    }

    private PageResult<WfmProcessInfo> queryProcesses(DataFilter filter, int pageNo) {
        return DataQuery.identifier(WORK_PROCESS_IDENTIFIER)
                .decrypt()
                .dept()
                .specifyLanguage()
                .queryInvisible()
                .limit(MAX_PAGE_SIZE, pageNo)
                .exp()
                .filter(filter, WfmProcessInfo.class, System.currentTimeMillis());
    }

    public Map<String, Long> getShiftChangeMap(String tenantId, Long startDate, Long endDate, List<Long> empIdList) {
        if (CollectionUtils.isEmpty(empIdList)) {
            return Collections.emptyMap();
        }
        List<WfmShiftChangeDo> list = getApprovedEmpShiftChangeList(tenantId, startDate, endDate);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<String, Long> shiftMap = new HashMap<>();
        for (WfmShiftChangeDo row : list) {
            List<Long> empIds = row.getEmpIdList();
            for (Long empId : empIds) {
                if (CollectionUtils.isNotEmpty(empIdList) && !empIdList.contains(empId)) {
                    continue;
                }
                if (null != row.getNewShiftDefId()) {
                    shiftMap.put(String.format("%s_%s_%s_%s_%s", empId, row.getWorkDate() / 1000, row.getProcessId(), row.getOrderId(), row.getOldShiftDefId()), Long.valueOf(row.getNewShiftDefId()));
                }
            }
        }
        return shiftMap;
    }

    private List<WfmShiftChangeDo> getApprovedEmpShiftChangeList(String tenantId, Long startDate, Long endDate) {
        return wfmShiftChangeDo.getEmpShiftChangeList(tenantId, startDate * 1000, endDate * 1000, Collections.singletonList(2));
    }

    public Map<String, String> getShiftChangeNewShiftIdMap(String tenantId, Long startDate, Long endDate) {
        List<WfmShiftChangeDo> list = getApprovedEmpShiftChangeList(tenantId, startDate, endDate);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<String, String> shiftMap = new HashMap<>();
        for (WfmShiftChangeDo row : list) {
            List<Long> empIds = row.getEmpIdList();
            for (Long empId : empIds) {
                if (null != row.getOldShiftDefId()) {
                    shiftMap.put(String.format("%s_%s_%s_%s", row.getWorkDate(), row.getOrderId(), row.getProcessId(), row.getOldShiftDefId()),
                            String.valueOf(row.getNewShiftDefId()));
                }
            }
        }
        return shiftMap;
    }

    public Map<String, Long> getShiftChangeOldShiftIdMap(String tenantId, Long startDate, Long endDate) {
        List<WfmShiftChangeDo> list = getApprovedEmpShiftChangeList(tenantId, startDate, endDate);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<String, Long> shiftMap = new HashMap<>();
        for (WfmShiftChangeDo row : list) {
            List<Long> empIds = row.getEmpIdList();
            for (Long empId : empIds) {
                if (null != row.getOldShiftDefId()) {
                    shiftMap.put(String.format("%s_%s_%s_%s", empId, row.getWorkDate() / 1000, row.getProcessId(), row.getOrderId()), Long.valueOf(row.getOldShiftDefId()));
                }
            }
        }
        return shiftMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateWfmShiftChange(Long workDate, Long orderId, Long processId, Integer oldShiftDefId, Integer newShiftDefId, String empIds, String tenantId) {
        List<WfmShiftChangeDo> shiftChangeDo = wfmShiftChangeDo.getShiftChangeByInfo(workDate, orderId, processId, oldShiftDefId, tenantId);
        if (CollectionUtils.isNotEmpty(shiftChangeDo)) {
            //原记录作废(正常情况下只有一条)
            for (WfmShiftChangeDo shiftChange : shiftChangeDo) {
                shiftChange.setStatus(4);
                shiftChange.setUpdateBy(SecurityUserUtil.getSecurityUserInfo().getUserId());
                shiftChange.setUpdateTime(System.currentTimeMillis());
                wfmShiftChangeDo.updateByRecId(shiftChange);
            }
        }
        //新增记录
        WfmShiftChangeDo newShiftChangeDo = new WfmShiftChangeDo();
        newShiftChangeDo.setRecId(snowflakeUtil.createId());
        newShiftChangeDo.setTenantId(tenantId);
        newShiftChangeDo.setEmpIds(empIds);
        newShiftChangeDo.setWorkDate(workDate);
        newShiftChangeDo.setOrderId(orderId);
        newShiftChangeDo.setProcessId(processId);
        newShiftChangeDo.setOldShiftDefId(oldShiftDefId);
        newShiftChangeDo.setNewShiftDefId(newShiftDefId);
        newShiftChangeDo.setStatus(2);
        newShiftChangeDo.setDeleted(0);
        newShiftChangeDo.setCreateBy(SecurityUserUtil.getSecurityUserInfo().getUserId());
        newShiftChangeDo.setCreateTime(System.currentTimeMillis());
        newShiftChangeDo.setUpdateTime(System.currentTimeMillis());
        newShiftChangeDo.setUpdateBy(SecurityUserUtil.getSecurityUserInfo().getUserId());
        wfmShiftChangeDo.save(newShiftChangeDo);
        return Result.success();
    }

    public Result<Boolean> batchInsertOrUpdateWfmShiftChange(Integer newShiftDefId, List<ProcessKey> processKeys) {
        SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        //获取工序的替换班次记录
        List<Long> schedulingTimes = processKeys.stream().map(ProcessKey::getSchedulingTime).map(Long::valueOf).distinct().collect(Collectors.toList());
        List<Long> orderIds = processKeys.stream().map(ProcessKey::getOrderId).map(Long::valueOf).distinct().collect(Collectors.toList());
        List<Long> processIds = processKeys.stream().map(ProcessKey::getProcessId).map(Long::valueOf).distinct().collect(Collectors.toList());
        List<Long> shiftIds = processKeys.stream().map(ProcessKey::getShiftId).map(Long::valueOf).distinct().collect(Collectors.toList());
        Map<String, List<WfmShiftChangeDo>> shiftChangeMap = wfmShiftChangeDo.getShiftChangeMap(schedulingTimes, orderIds, processIds, shiftIds,
                Collections.singletonList(2), SecurityUserUtil.getSecurityUserInfo().getTenantId());

        //获取工序排班员工
        Map<String, Set<String>> processEmpMap = empScheduleService.getProcessEmpMap(Long.valueOf(processKeys.get(0).getSchedulingTime()),
                Long.valueOf(processKeys.get(0).getSchedulingTime()), null, securityUserInfo.getTenantId());

        //获取待作废替换记录id、待插入替换记录
        List<Long> pendingCancellationBids = new ArrayList<>();
        List<WfmShiftChangeDo> pendingInsertDos = new ArrayList<>();

        for (ProcessKey processKey : processKeys) {
            String key = processKey.getSchedulingTime() + "_" + processKey.getOrderId() + "_" + processKey.getProcessId() + "_" + processKey.getShiftId();
            if (shiftChangeMap.containsKey(key))
                pendingCancellationBids.add(shiftChangeMap.get(key).get(0).getRecId());
            WfmShiftChangeDo newShiftChangeDo = new WfmShiftChangeDo();
            newShiftChangeDo.setRecId(snowflakeUtil.createId());
            newShiftChangeDo.setTenantId(securityUserInfo.getTenantId());
            newShiftChangeDo.setEmpIds(processEmpMap.containsKey(key) ? String.join(",", processEmpMap.get(key)) : null);
            newShiftChangeDo.setWorkDate(Long.valueOf(processKey.getSchedulingTime()));
            newShiftChangeDo.setOrderId(Long.valueOf(processKey.getOrderId()));
            newShiftChangeDo.setProcessId(Long.valueOf(processKey.getProcessId()));
            newShiftChangeDo.setOldShiftDefId(Integer.valueOf(processKey.getShiftId()));
            newShiftChangeDo.setNewShiftDefId(newShiftDefId);
            newShiftChangeDo.setStatus(2);
            newShiftChangeDo.setDeleted(0);
            newShiftChangeDo.setCreateBy(SecurityUserUtil.getSecurityUserInfo().getUserId());
            newShiftChangeDo.setCreateTime(System.currentTimeMillis());
            newShiftChangeDo.setUpdateTime(System.currentTimeMillis());
            newShiftChangeDo.setUpdateBy(SecurityUserUtil.getSecurityUserInfo().getUserId());
            pendingInsertDos.add(newShiftChangeDo);
        }

        //批量作废
        if(CollectionUtils.isNotEmpty(pendingCancellationBids))
            wfmShiftChangeDo.batchUpdateStatus(pendingCancellationBids, securityUserInfo.getUserId(), System.currentTimeMillis(), 4);
        //批量插入
        if(CollectionUtils.isNotEmpty(pendingInsertDos))
            wfmShiftChangeDo.batchInsert(pendingInsertDos);
        return Result.ok();
    }

}
