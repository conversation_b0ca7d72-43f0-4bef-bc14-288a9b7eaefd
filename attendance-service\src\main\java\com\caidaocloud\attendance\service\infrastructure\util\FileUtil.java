package com.caidaocloud.attendance.service.infrastructure.util;

import com.caidaocloud.attendance.service.application.dto.BaseHeaderDto;
import com.caidaocloud.oss.file.LocalMultipartFile;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Pattern;

@Slf4j
public class FileUtil {

    private final static String SUFFIX = "xlsx";

    private final static int MAX_COLUMN_WIDTH = 65280;

    private static final Pattern SCIENTIFIC_NOTATION_PATTERN = Pattern.compile("^[-+]?\\d+(\\.\\d+)?[eE][-+]?\\d+$");

    public static synchronized MultipartFile genExcelAndConvertToMultipartFile(List<BaseHeaderDto> heads, List<Map> dataList, String fullFileName, String fileName,
                                                                               String ext, List<String> convertToNumberExceptField) throws IOException {

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        LocalMultipartFile file = null;
        try {
            // 创建工作薄对象
            Workbook wb = SUFFIX.equals(ext) ? new SXSSFWorkbook() : new HSSFWorkbook();
            // 创建工作表对象
            Sheet sheet = wb.createSheet(fileName);
            // 生成表的表头
            int starRowIndex = 0;
            int startColIndex = 0;
            Row headRow = sheet.createRow(starRowIndex);
            for (int i = startColIndex; i < heads.size(); i++) {
                BaseHeaderDto head = heads.get(i);
                Cell cell = headRow.createCell(i);
                cell.setCellStyle(getHeaderCellStyle(wb));
                cell.setCellValue(head.getValue());
            }
            if (wb instanceof SXSSFWorkbook) {
                ((SXSSFSheet) sheet).trackAllColumnsForAutoSizing();
            }
            // 必须在单元格设值以后进行
            // 设置为根据内容自动调整列宽
            for (int i = 0; i < heads.size(); i++) {
                sheet.autoSizeColumn(i);
                int width = sheet.getColumnWidth(i) * 15 / 10;
                int columnWidth = Math.min(width, MAX_COLUMN_WIDTH);
                sheet.setColumnWidth(i, columnWidth);
            }
            DataFormat format = wb.createDataFormat();
            // 定义两种格式：整数（无小数点）和小数（自适应）
            CellStyle integerStyle = getCellStyle(wb, BorderStyle.THIN, IndexedColors.BLACK.getIndex(), (short) 0, FillPatternType.SOLID_FOREGROUND, HorizontalAlignment.CENTER, (short) 12);
            integerStyle.setDataFormat(format.getFormat("0")); // 整数格式（无小数点）
            CellStyle decimalStyle = getCellStyle(wb, BorderStyle.THIN, IndexedColors.BLACK.getIndex(), (short) 0, FillPatternType.SOLID_FOREGROUND, HorizontalAlignment.CENTER, (short) 12);
            decimalStyle.setDataFormat(format.getFormat("0.######")); // 小数格式（最多6位）
            // 创建工作表的行
            CellStyle cellStyle = getCellStyle(wb, BorderStyle.THIN, IndexedColors.BLACK.getIndex(), (short) 0, FillPatternType.SOLID_FOREGROUND, HorizontalAlignment.CENTER, (short) 12);
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                for (int j = startColIndex; j < heads.size(); j++) {
                    Map map = dataList.get(i);
                    BaseHeaderDto head = heads.get(j);
                    Cell cell = row.createCell(j);
                    cell.setCellStyle(cellStyle);
                    String cellValue = map.get(head.getId()) == null ? "" : map.get(head.getId()).toString();
                    if (null != convertToNumberExceptField && convertToNumberExceptField.contains(head.getId())) {
                        cell.setCellValue(cellValue);
                    } else {
                        if (isConvertibleToNumber(cellValue)) {
                            BigDecimal number = parseToBigDecimal(cellValue); // 用BigDecimal判断是否为整数
                            cell.setCellValue(number.doubleValue());
                            // 判断是否为整数（无小数部分）
                            if (number.compareTo(number.setScale(0, RoundingMode.DOWN)) == 0) {
                                cell.setCellStyle(integerStyle); // 整数：应用无小数点格式
                            } else {
                                cell.setCellStyle(decimalStyle); // 小数：应用自适应格式
                            }
                        } else {
                            cell.setCellValue(cellValue);
                        }
                    }
                    cellStyle.setWrapText(true);
                }
                //解决sxssworkbook设置单元格宽度的漏设
                if (i % 100 == 0 && i > 0) {
                    if (wb instanceof SXSSFWorkbook) {
                        ((SXSSFSheet) sheet).trackAllColumnsForAutoSizing();
                        // 必须在单元格设值以后进行
                        // 设置为根据内容自动调整列宽
                        for (int k = 0; k < heads.size(); k++) {
                            sheet.autoSizeColumn(k);
                            int width = sheet.getColumnWidth(k) * 15 / 10;
                            int columnWidth = Math.min(width, MAX_COLUMN_WIDTH);
                            sheet.setColumnWidth(k, columnWidth);
                        }
                    }
                }
            }
            if (wb instanceof SXSSFWorkbook) {
                ((SXSSFSheet) sheet).trackAllColumnsForAutoSizing();
            }
            // 必须在单元格设值以后进行
            // 设置为根据内容自动调整列宽
            for (int i = 0; i < heads.size(); i++) {
                sheet.autoSizeColumn(i);
                int width = sheet.getColumnWidth(i) * 15 / 10;
                int columnWidth = Math.min(width, MAX_COLUMN_WIDTH);
                sheet.setColumnWidth(i, columnWidth);
            }
            // 处理中文不能自动调整列宽的问题
            // setSizeColumn(sheet, heads.size());
            // 文档输出
            wb.write(os);
            byte[] b = os.toByteArray();
            file = new LocalMultipartFile(fullFileName, b, "application/vnd.ms-excel;charset=UTF-8");
            file.setOriginalFilename(fullFileName);
        } catch (Exception ex) {
            log.error("生成文件失败:{}", ex.getMessage(), ex);
            throw new IOException(ex.getMessage(), ex);
        } finally {
            try {
                os.flush();
                os.close();
                os = null;
            } catch (IOException e) {
                log.error("close");
            }
        }
        return file;
    }

    /**
     * 优化：判断对象是否可转换为数值（精简判断逻辑）
     */
    private static boolean isConvertibleToNumber(Object value) {
        if (value == null) return false;
        // 1. 所有Number子类（Integer、Long、BigDecimal等）直接返回true
        if (value instanceof Number) return true;
        // 2. 字符串：匹配数字格式（普通或科学计数法）
        if (value instanceof String) {
            String str = ((String) value).trim();
            return str.matches("-?\\d+(\\.\\d+)?") || SCIENTIFIC_NOTATION_PATTERN.matcher(str).matches();
        }
        return false;
    }

    /**
     * 转换为BigDecimal，用于精确判断是否为整数
     */
    private static BigDecimal parseToBigDecimal(Object value) {
        if (value == null) return BigDecimal.ZERO;

        if (value instanceof AtomicInteger) {
            return BigDecimal.valueOf(((AtomicInteger) value).get());
        }
        if (value instanceof AtomicLong) {
            return BigDecimal.valueOf(((AtomicLong) value).get());
        }
        if (value instanceof BigInteger) {
            return new BigDecimal((BigInteger) value);
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString()); // 避免double直接转换的精度问题
        }
        if (value instanceof String) {
            return new BigDecimal(((String) value).trim());
        }
        return BigDecimal.ZERO;
    }

    /**
     * 优化：转换为double（统一处理各种类型）
     */
    private static double parseToDouble(Object value) {
        if (value == null) return 0;
        // 处理原子类型（通过其get()方法获取数值）
        if (value instanceof AtomicInteger) {
            return ((AtomicInteger) value).get();
        }
        if (value instanceof AtomicLong) {
            return ((AtomicLong) value).get();
        }
        // 处理BigInteger（通过BigDecimal中转）
        if (value instanceof BigInteger) {
            return new BigDecimal((BigInteger) value).doubleValue();
        }
        // 处理所有Number类型（包括包装类、BigDecimal等）
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        // 处理字符串（包括科学计数法）
        if (value instanceof String) {
            return new BigDecimal(((String) value).trim()).doubleValue();
        }
        return 0;
    }

    public static CellStyle getHeaderCellStyle(Workbook workbook) {
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 11);
        headerFont.setFontName("微软雅黑");
        headerFont.setCharSet((byte) 1);
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        if (workbook instanceof HSSFWorkbook) {
            HSSFPalette customPalette = ((HSSFWorkbook) workbook).getCustomPalette();
            customPalette.setColorAtIndex(IndexedColors.BLUE.getIndex(), (byte) -72, (byte) -52, (byte) -28);
            headerStyle.setFillBackgroundColor(IndexedColors.BLUE.getIndex());
        } else {
            headerStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.index);
        }
        headerStyle.setFont(headerFont);
        return headerStyle;
    }

    public static CellStyle getCellStyle(Workbook workbook, BorderStyle border, short borderColor, short background, FillPatternType fillPattern, HorizontalAlignment alignment, short fontSize) {

        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderBottom(border); //边框 BorderStyle.THIN
        cellStyle.setBorderLeft(border);
        cellStyle.setBorderRight(border);
        cellStyle.setBorderTop(border);
        cellStyle.setTopBorderColor(borderColor); // 边框颜色 HSSFColor.BLACK.index
        cellStyle.setLeftBorderColor(borderColor);
        cellStyle.setRightBorderColor(borderColor);
        cellStyle.setBottomBorderColor(borderColor);
        if (background != 0) {
            cellStyle.setFillBackgroundColor(background); // 背景 IndexedColors.SKY_BLUE.getIndex()
            cellStyle.setFillPattern(fillPattern); // 填充模式 FillPatternType.SOLID_FOREGROUND
        }
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(alignment); // 居中 HorizontalAlignment.CENTER
        cellStyle.setWrapText(true); // 设置自动换行
        Font font = workbook.createFont();
        font.setFontHeightInPoints(fontSize); //设置字体大小
        cellStyle.setFont(font);
        return cellStyle;
    }

    private static void setSizeColumn(Sheet sheet, int size) {
        for (int columnNum = 0; columnNum < size; columnNum++) {
            int columnWidth = sheet.getColumnWidth(columnNum) / 256;
            for (int rowNum = 0; rowNum < sheet.getLastRowNum(); rowNum++) {
                Row currentRow;
                //当前行未被使用过
                if (sheet.getRow(rowNum) == null) {
                    currentRow = sheet.createRow(rowNum);
                } else {
                    currentRow = sheet.getRow(rowNum);
                }
                if (currentRow.getCell(columnNum) != null) {
                    Cell currentCell = currentRow.getCell(columnNum);
                    if (currentCell.getCellType() == CellType.STRING) {
                        int length = currentCell.getStringCellValue().getBytes().length;
                        if (columnWidth < length) {
                            columnWidth = length;
                        }
                    }
                }
            }
            sheet.setColumnWidth(columnNum, columnWidth * 256);
        }
    }

    /**
     * 创建文件夹路径
     */
    public static boolean createFile(String path) {
        File file = new File(path);
        // 判断文件夹路径是否存在;
        if (!file.exists()) {
            // 创建文件夹;
            return file.mkdirs();
        }
        return true;
    }

    /**
     * 删除目录下所有的文件
     */
    public static boolean deleteFiles(File file) {
        if (null == file) {
            return false;
        }
        if (file.isDirectory()) {
            String[] files = file.list();
            if (files == null) {
                return false;
            }
            for (String entry : files) {
                boolean bol = deleteFiles(new File(file, entry));
                if (bol) {
                    log.info("删除成功!");
                } else {
                    log.error("删除失败!");
                }
            }
        }
        return file.delete();
    }

    public static Workbook createWorkbook(String originFileName, DataInputStream in) throws IOException {
        if (null == originFileName || null == in) {
            return null;
        }
        Workbook workbook = null;
        ZipSecureFile.setMinInflateRatio(-1.0d);
        try {
            if (originFileName.endsWith("xls")) {
                workbook = new HSSFWorkbook(in);
            }
            if (originFileName.endsWith("xlsx")) {
                workbook = new XSSFWorkbook(in);
            }
        } catch (Exception e) {
            log.error("inputStreamToWorkBookErr:{}", e.getMessage(), e);
        } finally {
            in.close();
        }
        return workbook;
    }

    public static Workbook createWorkbook(String originFileName, InputStream in) throws IOException {
        if (null == originFileName || null == in) {
            return null;
        }
        Workbook workbook = null;
        ZipSecureFile.setMinInflateRatio(-1.0d);
        try {
            if (originFileName.endsWith("xls")) {
                workbook = new HSSFWorkbook(in);
            }
            if (originFileName.endsWith("xlsx")) {
                workbook = new XSSFWorkbook(in);
            }
        } catch (Exception e) {
            log.error("inputStreamToWorkBookErr:{}", e.getMessage(), e);
        } finally {
            in.close();
        }
        return workbook;
    }
}
