package com.caidaocloud.attendance.service.interfaces.dto.shift;

import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.commons.utils.JacksonJsonUtil;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.mobile.utils.JsonTool;
import com.caidaocloud.attendance.core.wa.dto.ShiftRestPeriods;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiOvertimeDto;
import com.caidaocloud.attendance.core.wa.dto.shift.OvertimeRestPeriodsDto;
import com.caidaocloud.attendance.core.wa.dto.shift.RestPeriodDto;
import com.caidaocloud.attendance.core.wa.dto.shift.ShiftHalfdayTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.enums.FlexibleEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftBelongModuleEnum;
import com.caidaocloud.attendance.service.interfaces.dto.FlexibleDto;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.util.PGobject;

import java.math.BigDecimal;
import java.util.*;

/**
 * 班次设置（一段班）保存DTO
 *
 * <AUTHOR>
 * @Date 2025/1/23
 */
@Data
@ApiModel("班次设置（一段班）保存DTO")
public class ShiftSaveDto {
    /******************班次基本配置信息******************/
    // 基本信息
    @ApiModelProperty("班次Id")
    private Integer shiftDefId;
    @ApiModelProperty("日期类型：1、工作日、2休息日、3法定假日、4特殊休日、5 法定休日")
    private Integer dateType;
    @ApiModelProperty("班次名称")
    private String shiftDefName;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nShiftDefName;
    @ApiModelProperty("班次编码")
    private String shiftDefCode;
    @ApiModelProperty("总工作时长")
    private Integer workTotalTime;
    @ApiModelProperty("代替班次ID, 非工作日出差等业务使用")
    private Integer substituteShift;
    @ApiModelProperty("班次工作时间跨夜标记: true 跨夜、false 不跨夜")
    private Boolean isNight = false;
    @ApiModelProperty("标识是否为临时班次")
    private boolean temporaryShift;

    // 半天定义规则
    @ApiModelProperty("半天定义类型： 1 自定义半天时间点、2 班次工作时长的一半 默认 1")
    private Integer halfdayType = 1;
    @ApiModelProperty("半天时间")
    private Integer halfdayTime;
    @ApiModelProperty("自定义半天时间点的归属标记：1 当日、2 次日")
    private Integer halfdayTimeBelong;

    // 加班时间规则
    @ApiModelProperty("加班开始时间")
    private Integer overtimeStartTime;
    @ApiModelProperty("加班结束时间")
    private Integer overtimeEndTime;
    @ApiModelProperty("加班开始时间归属标记: 1 当日、2 次日")
    private Integer overtimeStartTimeBelong;
    @ApiModelProperty("加班结束时间归属标记: 1 当日、2 次日")
    private Integer overtimeEndTimeBelong;
    @ApiModelProperty("加班休息时间段(支持多段)")
    private List<OvertimeRestPeriodsDto> overtimeRestPeriods;
    @ApiModelProperty("多段加班")
    private List<MultiOvertimeDto> multiOvertime;
    @ApiModelProperty("任意时间加班")
    private boolean noLimitOtTime;
    @ApiModelProperty("取卡时间范围，单位小时")
    private Integer clockTimeRange;

    // 补卡时间规则
    @ApiModelProperty("补卡时间限制(支持多段)")
    private List<ClockTimeLimitDto> repairRestPeriods;

    @ApiModelProperty("所属模块")
    private String belongModule = ShiftBelongModuleEnum.ATTENDANCE.getCode();

    /******************一段班字段配置信息******************/
    // 班次工作时间设置
    @ApiModelProperty("上班时间")
    private Integer startTime;
    @ApiModelProperty("下班时间")
    private Integer endTime;
    @ApiModelProperty("上班时间归属标记: 1 当日、2 次日")
    private Integer startTimeBelong;
    @ApiModelProperty("下班时间归属标记: 1 当日、2 次日")
    private Integer endTimeBelong;


    @ApiModelProperty("是否有午休时间：0否，1是")
    private Boolean isNoonRest;
    @ApiModelProperty("休息时间段(支持多段)")
    private List<RestPeriodDto> restPeriods;
    @ApiModelProperty("总休息时长")
    private Integer restTotalTime;

    // 打卡规则
    @ApiModelProperty("上班打卡开始时间")
    private Integer onDutyStartTime;
    @ApiModelProperty("上班打卡截止时间")
    private Integer onDutyEndTime;
    @ApiModelProperty("下班打卡开始时间")
    private Integer offDutyStartTime;
    @ApiModelProperty("下班打卡截止时间")
    private Integer offDutyEndTime;
    @ApiModelProperty("最早上班打卡时间归属标记: 1 当日、2 次日、3 前日")
    private Integer onDutyStartTimeBelong;
    @ApiModelProperty("上班打卡截止时间归属标记：1 当日、2 次日")
    private Integer onDutyEndTimeBelong;
    @ApiModelProperty("下班打卡开始时间归属标记：1 当日、2 次日")
    private Integer offDutyStartTimeBelong;
    @ApiModelProperty("下班打卡截止时间归属标记：1 当日、2 次日")
    private Integer offDutyEndTimeBelong;
    @ApiModelProperty("中途打卡时间段")
    private List<MidwayClockTimeDto> midwayClockTimes;

    // 弹性打卡设置
    @ApiModelProperty("弹性规则 1:晚到晚走，早到早走 2:下班晚走，第二天早到(仅一段班使用)")
    private Integer flexibleWorkRule;
    @ApiModelProperty("上班最多可晚到多少小时(仅一段班使用)")
    private BigDecimal flexibleWorkLate;
    @ApiModelProperty("下班最多可早走多少小时(仅一段班使用)")
    private BigDecimal flexibleWorkEarly;
    @ApiModelProperty("下班晚走,第二天晚到(仅一段班使用)")
    private FlexibleDto[] flexibleRules;
    @ApiModelProperty("下班晚走,第二天晚到(仅一段班使用)")
    private String flexibleOffWorkRule;
    @ApiModelProperty("弹性分析开关 1:关闭 2:开启(仅一段班使用)")
    private Integer flexibleShiftSwitch;

    public Integer doGetRealStartTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.startTimeBelong)) {
            return startTime + 1440;
        }
        return startTime;
    }

    public Integer doGetRealEndTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.endTimeBelong)) {
            return endTime + 1440;
        }
        return endTime;
    }

    public void initField() {
        this.initShiftDefName();
        this.initI18nShiftDefName();
        if (this.getFlexibleRules() != null) {
            String jsonStr = JsonTool.createJsonString(this.getFlexibleRules());
            this.setFlexibleOffWorkRule(jsonStr);
        }
    }

    public void initShiftDefName() {
        if (StringUtils.isNotBlank(this.shiftDefName)) {
            return;
        }
        if (null == this.i18nShiftDefName || this.i18nShiftDefName.isEmpty() || null == this.i18nShiftDefName.get("default")) {
            return;
        }
        this.setShiftDefName(this.i18nShiftDefName.get("default"));
    }

    public void initI18nShiftDefName() {
        if (null != this.i18nShiftDefName && !this.i18nShiftDefName.isEmpty()) {
            return;
        }
        if (StringUtils.isBlank(this.shiftDefName)) {
            return;
        }
        Map<String, String> i18nName = new HashMap<>();
        i18nName.put("default", this.shiftDefName);
        this.setI18nShiftDefName(i18nName);
    }

    public void initOvertime() {
        if (null != this.getOvertimeStartTime() && null != this.getOvertimeEndTime()) {
            return;
        }
        if (CollectionUtils.isEmpty(this.getMultiOvertime())) {
            return;
        }
        List<MultiOvertimeDto> multiOvertime = this.getMultiOvertime();
        multiOvertime.sort(Comparator.comparing(MultiOvertimeDto::getOvertimeStartTime));
        MultiOvertimeDto firstOvertime = multiOvertime.get(0);

        this.setOvertimeStartTime(firstOvertime.getOvertimeStartTime());
        this.setOvertimeEndTime(firstOvertime.getOvertimeEndTime());
        this.setOvertimeStartTimeBelong(firstOvertime.getOvertimeStartTimeBelong());
        this.setOvertimeEndTimeBelong(firstOvertime.getOvertimeEndTimeBelong());
    }

    public void doInitOnDutyTime() {
        // TODO 前一日
        if (ShiftTimeBelongTypeEnum.PRE_DAY.getIndex().equals(this.getOnDutyStartTimeBelong())) {
            Integer onDutyStartTime = this.getOnDutyStartTime() - 1440;
            this.setOnDutyStartTime(onDutyStartTime);
            this.setOnDutyStartTimeBelong(ShiftTimeBelongTypeEnum.TODAY.getIndex());
        }
    }

    public void preCheckBase() {
        if (null == this.getDateType()) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.DATE_TYPE_IS_NULL, Boolean.FALSE).getMsg());
        }
        if (!StringUtil.isNotBlank(this.getShiftDefName())) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_NAME_IS_NULL, Boolean.FALSE).getMsg());
        }
        if (this.getShiftDefName().length() > 100) {
            throw new ServerException(Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_NAME_IS_TOO_LONG, null).getMsg(), 100)).getMsg());
        }
        if (!StringUtil.isNotBlank(this.getShiftDefCode())) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_CODE_IS_NULL, Boolean.FALSE).getMsg());
        }
        if (this.getShiftDefCode().length() > 100) {
            throw new ServerException(Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_CODE_IS_TOO_LONG, null).getMsg(), 100)).getMsg());
        }
        if (!((this.getOvertimeStartTime() == null && this.getOvertimeEndTime() == null)
                || (this.getOvertimeStartTime() != null && this.getOvertimeEndTime() != null))) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_PERIOD_NOT_SET, Boolean.FALSE).getMsg());
        }
        if (CollectionUtils.isNotEmpty(this.getRepairRestPeriods())) {
            if (this.getRepairRestPeriods().stream().anyMatch(t -> t.getStartTime() == null || t.getEndTime() == null)) {
                throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.TIME_RULE_ERROR, Boolean.FALSE).getMsg());
            }
        }
        this.checkMultiOvertimeSelf();
    }

    public void preCheck() {
        this.preCheckBase();
        if (FlexibleEnum.OPEN.getIndex().equals(this.getFlexibleShiftSwitch())
                && this.getStartTime() != null && this.getEndTime() != null && this.getStartTime() > this.getEndTime()) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.CROSS_NIGHT_SHIFT_NOT_FLEXIBILITY, Boolean.FALSE).getMsg());
        }
        if (this.checkRestTime()) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_REST_EXCEEDS_COMMUTING, Boolean.FALSE).getMsg());
        }
        if (CollectionUtils.isNotEmpty(this.getMidwayClockTimes())) {
            if (this.getMidwayClockTimes().stream().anyMatch(t -> t.getStartTime() == null || t.getEndTime() == null)) {
                throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.CLOCK_TIME_RULE_ERROR, Boolean.FALSE).getMsg());
            }
        }
    }

    /**
     * 保存班次时，检查班次休息时间段超出上下班时间
     *
     * @return true 异常 false 正常
     */
    public boolean checkRestTime() {
        List<RestPeriodDto> restTimes = this.getRestPeriods();
        if (CollectionUtils.isEmpty(restTimes)) {
            return false;
        }
        boolean isky;
        if (null != this.getStartTimeBelong() && null != this.getEndTimeBelong()) {
            isky = ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(this.getStartTimeBelong())
                    && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.getEndTimeBelong());
        } else {
            isky = CdWaShiftUtil.checkCrossNight(this.getStartTime(), this.getEndTime(), this.getDateType());
        }
        Integer startTime = this.getStartTime();
        Integer endTime = this.getEndTime();
        if (isky) {
            endTime = endTime + 24 * 60;
        }
        for (RestPeriodDto r : restTimes) {
            Integer noonRestStart = r.getNoonRestStart();
            Integer noonRestEnd = r.getNoonRestEnd();
            if (!isky) {
                if (noonRestStart < startTime || noonRestEnd > endTime) {
                    return true;
                }
            } else {
                ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStart, noonRestEnd, startTime, this.getEndTime(), this.getDateType());
                noonRestStart = restPeriod.getNoonRestStart();
                noonRestEnd = restPeriod.getNoonRestEnd();
                if (noonRestEnd < startTime || noonRestStart > endTime || noonRestStart < startTime || noonRestEnd > endTime) {
                    return true;
                }
            }
        }
        return false;
    }

    public WaShiftDef doConvert() {
        WaShiftDef shiftDef = ObjectConverter.convert(this, WaShiftDef.class);
        shiftDef.setBelongOrgid(UserContext.getTenantId());
        if (null != this.getI18nShiftDefName()) {
            shiftDef.setI18nShiftDefName(FastjsonUtil.toJson(this.getI18nShiftDefName()));
        }
        this.doSetIsNight(shiftDef);
        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDef.getDateType())) {
            shiftDef.setIsHalfdayTime(null != shiftDef.getHalfdayTime()
                    || ShiftHalfdayTypeEnum.HALF_OF_SHIFT.getIndex().equals(shiftDef.getHalfdayType()));
        } else {
            shiftDef.setStartTime(0);
            shiftDef.setEndTime(0);
            shiftDef.setWorkTotalTime(0);
        }
        // 补卡限制
        if (CollectionUtils.isNotEmpty(this.getRepairRestPeriods())) {
            shiftDef.setClockTimeLimit(JSONUtils.ObjectToJson(this.getRepairRestPeriods()));
        }
        // 任意时间加班
        if (this.noLimitOtTime) {
            shiftDef.setClockAnalysisRule(FastjsonUtil.toJson(ShiftClockAnalysisRuleDto.doBuildByOt(this.clockTimeRange)));
        } else {
            shiftDef.setClockAnalysisRule(FastjsonUtil.toJson(ShiftClockAnalysisRuleDto.doBuildDefault()));
        }
        // 多段加班时间
        if (CollectionUtils.isNotEmpty(this.getMultiOvertime())) {
            shiftDef.setMultiOvertime(FastjsonUtil.toJson(this.getMultiOvertime()));
        }
        // 多段加班休息时间
        try {
            this.doSetOvertimeRestJsonInfo(shiftDef);
        } catch (Exception e) {
            if (e instanceof ServerException) {
                throw new ServerException(e.getMessage());
            }
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_90014", WebUtil.getRequest()));
        }
        // 中途打卡时间设置（仅一段班使用，历史字段）
        if (CollectionUtils.isNotEmpty(this.getMidwayClockTimes())) {
            shiftDef.setMidwayClockTime(FastjsonUtil.toJson(this.getMidwayClockTimes()));
        }
        // 休息时间设置（仅一段班使用）
        if (shiftDef.getIsNoonRest() == null) {
            shiftDef.setIsNoonRest(false);
        }
        List<RestPeriodDto> restList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(this.getRestPeriods())) {
            shiftDef.setRestPeriods(FastjsonUtil.toJson(this.getRestPeriods()));
            restList = FastjsonUtil.toArrayList(FastjsonUtil.toJson(shiftDef.getRestPeriods()), RestPeriodDto.class);
            restList.sort(Comparator.comparing(RestPeriodDto::doGetRealNoonRestStart));
        }
        // 计算休息时长
        this.doSetRestTime(shiftDef, restList);
        this.doSetRestInfo(shiftDef, restList);
        // 计算工作时长
        this.doSetWorkTime(shiftDef);
        return shiftDef;
    }

    public void doSetOvertimeRestJsonInfo(WaShiftDef shiftDef) throws Exception {
        if (null == this.getOvertimeRestPeriods()) {
            return;
        }
        List<OvertimeRestPeriodsDto> otRestPeriods = this.getOvertimeRestPeriods();
        if (CollectionUtils.isEmpty(otRestPeriods)) {
            PGobject pGobject = new PGobject();
            pGobject.setType("jsonb");
            pGobject.setValue("[]");
            shiftDef.setOvertimeRestPeriods(pGobject);
            return;
        }
        for (OvertimeRestPeriodsDto period : otRestPeriods) {
            Integer start = period.getOvertimeRestStartTime();
            Integer end = period.getOvertimeRestEndTime();
            if (end < start && end != 0) {
                throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_OVERTIME_ACROSS_NIGHT, Boolean.FALSE).getMsg());
            }
        }
        PGobject pGobject = new PGobject();
        pGobject.setType("jsonb");
        pGobject.setValue(JacksonJsonUtil.beanToJson(otRestPeriods));
        shiftDef.setOvertimeRestPeriods(pGobject);
    }

    public void doSetRestInfo(WaShiftDef shiftDef, List<RestPeriodDto> restList) {
        try {
            if (CollectionUtils.isNotEmpty(restList)) {
                Optional<RestPeriodDto> optional = restList.stream().findFirst();
                if (optional.isPresent()) {
                    RestPeriodDto first = optional.get();
                    shiftDef.setNoonRestStart(first.getNoonRestStart());
                    shiftDef.setNoonRestEnd(first.getNoonRestEnd());
                    shiftDef.setNoonRestStartBelong(first.getNoonRestStartBelong());
                    shiftDef.setNoonRestEndBelong(first.getNoonRestEndBelong());
                }
                restList.remove(0);
                PGobject pGobject = new PGobject();
                pGobject.setType("jsonb");
                pGobject.setValue(JacksonJsonUtil.beanToJson(restList));
                shiftDef.setRestPeriods(pGobject);
            } else {
                PGobject pGobject = new PGobject();
                pGobject.setType("jsonb");
                pGobject.setValue("[]");
                shiftDef.setRestPeriods(pGobject);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_90014", WebUtil.getRequest()));
        }
    }

    /**
     * 计算休息时长
     *
     * @param shiftDef
     * @param restList
     */
    public void doSetRestTime(WaShiftDef shiftDef, List<RestPeriodDto> restList) {
        Integer iStart = shiftDef.getStartTime();
        Integer iEnd = shiftDef.getEndTime();
        Integer iEndOriginal = shiftDef.getEndTime();

        int restTotalTime = 0;
        shiftDef.setRestTotalTime(restTotalTime);
        if (iStart == null || iEnd == null || !DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDef.getDateType())) {
            return;
        }
        if (shiftDef.getIsNoonRest() == null || !shiftDef.getIsNoonRest() || CollectionUtils.isEmpty(restList)) {
            return;
        }

        if (CdWaShiftUtil.checkCrossNight(iStart, iEnd, shiftDef.getDateType())) {
            iEnd += 1440;
        }

        for (RestPeriodDto period : restList) {
            Integer begin = period.getNoonRestStart();
            Integer end = period.getNoonRestEnd();
            if (begin == null || end == null) {
                continue;
            }
            if (CdWaShiftUtil.checkCrossNight(begin, end, shiftDef.getDateType())) {
                end += 1440;
            } else if (!CdWaShiftUtil.checkCrossNight(begin, end, shiftDef.getDateType())) {
                if (begin < iStart && begin <= iEndOriginal) {
                    end += 1440;
                    begin += 1440;
                }
            }
            if (!(end <= iStart || iEnd <= begin)) {
                restTotalTime += Math.min(end, iEnd) - Math.max(begin, iStart);
            }
        }
        shiftDef.setRestTotalTime(restTotalTime);
    }

    /**
     * 计算工作时长
     *
     * @param shiftDef
     */
    public void doSetWorkTime(WaShiftDef shiftDef) {
        int workTime = 0;
        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDef.getDateType()) &&
                shiftDef.getEndTime() != null && shiftDef.getStartTime() != null) {
            Integer wStart = shiftDef.getStartTime();
            Integer wEnd = shiftDef.getEndTime();
            if (CdWaShiftUtil.checkCrossNight(wStart, wEnd, shiftDef.getDateType())) {
                wEnd += 1440;
            }
            Integer restTime = Optional.ofNullable(shiftDef.getRestTotalTime()).orElse(0);
            workTime = wEnd - wStart - restTime;
        }
        shiftDef.setWorkTotalTime(workTime);
    }

    public void doSetIsNight(WaShiftDef shiftDef) {
        if (CdWaShiftUtil.checkCrossNightV2(shiftDef, shiftDef.getDateType())) {
            shiftDef.setIsNight(Boolean.TRUE);
        } else {
            shiftDef.setIsNight(Boolean.FALSE);
        }
    }

    public void checkMultiOvertimeSelf() {
        if (CollectionUtils.isEmpty(this.getMultiOvertime())) {
            return;
        }
        List<MultiOvertimeDto> multiOvertimeDtoList = this.getMultiOvertime();
        multiOvertimeDtoList.sort(Comparator.comparing(MultiOvertimeDto::doGetRealOvertimeStartTime));
        if (multiOvertimeDtoList.size() > 1) {
            for (int i = 0; i < multiOvertimeDtoList.size(); i++) {
                for (int j = i + 1; j < multiOvertimeDtoList.size(); j++) {
                    MultiOvertimeDto current = multiOvertimeDtoList.get(i);
                    MultiOvertimeDto next = multiOvertimeDtoList.get(j);
                    if (current.doGetRealOvertimeEndTime() > next.doGetRealOvertimeStartTime()
                            && current.doGetRealOvertimeStartTime() < next.doGetRealOvertimeEndTime()) {
                        throw new ServerException(MessageHandler.getMessage("caidao.exception.error_202828", WebUtil.getRequest()));
                    }
                }
            }
        }
    }
}
