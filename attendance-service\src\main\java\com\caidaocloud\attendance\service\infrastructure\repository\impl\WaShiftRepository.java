package com.caidaocloud.attendance.service.infrastructure.repository.impl;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaShiftDefMapper;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.mybatis.model.WaShiftDefExample;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.enums.ShiftBelongModuleEnum;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.domain.repository.IWaShiftRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaShiftPo;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ShiftPageDto;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.mongodb.BasicDBObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Repository
public class WaShiftRepository implements IWaShiftRepository {

    private static final String collectionName = "wa_shift_";

    /*@Autowired
    private MongodbDao<WaShiftPo> mongodbDao;*/

    @Resource
    private WaShiftDefMapper waShiftDefMapper;

    private String getCollectionName(String tenantId) {
        return collectionName + tenantId;
    }

    @Override
    public void save(WaShiftPo waShiftPo, String tenantId) {
        //mongodbDao.save(waShiftPo, this.getCollectionName(tenantId));
    }

    @Override
    public void saveList(List<WaShiftPo> list, String tenantId) {
        //mongodbDao.save(list, this.getCollectionName(tenantId));
    }

    @Override
    public WaShiftPo selectWaShiftPoByDate(String belongOrgid, Long empId, Long workDate, String tenantId) {
        BasicDBObject query = new BasicDBObject();
        query.put("b", belongOrgid);
        query.put("e", empId);
        query.put("w", workDate);
        // workDate 升序查询
        List<WaShiftPo> entities = null;//mongodbDao.query(query, this.getCollectionName(tenantId), WaShiftPo.class);
        return this.getRecord(entities);
    }

    @Override
    public AttendancePageResult<WaShiftDo> getShiftDefList(ShiftPageDto shiftPageDto) {
        PageBean pageBean = PageUtil.getPageBean(shiftPageDto);
        String filter = pageBean.getFilter();
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "shift_def_id.desc" : pageBean.getOrder();
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        PageList<WaShiftDef> pageList;
        if (shiftPageDto.isAllShift()) {
            pageList = waShiftDefMapper.selectAllPageList(myPageBounds, shiftPageDto.getBelongOrgId(),
                    shiftPageDto.getKeywords(), shiftPageDto.getDateType(), filter, shiftPageDto.getBelongModule());
        } else {
            pageList = waShiftDefMapper.selectPageList(myPageBounds, shiftPageDto.getBelongOrgId(),
                    shiftPageDto.getKeywords(), shiftPageDto.getDateType(), filter, shiftPageDto.getBelongModule(),
                    shiftPageDto.isTemporaryShift());
        }
        List<WaShiftDo> shiftDos = ObjectConverter.convertList(pageList, WaShiftDo.class);
        return new AttendancePageResult<>(shiftDos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
    }

    @Override
    public List<WaShiftDo> selectListByIds(List<Integer> shiftDefIds) {
        WaShiftDefExample example = new WaShiftDefExample();
        example.createCriteria().andBelongOrgidEqualTo(UserContext.getTenantId())
                .andShiftDefIdIn(shiftDefIds);
        List<WaShiftDef> defList = waShiftDefMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(defList)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(defList, WaShiftDo.class);
    }

    private WaShiftPo getRecord(List<WaShiftPo> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return null;
        }
        return entities.get(0);
    }

    @Override
    public void deleteShift(String belongOrgid, String tenantId) {
        BasicDBObject query = new BasicDBObject();
        query.put("b", belongOrgid);
        //mongodbDao.delete(query, this.getCollectionName(tenantId));
    }

    @Override
    public WaShiftPo getShiftById(Integer shiftDefId) {
        WaShiftDef waShiftDef = waShiftDefMapper.selectByPrimaryKey(shiftDefId);
        if (waShiftDef != null) {
            return ObjectConverter.convert(waShiftDef, WaShiftPo.class);
        }
        return null;
    }

    @Override
    public int saveOrUpdateWaShiftDef(WaShiftDef waShiftDef) {
        if (waShiftDef.getShiftDefId() == null) {
            return waShiftDefMapper.insertSelective(waShiftDef);
        } else {
            //return waShiftDefMapper.updateByPrimaryKeySelective(waShiftDef);
            return waShiftDefMapper.updateByPrimaryKey(waShiftDef);
        }
    }

    @Override
    public List<WaShiftDo> getWaShiftDefList(String belongOrgId, ShiftBelongModuleEnum belongModule) {
        WaShiftDefExample example = new WaShiftDefExample();
        example.createCriteria().andBelongOrgidEqualTo(belongOrgId)
                .andBelongModuleEqualTo(null == belongModule ? ShiftBelongModuleEnum.ATTENDANCE.getCode() : belongModule.getCode())
                .andTemporaryShiftEqualTo(Boolean.FALSE);
        List<WaShiftDef> defList = waShiftDefMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(defList)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(defList, WaShiftDo.class);
    }

    @Override
    public boolean checkShiftDefReCode(String tenantId, String defCode, Integer shiftDefId, String belongModule) {
        WaShiftDefExample example = new WaShiftDefExample();
        WaShiftDefExample.Criteria criteria = example.createCriteria();
        criteria.andBelongOrgidEqualTo(tenantId);
        criteria.andShiftDefCodeEqualTo(defCode);
        if (shiftDefId != null) {
            criteria.andShiftDefIdNotEqualTo(shiftDefId);
        }
        if (StringUtils.isNotBlank(belongModule)) {
            criteria.andBelongModuleEqualTo(belongModule);
        }
        criteria.andTemporaryShiftEqualTo(Boolean.FALSE);
        List list = waShiftDefMapper.selectByExample(example);
        return list != null && list.size() > 0;
    }

    @Override
    public boolean checkShiftDefReName(String tenantId, String defName, Integer shiftDefId, String belongModule) {
        WaShiftDefExample example = new WaShiftDefExample();
        WaShiftDefExample.Criteria criteria = example.createCriteria();
        if (shiftDefId != null) {
            criteria.andShiftDefIdNotEqualTo(shiftDefId);
        }
        criteria.andBelongOrgidEqualTo(tenantId);
        criteria.andShiftDefNameEqualTo(defName);
        if (StringUtils.isNotBlank(belongModule)) {
            criteria.andBelongModuleEqualTo(belongModule);
        }
        criteria.andTemporaryShiftEqualTo(Boolean.FALSE);
        List list = waShiftDefMapper.selectByExample(example);
        return list != null && list.size() > 0;
    }

    @Override
    public List<WaShiftDo> getWaShiftDefList(String tenantId, List<Integer> shiftIds) {
        WaShiftDefExample example = new WaShiftDefExample();
        WaShiftDefExample.Criteria criteria = example.createCriteria();
        criteria.andBelongOrgidEqualTo(tenantId);
        if (shiftIds != null && shiftIds.size() > 0) {
            criteria.andShiftDefIdIn(shiftIds);
        }
        List<WaShiftDef> list = waShiftDefMapper.selectByExample(example);
        return ObjectConverter.convertList(list, WaShiftDo.class);
    }

    @Override
    public List<WaShiftDo> selectListByStTime(String tenantId, ShiftBelongModuleEnum belongModule,
                                              Boolean temporaryShift, Integer startTime, Integer endTime,
                                              Integer startTimeBelong, Integer endTimeBelong) {
        WaShiftDefExample example = new WaShiftDefExample();
        WaShiftDefExample.Criteria criteria = example.createCriteria();
        criteria.andBelongOrgidEqualTo(tenantId)
                .andBelongModuleEqualTo(null == belongModule ? ShiftBelongModuleEnum.ATTENDANCE.getCode() : belongModule.getCode())
                .andTemporaryShiftEqualTo(Optional.ofNullable(temporaryShift).orElse(Boolean.FALSE));
        if (null != startTime && null != endTime && null != startTimeBelong && null != endTimeBelong) {
            criteria.andStartTimeEqualTo(startTime)
                    .andEndTimeEqualTo(endTime)
                    .andStartTimeBelongEqualTo(startTimeBelong).
                    andEndTimeBelongEqualTo(endTimeBelong);
        }
        List<WaShiftDef> defList = waShiftDefMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(defList)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(defList, WaShiftDo.class);
    }
}
