package com.caidaocloud.attendance.service.wfm.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class WorkingHourMonthAnalyzeDto implements Serializable {
    @ApiModelProperty("员工主键")
    private Long empId;
    @ApiModelProperty("工号")
    private String workno;
    @ApiModelProperty("姓名")
    private String empName;
    private Long productId;
    @ApiModelProperty("产品")
    private String productName;
    private Long processId;
    @ApiModelProperty("工序编码")
    private String processCode;
    @ApiModelProperty("计薪工序")
    private String processName;
    @ApiModelProperty("排班工时(不包括法定假日排班工时)")
    private BigDecimal workTime;
    @ApiModelProperty("法定假日排班工时")
    private BigDecimal holidayWorkTime;
    @ApiModelProperty("实际工时")
    private BigDecimal actualWorkTime;
    @ApiModelProperty("法定假日实际工时")
    private BigDecimal holidayActualWorkTime;
    @ApiModelProperty("完成数量")
    private Integer completedQuantity;
    @ApiModelProperty("完成订单")
    private List<WfmOrderInfo> completedOrders;
    @ApiModelProperty("实际入库数量")
    private BigDecimal inventoryQuantity;
    @ApiModelProperty("入库订单")
    private List<WfmOrderInfo> inventoryOrders;
    @ApiModelProperty("异常工时")
    private BigDecimal abnormalWorkTime;
    @ApiModelProperty("异常工时详情")
    private List<AbnormalDto> abnormalWorkTimes;

    @ApiModelProperty("有效工时")
    private BigDecimal effectWorkTime;
    @ApiModelProperty("法定假日有效工时")
    private BigDecimal holidayEffectWorkTime;
    @ApiModelProperty("工序工时")
    private BigDecimal processWorkTime;
    @ApiModelProperty("实际完成数量")
    private BigDecimal actualCompletedQuantity;

    @ApiModelProperty("工作日实际工时（单位：小时）")
    private BigDecimal workDayActualWorkTime;

    @ApiModelProperty("工作日加班时长")
    private BigDecimal workDayOtDuration;
    @ApiModelProperty("休息日加班时长")
    private BigDecimal restDayOtDuration;
}
