<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaPlanEmpRelMapper">
    <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaPlanEmpRelPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="corp_id" jdbcType="BIGINT" property="corpId"/>
        <result column="belong_org_id" jdbcType="VARCHAR" property="belongOrgId"/>
        <result column="plan_id" jdbcType="BIGINT" property="planId"/>
        <result column="emp_id" jdbcType="BIGINT" property="empId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>

        <result column="start_time" jdbcType="BIGINT" property="startTime"/>
        <result column="end_time" jdbcType="BIGINT" property="endTime"/>
    </resultMap>
    <sql id="Base_Column_List">
      id, corp_id, belong_org_id, plan_id, emp_id, creator, create_time, updater, update_time, start_time, end_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wa_plan_emp_rel
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
      delete from wa_plan_emp_rel
      where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaPlanEmpRelPo">
      insert into wa_plan_emp_rel (id, corp_id, belong_org_id,
      plan_id, emp_id, creator, create_time, updater, update_time, start_time, end_time)
      values (#{id,jdbcType=BIGINT}, #{corpId,jdbcType=BIGINT}, #{belongOrgId,jdbcType=VARCHAR},
      #{planId,jdbcType=BIGINT}, #{empId,jdbcType=BIGINT}, #{creator,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{updater,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{startTime,jdbcType=BIGINT}, #{endTime,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective"
            parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaPlanEmpRelPo">
        insert into wa_plan_emp_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="corpId != null">
                corp_id,
            </if>
            <if test="belongOrgId != null">
                belong_org_id,
            </if>
            <if test="planId != null">
                plan_id,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="corpId != null">
                #{corpId,jdbcType=BIGINT},
            </if>
            <if test="belongOrgId != null">
                #{belongOrgId,jdbcType=VARCHAR},
            </if>
            <if test="planId != null">
                #{planId,jdbcType=BIGINT},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=BIGINT},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaPlanEmpRelPo">
        update wa_plan_emp_rel
        <set>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=BIGINT},
            </if>
            <if test="belongOrgId != null">
                belong_org_id = #{belongOrgId,jdbcType=VARCHAR},
            </if>
            <if test="planId != null">
                plan_id = #{planId,jdbcType=BIGINT},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=BIGINT},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaPlanEmpRelPo">
      update wa_plan_emp_rel
      set corp_id = #{corpId,jdbcType=BIGINT},
      belong_org_id = #{belongOrgId,jdbcType=VARCHAR},
      plan_id = #{planId,jdbcType=BIGINT},
      emp_id = #{empId,jdbcType=BIGINT},
      creator = #{creator,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      updater = #{updater,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      start_time = #{endTime,jdbcType=BIGINT},
      end_time = #{endTime,jdbcType=BIGINT}
      where id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteByPlanId">
      delete from wa_plan_emp_rel
      where plan_id = #{planId} and belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
    </delete>

    <select id="queryByPlanId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from wa_plan_emp_rel
        where plan_id = #{planId,jdbcType=BIGINT}
    </select>

    <insert id="saveBatch" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaPlanEmpRelPo">
        <foreach collection="records" item="record" index="index" separator=";">
            insert into wa_plan_emp_rel (id, corp_id, belong_org_id, plan_id, emp_id, creator, create_time, updater, update_time, start_time, end_time)
            values(#{record.id,jdbcType=BIGINT}, #{record.corpId,jdbcType=BIGINT},
            #{record.belongOrgId,jdbcType=VARCHAR},#{record.planId,jdbcType=BIGINT},
            #{record.empId,jdbcType=BIGINT}, #{record.creator,jdbcType=BIGINT},
            #{record.createTime,jdbcType=BIGINT}, #{record.updater,jdbcType=BIGINT},
            #{record.updateTime,jdbcType=BIGINT}, #{record.startTime,jdbcType=BIGINT},
            #{record.endTime,jdbcType=BIGINT})
        </foreach>
    </insert>

    <resultMap id="KeyValueMap" type="com.caidaocloud.dto.EmpInfoKeyValue">
        <result column="plan_id" jdbcType="BIGINT" property="planId"/>
        <result column="emp_id" jdbcType="INTEGER" property="value"/>
        <result column="emp_name" jdbcType="VARCHAR" property="text"/>
        <result column="workno" jdbcType="VARCHAR" property="workNo"/>
    </resultMap>
    <select id="queryEmployeesByPlanId" resultMap="KeyValueMap">
      select p.plan_id, p.emp_id, e.emp_name, e.workno
      from wa_plan_emp_rel p
      left join sys_emp_info e on p.emp_id = e.empid and e.deleted = 0
      where p.plan_id = #{planId,jdbcType=BIGINT} and p.belong_org_id = #{belongOrgId,jdbcType=VARCHAR} and e.deleted = 0
    </select>

    <select id="queryEmployeesByPlanIds" resultMap="KeyValueMap">
        select p.plan_id, p.emp_id, e.emp_name, e.workno
        from wa_plan_emp_rel p
        left join sys_emp_info e on p.emp_id = e.empid
        where p.belong_org_id = #{belongOrgId,jdbcType=VARCHAR} and e.deleted = 0
        <if test="planIds!=null and planIds.size()>0">
            and plan_id in
            <foreach collection="planIds" item="planId" open="(" close=")" separator=",">
                #{planId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <update id="updateRelPlanIdByParams">
        update wa_plan_emp_rel
        set plan_id = #{planId},
        creator = #{userId,jdbcType=INTEGER},
        create_time = floor(extract(epoch from now()))*1000,
        updater = #{userId,jdbcType=INTEGER},
        update_time = floor(extract(epoch from now()))*1000
        where corp_id = #{corpId,jdbcType=BIGINT} and belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        <if test="empIds != null and empIds.size()>0">
            and emp_id in
            <foreach collection="empIds" item="empId" open="(" close=")" separator=",">
                #{empId,jdbcType=BIGINT}
            </foreach>
        </if>
    </update>

    <resultMap id="PlanRelMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpClockPlanRel">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="plan_id" jdbcType="BIGINT" property="planId"/>
        <result column="plan_name" jdbcType="VARCHAR" property="planName"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="emp_name" jdbcType="VARCHAR" property="empName"/>
        <result column="workno" jdbcType="VARCHAR" property="workno"/>
        <result column="start_time" jdbcType="BIGINT" property="startTime"/>
        <result column="end_time" jdbcType="BIGINT" property="endTime"/>

        <result column="orgName" jdbcType="VARCHAR" property="orgName"/>
        <result column="fullPath" jdbcType="VARCHAR" property="fullPath"/>
        <result column="updater" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>

        <result column="stats" jdbcType="INTEGER" property="empStatus"/>
        <result column="employ_type" jdbcType="BIGINT" property="empStyle"/>
        <result column="hire_date" jdbcType="BIGINT" property="hireDate"/>
        <result column="termination_date" jdbcType="BIGINT" property="terminationDate"/>

        <result column="i18n_plan_name" jdbcType="VARCHAR" property="i18nPlanName"/>
    </resultMap>
    <select id="queryPlanEmpRelById" resultMap="PlanRelMap">
        select wper.id,wper.plan_id,wcp.plan_name,wper.emp_id,sei.emp_name,sei.workno,wper.start_time,wper.end_time
        from wa_plan_emp_rel wper
        left join sys_emp_info sei on wper.emp_id = sei.empid
        left join wa_clock_plan wcp on wcp.id = wper.plan_id
        where wper.id = #{id} and wper.belong_org_id = #{belongOrgId,jdbcType=VARCHAR} and sei.deleted = 0
    </select>

    <select id="queryEmpClockPlanByPeriod" resultMap="PlanRelMap">
        select * from wa_plan_emp_rel
        where belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        <if test="empId != null">
            and emp_id = #{empId}
        </if>
        <if test="startTime != null and endTime != null">
            and ((#{startTime} between start_time and end_time) or (#{endTime} between start_time and end_time)
            or (start_time <![CDATA[>=]]> #{startTime} and end_time <![CDATA[<=]]> #{endTime}))
        </if>
        <if test="id != null">
            and id <![CDATA[<>]]> #{id}
        </if>
    </select>

    <select id="queryEmpClockPlanList" resultMap="PlanRelMap">
        SELECT
        wper.id,
        wper.plan_id,
        wcp.plan_name,
        wcp.i18n_plan_name,
        wper.emp_id,
        ei.emp_name,
        ei.workno,
        wper.start_time,
        wper.end_time,
        co.shortname AS "orgName",
        case
        when co.full_path is not null and co.full_path != ''
        then concat_ws('/', co.full_path, co.shortname)
        else co.shortname
        end  AS "fullPath",
        wper.update_time,
        case
            when sei.workno is not null and sei.workno!=''
                then concat(sei.workno, '(', sei.emp_name, ')')
            when sui.empname is not null and sui.empname != ''
                then concat(sui.account, '(', sui.empname, ')')
            else ''
            end AS "updater",
        ei.stats,
        ei.employ_type,
        ei.hire_date,
        ei.termination_date
        FROM wa_plan_emp_rel wper
        JOIN wa_clock_plan wcp ON wcp.id = wper.plan_id
        JOIN sys_emp_info ei ON wper.emp_id = ei.empid
        LEFT JOIN sys_corp_org co ON ei.orgid = co.orgid AND co.deleted = 0
        LEFT JOIN sys_user_info sui ON sui.userid = wper.updater
        LEFT JOIN sys_emp_info sei ON sei.empid=sui.empid
        <where>
            ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR} AND ei.deleted = 0
            <if test="planId != null">
                AND es.plan_id = #{planId}
            </if>
            <if test="filter != null and filter != ''">
                ${filter}
            </if>
            <if test="keywords != null and keywords != ''">
                AND (ei.workno in ${keywords} OR ei.emp_name in ${keywords})
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'NotEffective'">
                and wper.start_time <![CDATA[>]]> #{nowTime}
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'InEffect'">
                and #{nowTime} <![CDATA[>=]]> wper.start_time and #{nowTime} <![CDATA[<=]]> wper.end_time
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'Expired'">
                and wper.end_time <![CDATA[<]]> #{nowTime}
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'NotEffective+InEffect'">
                and wper.end_time <![CDATA[>=]]> #{nowTime}
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'NotEffective+Expired'">
                and (wper.start_time <![CDATA[>]]> #{nowTime} or wper.end_time <![CDATA[<]]> #{nowTime})
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'InEffect+Expired'">
                and wper.start_time <![CDATA[<=]]> #{nowTime}
            </if>
        </where>
        ORDER BY wper.update_time DESC
    </select>

    <select id="queryPlanEmpRelByEmpIdAndPeriod" resultMap="PlanRelMap">
        select * from wa_plan_emp_rel
        where belong_org_id = #{tenantId,jdbcType=VARCHAR}
        <if test="empId != null">
            and emp_id = #{empId}
        </if>
        <if test="time != null">
            and #{time} between start_time and end_time
        </if>
    </select>

    <delete id="deleteBatch">
        delete from wa_plan_emp_rel
        where belong_org_id = #{tenantId,jdbcType=VARCHAR}
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>

    <select id="queryEmpPlanByIds" resultMap="BaseResultMap">
        select * from wa_plan_emp_rel
        where belong_org_id = #{tenantId,jdbcType=VARCHAR}
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>