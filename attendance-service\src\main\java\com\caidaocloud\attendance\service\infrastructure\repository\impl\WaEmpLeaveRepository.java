package com.caidaocloud.attendance.service.infrastructure.repository.impl;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaLeaveDaytimeMapper;
import com.caidao1.wa.mybatis.model.WaLeaveDaytime;
import com.caidao1.wa.mybatis.model.WaLeaveDaytimeExample;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.dto.EmpLeaveInfo;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveDo;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveDaytimeDo;
import com.caidaocloud.attendance.service.domain.repository.IWaEmpLeaveRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpLeaveMapper;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.imports.service.Infrastructure.util.PageUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.github.miemiedev.mybatis.paginator.domain.Paginator;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
public class WaEmpLeaveRepository implements IWaEmpLeaveRepository {
    @Autowired
    private EmpLeaveMapper empLeaveMapper;
    @Autowired
    private WaLeaveDaytimeMapper waLeaveDaytimeMapper;

    @Override
    public PageResult<WaEmpLeaveDo> getEmpLeavePageList(BasePage basePage, String belongOrgId,
                                                        Long empId, Integer wfFuncId, Integer status, String keywords) {
        PageBean pageBean = PageUtil.getPageBean(basePage);
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        PageList<WaEmpLeaveDo> empLeaveList = empLeaveMapper.getEmpLeaveList(myPageBounds, belongOrgId, empId, status, wfFuncId, keywords);
        if (CollectionUtils.isNotEmpty(empLeaveList)) {
            List<WaEmpLeaveDo> list = ObjectConverter.convertList(empLeaveList, WaEmpLeaveDo.class);
            Paginator paginator = empLeaveList.getPaginator();
            return new PageResult<>(list, basePage.getPageNo(), basePage.getPageSize(), paginator.getTotalCount());
        }
        return new PageResult<>(new ArrayList<>(), basePage.getPageNo(), basePage.getPageSize(), 0);
    }

    @Override
    public List<WaLeaveDaytimeDo> getEmpLeaveDaytimeList(Long empid, Long daytime, List<Integer> statusList) {
        return empLeaveMapper.getEmpLeaveDaytimeList(empid, daytime, statusList);
    }

    @Override
    public List<WaLeaveDaytimeDo> getEmpLeaveDay(Long empid, Long startDate, Long endDate, List<Integer> statusList) {
        return empLeaveMapper.getEmpLeaveDay(empid, startDate, endDate, statusList);
    }

    @Override
    public List<WaLeaveDaytimeDo> getLeaveDayTimeByDay(Integer leaveId, Long date) {
        WaLeaveDaytimeExample example = new WaLeaveDaytimeExample();
        WaLeaveDaytimeExample.Criteria criteria = example.createCriteria();
        criteria.andLeaveIdEqualTo(leaveId);
        if (Optional.ofNullable(date).isPresent()) {
            criteria.andLeaveDateEqualTo(date);
        }
        List<WaLeaveDaytime> list = waLeaveDaytimeMapper.selectByExample(example);
        return ObjectConverter.convertList(list, WaLeaveDaytimeDo.class);
    }

    @Override
    public int updateDayTime(WaLeaveDaytimeDo waLeaveDaytimeDo) {
        WaLeaveDaytime dayTime = ObjectConverter.convert(waLeaveDaytimeDo, WaLeaveDaytime.class);
        return waLeaveDaytimeMapper.updateByPrimaryKeySelective(dayTime);
    }

    @Override
    public PageResult<WaLeaveDaytimeDo> selectDayTimePage(AttendanceBasePage basePage, Long startTime, Long endTime,
                                                          List<Integer> statusList) {
        PageBean pageBean = com.caidaocloud.attendance.service.infrastructure.util.PageUtil.getNewPageBean(basePage);
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        PageList<WaLeaveDaytimeDo> pageList = empLeaveMapper.selectDayTimePageList(myPageBounds, UserContext.getTenantId(),
                startTime, endTime, statusList);
        if (CollectionUtils.isEmpty(pageList)) {
            return new PageResult<>(new ArrayList<>(), basePage.getPageNo(), basePage.getPageSize(), 0);
        }
        List<WaLeaveDaytimeDo> items = ObjectConverter.convertList(pageList, WaLeaveDaytimeDo.class);
        Paginator paginator = pageList.getPaginator();
        return new PageResult<>(items, basePage.getPageNo(), basePage.getPageSize(), paginator.getTotalCount());
    }

    @Override
    public List<EmpLeaveInfo> selectEmpLeaveDayTimeList(String tenantId, List<Long> empIdList, Long startDate, Long endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("belongid", tenantId);
        params.put("approvalStatusList", Lists.newArrayList(ApprovalStatusEnum.PASSED.getIndex(),
                ApprovalStatusEnum.IN_APPROVAL.getIndex()));
        params.put("empIdList", empIdList);
        params.put("startDate", startDate);
        params.put("endDate", endDate + 86399);
        params.put("onlyValid", Boolean.TRUE);
        List<Map> mapList = empLeaveMapper.selectEmpLeaveDayTimeList(params);
        if (CollectionUtils.isEmpty(mapList)) {
            return Lists.newArrayList();
        }
        return FastjsonUtil.convertList(mapList, EmpLeaveInfo.class);
    }
}
