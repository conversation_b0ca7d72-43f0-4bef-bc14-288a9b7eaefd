package com.caidaocloud.attendance.service.wfm.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.commons.utils.SpringUtils;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.vo.MultiShiftSimpleVo;
import com.caidaocloud.attendance.core.wa.vo.MultiWorkTimeInfoSimpleVo;
import com.caidaocloud.attendance.sdk.dto.tenant.TenantDto;
import com.caidaocloud.attendance.sdk.feign.TenantFeignClient;
import com.caidaocloud.attendance.service.application.service.ICacheCommonService;
import com.caidaocloud.attendance.service.application.service.IQuotaService;
import com.caidaocloud.attendance.service.infrastructure.feign.WfmFeignClient;
import com.caidaocloud.attendance.service.infrastructure.feign.wfm.WfmKeyValuePair;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.schedule.application.service.dto.*;
import com.caidaocloud.attendance.service.schedule.application.service.schedule.IEmpScheduleService;
import com.caidaocloud.attendance.service.schedule.domain.entity.WaShiftGroupDo;
import com.caidaocloud.attendance.service.wfm.application.dto.*;
import com.caidaocloud.attendance.service.wfm.application.enums.OrgDataScopeEnum;
import com.caidaocloud.attendance.service.wfm.application.service.IWorkingHourAnalyzeService;
import com.caidaocloud.attendance.service.wfm.application.service.OrgDataScopeService;
import com.caidaocloud.attendance.service.wfm.application.service.WfmHolidayService;
import com.caidaocloud.attendance.service.wfm.application.service.WfmShiftService;
import com.caidaocloud.attendance.service.wfm.domain.entity.WorkingHourAnalyzeDo;
import com.caidaocloud.attendance.service.wfm.interfaces.dto.WfmDayAnalysePageDto;
import com.caidaocloud.attendance.service.wfm.interfaces.dto.WfmMonthAnalysePageDto;
import com.caidaocloud.attendance.service.wfm.interfaces.dto.WorkingHourDayAnalyseDto;
import com.caidaocloud.attendance.service.wfm.interfaces.dto.WorkingHourOrderDto;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.DeleteStatusEnum;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Slf4j
@Service
public class WorkingHourAnalyzeServiceImpl implements IWorkingHourAnalyzeService {
    @Resource
    private IEmpScheduleService empScheduleService;
    @Resource
    private WorkingHourAnalyzeDo workingHourAnalyzeDo;
    @Resource
    private WfmHolidayService wfmHolidayService;
    @Resource
    private WfmFeignClient wfmFeignClient;
    @Resource
    private TenantFeignClient tenantFeignClient;
    @Resource
    private CacheService cacheService;
    @Resource
    private ICacheCommonService cacheCommonService;
    @Resource
    private OrgDataScopeService orgDataScopeService;
    @Resource
    private WfmShiftService wfmShiftService;
    @Value("${caidaocloud.data.wfm.workingHour.restDeduction:false}")
    private boolean wfmWorkingHourRestDeduction;

    private final static String WFM_ORDER_IDENTIFIER = "entity.wfm.OrderManagement";
    private final static String WFM_ABNORMAL_WORKING_HOUR_IDENTIFIER = "entity.wfm.AbnormalWorkHours";
    private final static String WFM_EMP_ABNORMAL_WORKING_HOUR_IDENTIFIER = "entity.wfm.EmpAbnormalWorkHours";
    private final static String WFM_PROCESS_EFFECTIVE_TIME_IDENTIFIER = "entity.wfm.EffectiveTime";
    private final static String WFM_REGISTER_RECORD_IDENTIFIER = "entity.wfm.ScanWorkHours";
    private final static String EMP_WORK_INFO_IDENTIFIER = "entity.hr.EmpWorkInfo";
    private final static String WORK_PROCESS_IDENTIFIER = "entity.wfm.ProcessManagement";
    private final static String WFM_CONFIRM_PROCESS_COMPLETION = "entity.wfm.ConfirmProcessCompletion";
    private final static String WFM_AVERAGE_WORKING_HOUR_IDENTIFIER = "entity.wfm.newAverageAttendanceTime";
    private static final String WFM_WORKING_HOUR_ANALYZE = "WFM_WORKING_HOUR_ANALYZE";
    private static final String WFM_WORKING_HOUR_DAY_DATA_SCOPE_IDENTIFIER = "WFM_WORKING_HOUR_DAY";
    private static final String WFM_WORKING_HOUR_MONTH_DATA_SCOPE_IDENTIFIER = "WFM_WORKING_HOUR_MONTH";
    private final SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);
    private static final int MAX_PAGE_SIZE = 5000;
    private static final int DEFAULT_PAGE_NO = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;
    private static final String PIECEWORK_CODE = "1";
    private static final String STANDARD_WORK_HOUR_CODE = "0";
    private static final BigDecimal STANDARD_DAILY_WORK_MINUTES = BigDecimal.valueOf(8 * 60); // 8小时（分钟）
    private static final ZoneId DEFAULT_ZONE = ZoneId.systemDefault();
    private static final int BATCH_DELETE_SIZE = 500;
    private static final int BATCH_SAVE_SIZE = 400;
    private final ForkJoinPool analyzeThreadPool = new ForkJoinPool(5, ForkJoinPool.defaultForkJoinWorkerThreadFactory, (t, e) -> log.error("工时分析线程池异常: {}", e.getMessage(), e), true);

    @Override
    public void autoAnalyzeWorkingHour() {
        List<String> tenantIds = getTenants();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return;
        }
        for (String tenantId : tenantIds) {
            String lockKey = String.format("%s_%s", WFM_WORKING_HOUR_ANALYZE, tenantId);
            if (cacheService.containsKey(lockKey)) {
                log.info("当前租户有其他任务正在执行，tenantId：{}", tenantId);
                continue;
            }
            cacheService.cacheValue(lockKey, "0.5", 600);
            WorkingHourDayAnalyseDto dto = new WorkingHourDayAnalyseDto();
            Long currentDate = DateUtil.getOnlyDate();
            dto.setStartDate(DateUtil.addDate(currentDate * 1000, -1));
            dto.setEndDate(currentDate);
            UserInfo userInfo = new UserInfo();
            userInfo.setTenantId(tenantId);
            userInfo.setUserId(0L);
            try {
                analyzeWorkingHour(dto, userInfo);
            } catch (Exception e) {
                log.error("autoAnalyzeWorkingHour tenant:{} exception:{},", tenantId, e.getMessage(), e);
            } finally {
                cacheService.remove(lockKey);
            }
        }
    }

    private List<String> getTenants() {
        try {
            Result<List<TenantDto>> result = tenantFeignClient.tenantList();
            if (null == result || 0 != result.getCode() || !result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
                return Lists.newArrayList();
            }
            List<TenantDto> tenants = result.getData();
            return tenants.stream().map(TenantDto::getTenantId).distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询租户失败：{}", e.getMessage(), e);
        }
        return Lists.newArrayList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void analyzeWorkingHour(WorkingHourDayAnalyseDto dto, UserInfo userInfo) {
        String tenantId = userInfo.getTenantId();
        Long startDate = Optional.ofNullable(dto.getStartDate()).orElse(DateUtil.getOnlyDate());
        Long endDate = Optional.ofNullable(dto.getEndDate()).orElse(DateUtil.getOnlyDate());
        if (startDate > endDate) {
            endDate = startDate;
        }
        //是否有指定的计算工时对象
        List<Long> empIds = dto.getEmpIds();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("查询员工排班开始");
        //查询员工排班
        List<EmpMultiShiftInfoDto> empShifts = empScheduleService.getEmpWfmShiftInfos(tenantId, startDate, endDate, empIds, dto.getOrderIds(), true);
        stopWatch.stop();
        int empShiftNum = empShifts.size();
        stopWatch.start("查询员工排班开始去重");
        empShifts = empShifts.stream().filter(shift -> shift.getLeafNumberId() != null && shift.getProcessId() != null && shift.getShiftDefId() != null).collect(Collectors.toList());
        empShifts = distinctByKey(empShifts, empShift -> String.format("%s_%s_%s_%s_%s_%s", empShift.getEmpId(), empShift.getWorkDate(),
                empShift.getLeafNumberId(), empShift.getProductId(), empShift.getProcessId(), empShift.getShiftDefId()));
        stopWatch.stop();
        if (empShiftNum > empShifts.size()) {
            log.info("analyzeWorkingHour emp shift repeat tenantId : {}, startDate:{}, endDate:{}", tenantId, startDate, endDate);
        }
        stopWatch.start("查询特殊日期，班次变更记录");
        WfmWorkingHourAnalyzeInfo analyzeInfo = getAnalyzeInfo(tenantId, startDate, endDate);
        stopWatch.stop();
        //设置订单等信息
        stopWatch.start("查询异常工时，订单，报工工时");
        setWorkingHourAnalyzeInfo(tenantId, startDate, endDate, empShifts, analyzeInfo);
        stopWatch.stop();
        List<WorkingHourAnalyzeDo> result = new ArrayList<>();
        log.info("start analyzeWorkingHour tenantId : {}, startDate:{}, endDate:{}", tenantId, startDate, endDate);
        stopWatch.start("工时分析");
        for (EmpMultiShiftInfoDto empShift : empShifts) {
            try {
                WorkingHourAnalyzeDo analyze = getWorkingHourAnalyze(empShift, analyzeInfo, userInfo);
                result.add(analyze);
            } catch (Exception e) {
                log.error("analyzeWorkingHour exception:{}, emp:{}", e.getMessage(), JSON.toJSONString(empShift), e);
            }
        }
        stopWatch.stop();
        stopWatch.start("工时本月入库分析");
        result.addAll(calInventoryOrder(tenantId, startDate, endDate, result, userInfo));
        stopWatch.stop();
        if (CollectionUtils.isNotEmpty(result)) {
            stopWatch.start("汇总分析多天完成一个叶片情况");
            summaryWorkingHourCompletedQuantity(result, analyzeInfo);
            stopWatch.stop();
            List<Long> deleteEmpIds = result.stream().map(WorkingHourAnalyzeDo::getEmpId).distinct().collect(Collectors.toList());
            List<Long> deleteOrderIds = dto.getOrderIds();
            List<List<Long>> deleteEmpIdBatches = ListTool.split(deleteEmpIds, 500);
            int totalDeleteBatches = deleteEmpIdBatches.size();
            int deletedCount = 0;
            String orderIdsParam = null;
            if (CollectionUtils.isNotEmpty(deleteOrderIds)) {
                orderIdsParam = "'{" + StringUtils.join(deleteOrderIds, ",") + "}'";
            }
            stopWatch.start("删除老数据");
            for (int i = 0; i < totalDeleteBatches; i++) {
                List<Long> batch = deleteEmpIdBatches.get(i);
                if (CollectionUtils.isEmpty(batch)) {
                    continue;
                }
                String empIdsParam = "'{" + StringUtils.join(batch, ",") + "}'";
                int deletedRows = workingHourAnalyzeDo.batchDelete(tenantId, startDate, endDate, empIdsParam, orderIdsParam);
                deletedCount += deletedRows;
                log.info("analyzeWorkingHour - Deleted batch {}/{}: {} records", i + 1, totalDeleteBatches, deletedRows);
            }
            stopWatch.stop();
            log.info("analyzeWorkingHour - Total deleted records: {}", deletedCount);
            List<List<WorkingHourAnalyzeDo>> addBatches = ListTool.split(result, 400);
            int totalAddBatches = addBatches.size();
            int savedCount = 0;
            stopWatch.start("插入新数据");
            for (int i = 0; i < totalAddBatches; i++) {
                List<WorkingHourAnalyzeDo> batch = addBatches.get(i);
                if (CollectionUtils.isEmpty(batch)) {
                    continue;
                }
                workingHourAnalyzeDo.batchSave(tenantId, batch);
                savedCount += batch.size();
                log.info("analyzeWorkingHour - Saved batch {}/{}: {} records", i + 1, totalAddBatches, batch.size());
            }
            stopWatch.stop();
            log.info("analyzeWorkingHour - Total saved records: {}/{}", savedCount, result.size());
            stopWatch.start("工时分析，生成调休配额");
            genEmpWorkingHourCompensatoryQuota(tenantId, userInfo.getUserId(), startDate, endDate, result);
            stopWatch.stop();
        }
        log.info("analyzeWorkingHour finished tenantId : {}, ", tenantId);
        log.error("analyzeWorkingHour end,{}", stopWatch.prettyPrint());
    }

    public void analyzeWorkingHour2(WorkingHourDayAnalyseDto dto, UserInfo userInfo) {
        String tenantId = userInfo.getTenantId();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("整体流程开始");

        try {
            // 1. 参数预处理与基础校验
            Pair<Long, Long> dateRange = preprocessDateRange(dto);
            Long startDate = dateRange.getLeft();
            Long endDate = dateRange.getRight();
            List<Long> empIds = Optional.ofNullable(dto.getEmpIds()).orElse(Collections.emptyList());
            List<Long> orderIds = Optional.ofNullable(dto.getOrderIds()).orElse(Collections.emptyList());

            // 2. 查询并处理排班数据（去重、过滤无效数据）
            stopWatch.start("查询并处理排班数据");
            List<EmpMultiShiftInfoDto> empShifts = queryAndDeduplicateShifts(tenantId, startDate, endDate, empIds, orderIds);
            if (CollectionUtils.isEmpty(empShifts)) {
                log.info("无有效排班数据，终止分析流程");
                return;
            }
            stopWatch.stop();

            // 3. 工时分析计算
            // 准备分析所需的基础信息（含setWorkingHourAnalyzeInfo关键步骤）
            WfmWorkingHourAnalyzeInfo analyzeInfo = getAnalyzeInfo(tenantId, startDate, endDate);
            setWorkingHourAnalyzeInfo(tenantId, startDate, endDate, empShifts, analyzeInfo);
            stopWatch.start("工时分析计算（并行）");
            List<WorkingHourAnalyzeDo> result = analyzeWorkingHourData(empShifts, tenantId, userInfo, startDate, endDate, analyzeInfo);
            stopWatch.stop();

            // 4. 数据持久化与后续处理
            if (CollectionUtils.isNotEmpty(result)) {
                stopWatch.start("数据汇总与持久化");
                processDataPersistenceAndFollowUp(tenantId, startDate, endDate, result, orderIds, userInfo, analyzeInfo);
                stopWatch.stop();
            }
            log.info("工时分析流程完成，数据量：{}条，耗时统计：{}", empShifts.size(), stopWatch.prettyPrint());
        } catch (Exception e) {
            log.error("工时分析整体流程异常", e);
            throw new CDException("工时分析失败：" + e.getMessage());
        }
    }

    /**
     * 预处理日期范围（确保startDate <= endDate）
     */
    private Pair<Long, Long> preprocessDateRange(WorkingHourDayAnalyseDto dto) {
        Long startDate = Optional.ofNullable(dto.getStartDate()).orElse(DateUtil.getOnlyDate());
        Long endDate = Optional.ofNullable(dto.getEndDate()).orElse(DateUtil.getOnlyDate());
        if (startDate > endDate) {
            log.warn("开始日期大于结束日期，自动修正为同一日期：startDate={}, endDate={}", startDate, endDate);
            endDate = startDate;
        }
        return Pair.of(startDate, endDate);
    }

    /**
     * 查询排班数据并去重
     */
    private List<EmpMultiShiftInfoDto> queryAndDeduplicateShifts(String tenantId, Long startDate, Long endDate, List<Long> empIds, List<Long> orderIds) {
        // 1. 查询原始排班数据
        List<EmpMultiShiftInfoDto> empShifts = empScheduleService.getEmpWfmShiftInfos(tenantId, startDate, endDate, empIds, orderIds, true);
        int originalSize = empShifts.size();
        if (CollectionUtils.isEmpty(empShifts)) {
            return Collections.emptyList();
        }

        // 2. 过滤无效数据（关键字段非空校验）
        List<EmpMultiShiftInfoDto> validShifts = empShifts.stream().filter(shift -> shift.getLeafNumberId() != null && shift.getProcessId() != null&& shift.getShiftDefId() != null).collect(Collectors.toList());

        // 3. 高效去重（基于自定义唯一键+HashSet）
        Set<ShiftUniqueKey> uniqueKeys = new HashSet<>(validShifts.size());
        List<EmpMultiShiftInfoDto> deduplicatedShifts = new ArrayList<>(validShifts.size());
        for (EmpMultiShiftInfoDto shift : validShifts) {
            ShiftUniqueKey key = new ShiftUniqueKey(shift);
            if (uniqueKeys.add(key)) {
                deduplicatedShifts.add(shift);
            }
        }

        // 4. 日志记录去重情况
        if (originalSize > deduplicatedShifts.size()) {
            log.info("排班数据去重完成，原始条数：{}，有效去重后：{}，去重条数：{}", originalSize, deduplicatedShifts.size(), originalSize - deduplicatedShifts.size());
        }
        return deduplicatedShifts;
    }

    /**
     * 工时分析计算（并行处理，确保关键信息不丢失）
     */
    private List<WorkingHourAnalyzeDo> analyzeWorkingHourData(List<EmpMultiShiftInfoDto> empShifts, String tenantId, UserInfo userInfo, Long startDate, Long endDate, WfmWorkingHourAnalyzeInfo analyzeInfo) throws Exception {
        // 1. 使用自定义线程池执行并行分析（避免公共线程池竞争）
        Future<List<WorkingHourAnalyzeDo>> future = analyzeThreadPool.submit(() ->
                // 自定义分片策略，优化负载均衡
                StreamSupport.stream(new ShiftSpliterator(empShifts.spliterator()), true)
                        .map(shift -> {
                            try {
                                // 调用核心分析方法，传递完整的analyzeInfo
                                return getWorkingHourAnalyze(shift, analyzeInfo, userInfo);
                            } catch (Exception e) {
                                log.error("单条排班数据处理异常，shift={}", JSON.toJSONString(shift), e);
                                return null; // 跳过异常数据，不影响整体流程
                            }
                        }).filter(Objects::nonNull).collect(Collectors.toList())
        );

        // 2. 等待并行任务完成，获取基础分析结果
        List<WorkingHourAnalyzeDo> baseResult = future.get();

        // 3. 补充库存订单数据（独立处理，避免并行干扰）
        List<WorkingHourAnalyzeDo> inventoryOrders = calInventoryOrder(tenantId, startDate, endDate, baseResult, userInfo);
        baseResult.addAll(inventoryOrders);

        return baseResult;
    }

    /**
     * 数据持久化与后续处理
     */
    @Transactional(rollbackFor = Exception.class)
    public void processDataPersistenceAndFollowUp(String tenantId, Long startDate, Long endDate, List<WorkingHourAnalyzeDo> result,
                                                  List<Long> orderIds, UserInfo userInfo, WfmWorkingHourAnalyzeInfo analyzeInfo) {
        // 1. 汇总多天完成量（非持久化步骤，前置处理）
        summaryWorkingHourCompletedQuantity(result, analyzeInfo);
        // 2. 批量删除旧数据（参数化查询，避免SQL注入）
        List<Long> deleteEmpIds = result.stream().map(WorkingHourAnalyzeDo::getEmpId).distinct().collect(Collectors.toList());
        int totalDeleted = batchDeleteOldData(tenantId, startDate, endDate, deleteEmpIds, orderIds);
        log.info("旧数据删除完成，总删除条数：{}", totalDeleted);

        // 3. 批量保存新数据（分批处理，避免大数据量导致的内存溢出）
        int totalSaved = batchSaveNewData(tenantId, result);
        log.info("新数据保存完成，总保存条数：{}", totalSaved);
        // 4. 生成调休配额（非事务核心步骤，但依赖持久化结果）
        genEmpWorkingHourCompensatoryQuota(tenantId, userInfo.getUserId(), startDate, endDate, result);
    }

    /**
     * 批量删除旧数据
     */
    private int batchDeleteOldData(String tenantId, Long startDate, Long endDate, List<Long> deleteEmpIds, List<Long> orderIds) {
        if (CollectionUtils.isEmpty(deleteEmpIds)) {
            return 0;
        }
        String orderIdsParam = null;
        if (CollectionUtils.isNotEmpty(orderIds)) {
            orderIdsParam = "'{" + StringUtils.join(orderIds, ",") + "}'";
        }
        // 分批删除
        List<List<Long>> empIdBatches = ListTool.split(deleteEmpIds, BATCH_DELETE_SIZE);
        int totalDeleted = 0;
        for (List<Long> batch : empIdBatches) {
            if (CollectionUtils.isEmpty(batch)) {
                continue;
            }
            String empIdsParam = "'{" + StringUtils.join(batch, ",") + "}'";
            totalDeleted += workingHourAnalyzeDo.batchDelete(tenantId, startDate, endDate, empIdsParam, orderIdsParam);
        }
        return totalDeleted;
    }

    /**
     * 批量保存新数据（分批处理，优化性能）
     */
    private int batchSaveNewData(String tenantId, List<WorkingHourAnalyzeDo> result) {
        if (CollectionUtils.isEmpty(result)) {
            return 0;
        }
        // 分批保存（每批400条，适配数据库批量插入性能）
        List<List<WorkingHourAnalyzeDo>> saveBatches = ListTool.split(result, BATCH_SAVE_SIZE);
        int totalSaved = 0;
        for (List<WorkingHourAnalyzeDo> batch : saveBatches) {
            workingHourAnalyzeDo.batchSave(tenantId, batch);
            totalSaved += batch.size();
        }
        return totalSaved;
    }

    /**
     * 排班数据唯一键（用于高效去重）
     */
    private static class ShiftUniqueKey {
        private final Long empId;
        private final Long workDate;
        private final Long leafNumberId;
        private final Long productId;
        private final Long processId;
        private final Long shiftDefId;

        public ShiftUniqueKey(EmpMultiShiftInfoDto shift) {
            this.empId = shift.getEmpId();
            this.workDate = shift.getWorkDate();
            this.leafNumberId = shift.getLeafNumberId();
            this.productId = shift.getProductId();
            this.processId = shift.getProcessId();
            this.shiftDefId = shift.getShiftDefId();
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ShiftUniqueKey that = (ShiftUniqueKey) o;
            return Objects.equals(empId, that.empId) && Objects.equals(workDate, that.workDate) && Objects.equals(leafNumberId, that.leafNumberId) && Objects.equals(productId, that.productId) && Objects.equals(processId, that.processId) && Objects.equals(shiftDefId, that.shiftDefId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(empId, workDate, leafNumberId, productId, processId, shiftDefId);
        }
    }

    /**
     * 自定义分片器（优化并行流的任务拆分策略，提升负载均衡）
     */
    private static class ShiftSpliterator implements Spliterator<EmpMultiShiftInfoDto> {
        private final Spliterator<EmpMultiShiftInfoDto> original;

        public ShiftSpliterator(Spliterator<EmpMultiShiftInfoDto> original) {
            this.original = original;
        }

        @Override
        public boolean tryAdvance(Consumer<? super EmpMultiShiftInfoDto> action) {
            return original.tryAdvance(action);
        }

        @Override
        public Spliterator<EmpMultiShiftInfoDto> trySplit() {
            // 按数据量动态拆分（小批量拆分，避免单任务过大）
            Spliterator<EmpMultiShiftInfoDto> prefix = original.trySplit();
            return prefix != null ? new ShiftSpliterator(prefix) : null;
        }

        @Override
        public long estimateSize() {
            return original.estimateSize();
        }

        @Override
        public int characteristics() {
            return original.characteristics() | Spliterator.SIZED | Spliterator.SUBSIZED;
        }
    }

    public void genEmpWorkingHourCompensatoryQuota(String tenantId, Long userId, Long startDate, Long endDate, List<WorkingHourAnalyzeDo> analyzeList) {
        if (null == analyzeList || analyzeList.isEmpty()) {
            return;
        }
        List<WorkingHourAnalyzeDo> genCompensatoryQuotaAnalyzeList = analyzeList.stream().filter(analyze -> (null != analyze.getWorkDayOtDuration() && BigDecimal.ZERO.compareTo(analyze.getWorkDayOtDuration()) < 0)
                || (null != analyze.getRestDayOtDuration() && BigDecimal.ZERO.compareTo(analyze.getRestDayOtDuration()) < 0)).collect(Collectors.toList());
        if (analyzeList.isEmpty()) {
            return;
        }
        try {
            SpringUtils.getBean(IQuotaService.class).genEmpWorkingHourQuotaForCompensatoryLeave(tenantId, userId, startDate, endDate, genCompensatoryQuotaAnalyzeList);
        } catch (Exception e) {
            log.error("genEmpWorkingHourCompensatoryQuota exception:{}", e.getMessage(), e);
        }
    }

    private void summaryWorkingHourCompletedQuantity(List<WorkingHourAnalyzeDo> result, WfmWorkingHourAnalyzeInfo analyzeInfo) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        Map<String, List<WorkingHourAnalyzeDo>> empWorkingHourGroupMap = result.stream().collect(Collectors.groupingBy(workingHour ->
                String.format("%s_%s_%s_%s", workingHour.getEmpId(), workingHour.getProcessId(), workingHour.getProductId(), workingHour.getOrderId())));
        for (Map.Entry<String, List<WorkingHourAnalyzeDo>> entry : empWorkingHourGroupMap.entrySet()) {
            List<WorkingHourAnalyzeDo> list = entry.getValue();
            if (list.size() <= 1) {
                continue;
            }
            Long orderId = Long.parseLong(entry.getKey().split("_")[3]);
            Optional.ofNullable(analyzeInfo.getWfmOrderInfo(orderId)).filter(order -> "2".equals(order.getCompletionType())).ifPresent(order -> {
                Long actualCompletionTime = DateUtil.getOnlyDate(new Date(order.getActualCompletionTime()));
                for (WorkingHourAnalyzeDo row : list) {
                    if (actualCompletionTime.equals(row.getBelongDate())) {
                        BigDecimal workDayActualWorkTime = list.stream().map(WorkingHourAnalyzeDo::getWorkDayActualWorkTime).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal processWorkTime = list.stream().map(WorkingHourAnalyzeDo::getProcessWorkTime).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal holidayDayActualWorkTime = list.stream().map(WorkingHourAnalyzeDo::getHolidayActualWorkTime).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        row.setCompletedQuantity(order.getQuantity());
                        if (BigDecimal.ZERO.compareTo(processWorkTime) < 0) { //计算实际完成数量，除数不能为零
                            row.setActualCompletedQuantity(workDayActualWorkTime.add(holidayDayActualWorkTime).multiply(BigDecimal.valueOf(Optional.ofNullable(order.getQuantity()).orElse(0))).divide(processWorkTime, 2, RoundingMode.HALF_UP));
                        }
                    } else {
                        row.setCompletedQuantity(0);
                        row.setActualCompletedQuantity(BigDecimal.ZERO);
                    }
                }
            });
        }
    }

    private List<WorkingHourAnalyzeDo> calInventoryOrder(String tenantId, Long startDate, Long endDate, List<WorkingHourAnalyzeDo> result, UserInfo userInfo) {
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId).andNe("deleted", Boolean.TRUE.toString());
        if (null != startDate && null != endDate) {
            dataFilter = dataFilter.andGe("actualStorageTime", String.valueOf(startDate * 1000));
            dataFilter = dataFilter.andLe("actualStorageTime", String.valueOf(endDate * 1000));
        }
        dataFilter = dataFilter.andEq("storageType", "1").andEq("completionType", "2").andNe("actualCompletionTime", null);
        List<WfmOrderInfo> orderInfoList = getWfmOrdersByCondition(tenantId, dataFilter);
        if (CollectionUtils.isEmpty(orderInfoList)) {
            return Collections.emptyList();
        }
        orderInfoList = orderInfoList.stream().filter(order -> !isSameMonth(order.getActualCompletionTime(), order.getActualStorageTime())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderInfoList)) {
            return Collections.emptyList();
        }
        List<String> orderIds = orderInfoList.stream().map(WfmOrderInfo::getBid).collect(Collectors.toList());
        String anyOrderIds = CollectionUtils.isEmpty(orderIds) ? null : "'{" + StringUtils.join(orderIds, ",").concat("}'");
        List<String> productIds = orderInfoList.stream().map(WfmOrderInfo::getProductId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        String anyProductIds = CollectionUtils.isEmpty(productIds) ? null : "'{" + StringUtils.join(productIds, ",").concat("}'");
        List<WorkingHourAnalyzeDo> analyzeList = workingHourAnalyzeDo.getDayAnalyseListByCondition(tenantId, startDate, endDate, anyOrderIds, anyProductIds);
        if (CollectionUtils.isEmpty(analyzeList)) {
            return Collections.emptyList();
        }
        analyzeList = filterAnalyzeList(analyzeList, result);
        analyzeList = distinctByKey(analyzeList, analyze -> String.format("%s_%s_%s_%s", analyze.getEmpId(), analyze.getProcessId(), analyze.getProductId(), analyze.getOrderId()));
        analyzeList = filterNoShiftAnalyzeList(tenantId, endDate, orderIds, analyzeList);
        return storedOrderAnalyze(tenantId, analyzeList, orderInfoList, userInfo);
    }

    private List<WorkingHourAnalyzeDo> filterNoShiftAnalyzeList(String tenantId, Long periodDate, List<String> orderIds, List<WorkingHourAnalyzeDo> analyzeList) {
        if (null == analyzeList || analyzeList.size() == 0) {
            return analyzeList;
        }
        long startDate = getFirstDayOfMonthAtZero(periodDate);
        long endDate = getFirstDayOfMonthAtZero(periodDate);
        List<Long> empIds = analyzeList.stream().map(WorkingHourAnalyzeDo::getEmpId).distinct().collect(Collectors.toList());
        List<EmpMultiShiftInfoDto> empShifts = empScheduleService.getEmpWfmShiftInfos(tenantId, startDate, endDate, empIds,
                orderIds.stream().map(Long::parseLong).collect(Collectors.toList()), true);
        List<String> empProcessOrderKeyList = empShifts.stream().map(shift -> String.format("%s_%s_%s_%s", shift.getEmpId(), shift.getProcessId(), shift.getProductId(), shift.getLeafNumberId())).collect(Collectors.toList());
        return analyzeList.stream().filter(analyze -> !empProcessOrderKeyList.contains(String.format("%s_%s_%s_%s", analyze.getEmpId(), analyze.getProcessId(), analyze.getProductId(), analyze.getOrderId()))).collect(Collectors.toList());
    }

    // 判断两个秒级时间戳是否属于同月
    public boolean isSameMonth(long timestampSec1, long timestampSec2) {
        // 转换为毫秒级时间戳
        long millis1 = timestampSec1 * 1000;
        long millis2 = timestampSec2 * 1000;
        // 解析为本地日期
        LocalDate date1 = Instant.ofEpochMilli(millis1).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate date2 = Instant.ofEpochMilli(millis2).atZone(ZoneId.systemDefault()).toLocalDate();
        // 比对年份和月份
        return date1.getYear() == date2.getYear() && date1.getMonthValue() == date2.getMonthValue();
    }

    /**
     * 获取当月第一天00:00:00的秒级时间戳
     * @param timestampSec 输入的秒级时间戳
     * @return 当月第一天零点的秒级时间戳
     */
    public long getFirstDayOfMonthAtZero(long timestampSec) {
        // 秒级转毫秒级，解析为本地日期
        LocalDate date = Instant.ofEpochMilli(timestampSec * 1000).atZone(ZoneId.systemDefault()).toLocalDate();
        // 当月第一天的00:00:00
        LocalDate firstDay = date.withDayOfMonth(1);
        return firstDay.atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 获取当月最后一天00:00:00的秒级时间戳
     * @param timestampSec 输入的秒级时间戳
     * @return 当月最后一天零点的秒级时间戳
     */
    public long getLastDayOfMonthAtZero(long timestampSec) {
        // 秒级转毫秒级，解析为本地日期
        LocalDate date = Instant.ofEpochMilli(timestampSec * 1000).atZone(ZoneId.systemDefault()).toLocalDate();
        // 当月最后一天的00:00:00
        LocalDate lastDay = date.with(TemporalAdjusters.lastDayOfMonth());
        return lastDay.atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
    }

    private List<WorkingHourAnalyzeDo> storedOrderAnalyze(String tenantId, List<WorkingHourAnalyzeDo> analyzeList, List<WfmOrderInfo> orderInfoList, UserInfo userInfo) {
        Map<Long, WfmOrderInfo> orderInfoMap = orderInfoList.stream().collect(Collectors.toMap(order -> Long.valueOf(order.getBid()), Function.identity(), (v1, v2) -> v2));
        List<WorkingHourAnalyzeDo> storedOrderAnalyze = Lists.newArrayList();
        for (WorkingHourAnalyzeDo hourAnalyze : analyzeList) {
            Optional.ofNullable(orderInfoMap.get(hourAnalyze.getOrderId())).filter(order -> "1".equals(order.getStorageType())
                            && null != order.getActualStorageTime())
                    .ifPresent(order -> {
                        Long actualStorageTime = order.getActualStorageTime();
                        WorkingHourAnalyzeDo analyze = new WorkingHourAnalyzeDo();
                        analyze.setAnalyzeId(snowflakeUtil.createId());
                        analyze.setTenantId(tenantId);
                        analyze.setBelongDate(DateUtil.getOnlyDate(new Date(actualStorageTime)));
                        analyze.setEmpId(hourAnalyze.getEmpId());
                        analyze.setShiftId(hourAnalyze.getShiftId());
                        analyze.setOrderId(hourAnalyze.getOrderId());
                        analyze.setOrderNum(order.getBladeNumber());
                        analyze.setProductId(hourAnalyze.getProductId());
                        analyze.setProductName(hourAnalyze.getProductName());
                        analyze.setProcessId(hourAnalyze.getProcessId());
                        analyze.setProcessCode(hourAnalyze.getProcessCode());
                        analyze.setProcessName(hourAnalyze.getProcessName());
                        analyze.setProcessTime(hourAnalyze.getProcessTime());
                        analyze.setProcessWorkTime(hourAnalyze.getProcessWorkTime());

                        analyze.setWorkTime(0);
                        analyze.setActualWorkTime(BigDecimal.ZERO);
                        analyze.setHolidayActualWorkTime(BigDecimal.ZERO);
                        analyze.setWorkDayActualWorkTime(BigDecimal.ZERO);
                        analyze.setEffectWorkTime(BigDecimal.ZERO);
                        analyze.setAbnormalWorkTime(BigDecimal.ZERO);

                        analyze.setWorkDayOtDuration(BigDecimal.ZERO);
                        analyze.setRestDayOtDuration(BigDecimal.ZERO);

                        /*analyze.setCompletedQuantity(hourAnalyze.getCompletedQuantity());
                        analyze.setActualCompletedQuantity(hourAnalyze.getActualCompletedQuantity());*/
                        analyze.setCompletedQuantity(0);
                        analyze.setActualCompletedQuantity(BigDecimal.ZERO);
                        analyze.setStored("1".equals(order.getStorageType()));//入库状态
                        analyze.setStoredTime(actualStorageTime / 1000); //入库时间
                        analyze.setProcessType(hourAnalyze.getProcessType());
                        String notes = "";
                        if (null != order.getActualCompletionTime()) {
                            notes = String.format("%s完成，", DateUtil.getDateStrByTimesamp(order.getActualCompletionTime() / 1000));
                        }
                        if (null != analyze.getStoredTime()) {
                            notes += String.format("%s入库", DateUtil.getDateStrByTimesamp(analyze.getStoredTime()));
                        }
                        analyze.setNotes(notes);

                        analyze.setDeleted(DeleteStatusEnum.UN_DELETED.getIndex());
                        analyze.setCreator(userInfo.getUserId());
                        analyze.setCreateTime(DateUtil.getCurrentTime(true));
                        storedOrderAnalyze.add(analyze);
                    });
        }
        return storedOrderAnalyze;
    }

    private List<WorkingHourAnalyzeDo> filterAnalyzeList(List<WorkingHourAnalyzeDo> analyzeList, List<WorkingHourAnalyzeDo> result) {
        if (null == result || result.size() == 0) {
            return analyzeList;
        }
        List<String> empProcessOrderKeyList = result.stream().map(analyze -> String.format("%s_%s_%s_%s", analyze.getEmpId(), analyze.getProcessId(), analyze.getProductId(), analyze.getOrderId())).collect(Collectors.toList());
        return analyzeList.stream().filter(analyze -> !empProcessOrderKeyList.contains(String.format("%s_%s_%s_%s", analyze.getEmpId(), analyze.getProcessId(), analyze.getProductId(), analyze.getOrderId()))).collect(Collectors.toList());
    }

    private void setWorkingHourAnalyzeInfo(String tenantId, Long startDate, Long endDate, List<EmpMultiShiftInfoDto> empShifts, WfmWorkingHourAnalyzeInfo analyzeInfo) {
        List<Long> empIds = empShifts.stream().map(EmpMultiShiftInfoDto::getEmpId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Long> orderIds = empShifts.stream().map(EmpMultiShiftInfoDto::getLeafNumberId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<WfmOrderInfo> orderInfoList = getWfmOrders(tenantId, orderIds);
        analyzeInfo.setOrderInfoMap(orderInfoList);
        // 工序
        List<Long> processIds = empShifts.stream().map(EmpMultiShiftInfoDto::getProcessId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        analyzeInfo.setWorkingProcessMap(getWorkingProcessMap(tenantId, processIds));
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            // 员工信息
            analyzeInfo.setEmpInfoMap(getEmpInfoMap(tenantId, empIds.stream().map(String::valueOf).collect(Collectors.toList())));
            // 有效扫码时间
            analyzeInfo.setEffectiveTimeDtoList(getEffectiveTimeDtoList(tenantId, startDate * 1000, endDate * 1000, orderIds));
            // 查询异常工时申请数据
            analyzeInfo.setEmpAbnormalWorkingHourList(getEmpAbnormalWorkingHours(tenantId, startDate, endDate, empIds, orderIds, false));
            // 查询扫码数据
            analyzeInfo.setEmpRegisterRecords(getEmpRegisterRecordList(tenantId, startDate * 1000, endDate * 1000, empIds, orderIds));
            // 工序开始结束时间
            analyzeInfo.setConfirmProcessCompletions(getConfirmProcessCompletions(tenantId, startDate * 1000, endDate * 1000, orderIds));
            // 员工报工工时
            analyzeInfo.setEmpAverageAttendanceTimeMap(getEmpAverageWorkingHourMap(tenantId, startDate * 1000, endDate * 1000, orderIds));
        } finally {
            UserContext.removeSecurityUserInfo();
        }
    }

    private <T> List<T> distinctByKey(List<T> list, Function<T, String> keyExtractor) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().collect(Collectors.collectingAndThen(Collectors.toMap(keyExtractor, Function.identity(), (v1, v2) -> v2), map -> new ArrayList<>(map.values())));
    }

    private String getWorkProcessCode(Long processId, WfmWorkingHourAnalyzeInfo analyzeInfo) {
        return Optional.ofNullable(analyzeInfo.getWorkingProcess(processId)).map(WfmProcessInfo::getProcessCode).orElse("");
    }

    private WfmProcessInfo getWorkProcess(Long processId, WfmWorkingHourAnalyzeInfo analyzeInfo) {
        return analyzeInfo.getWorkingProcess(processId);
    }

    private WorkingHourAnalyzeDo getWorkingHourAnalyze(EmpMultiShiftInfoDto empShift, WfmWorkingHourAnalyzeInfo analyzeInfo, UserInfo userInfo) {
        Long empId = empShift.getEmpId();
        Long workDate = empShift.getWorkDate();
        Long shiftId = empShift.getShiftDefId();
        WorkingHourAnalyzeDo analyze = new WorkingHourAnalyzeDo();
        analyze.setAnalyzeId(snowflakeUtil.createId());
        analyze.setTenantId(userInfo.getTenantId());
        analyze.setBelongDate(workDate);
        analyze.setEmpId(empId);
        analyze.setShiftId(shiftId);
        analyze.setOrderId(empShift.getLeafNumberId());
        analyze.setOrderNum(empShift.getLeafNumber());
        analyze.setProductId(empShift.getProductId());
        analyze.setProductName(empShift.getProductName());
        analyze.setProcessId(empShift.getProcessId());
        analyze.setProcessCode(getWorkProcessCode(empShift.getProcessId(), analyzeInfo));
        analyze.setProcessName(empShift.getProcessName());
        //排班工时
        analyze.setWorkTime(Optional.ofNullable(empShift.getWorkTotalTime()).orElse(0));
        //异常工时
        List<EmpAbnormalWorkingHourDto> empAbnormalWorkingHourList = analyzeInfo.getEmpAbnormalWorkingHour(empId, empShift.getProcessId(), empShift.getLeafNumberId(), shiftId, workDate);
        analyze.setAbnormalWorkTime(getEmpAbnormalWorkingHour(empAbnormalWorkingHourList));
        analyze.setAbnormalContent(getEmpAbnormalWorkingHourContent(empAbnormalWorkingHourList));
        //计算工时
        this.calWorkTime(empShift, analyze, analyzeInfo);
        // 加班时长计算
        this.handlePieceworkEmployee(analyzeInfo, workDate, empShift, analyze, empId);
        analyze.setDeleted(DeleteStatusEnum.UN_DELETED.getIndex());
        analyze.setCreator(userInfo.getUserId());
        analyze.setCreateTime(DateUtil.getCurrentTime(true));
        return analyze;
    }

    /**
     * 处理计件员工的加班时长计算
     */
    private void handlePieceworkEmployee(WfmWorkingHourAnalyzeInfo analyzeInfo, Long workDate, EmpMultiShiftInfoDto empShift, WorkingHourAnalyzeDo analyze, Long empId) {
        // 提取Optional内部逻辑为独立方法，降低聚合度
        Optional.ofNullable(analyzeInfo.getEmpInfo(empId)).ifPresent(empWorkInfo -> processPieceworkEmp(empWorkInfo, workDate, empShift, analyzeInfo, analyze));
    }

    /**
     * 处理计件员工的核心业务逻辑
     */
    private void processPieceworkEmp(EmpWorkInfo empWorkInfo, Long workDate, EmpMultiShiftInfoDto empShift, WfmWorkingHourAnalyzeInfo analyzeInfo, WorkingHourAnalyzeDo analyze) {
        // 1. 判断是否为计件员工
        if (!isPieceworkEmployee(empWorkInfo)) {
            return;
        }
        // 2. 判断是否为非法定节假日
        if (isLegalHoliday(workDate, empShift.getDateType(), analyzeInfo)) {
            return;
        }
        // 3. 初始化加班时长变量
        BigDecimal workDayOtDuration = BigDecimal.ZERO;
        BigDecimal restDayOtDuration = BigDecimal.ZERO;
        // 4. 判断是否为标准工时
        boolean isStandardWorkHour = isStandardWorkHour(empWorkInfo);
        // 5. 计算加班时长
        if (!isStandardWorkHour) {// 综合工时：不区分休息日/工作日
            workDayOtDuration = calculateStandardWorkDayOt(analyze);
        } else {
            // 标准工时：工作日加班 = 实际工时 - 标准日工时（8小时）
            if (isWeekend(workDate)) {
                restDayOtDuration = analyze.getEffectWorkTime(); // 休息日全为加班
            } else {
                workDayOtDuration = calculateStandardWorkDayOt(analyze); // 工作日加班计算
            }
        }
        // 6. 确保加班时长非负
        workDayOtDuration = ensureNonNegative(workDayOtDuration);
        restDayOtDuration = ensureNonNegative(restDayOtDuration);
        // 7. 设置结果
        analyze.setWorkDayOtDuration(workDayOtDuration);
        analyze.setRestDayOtDuration(restDayOtDuration);
    }

    /**
     * 判断是否为计件员工
     */
    private boolean isPieceworkEmployee(EmpWorkInfo empWorkInfo) {
        DictSimple salaryWorkHour = empWorkInfo.getSalaryWorkHour();
        return salaryWorkHour != null && StringUtil.isNotBlank(salaryWorkHour.getCode()) && PIECEWORK_CODE.equals(salaryWorkHour.getCode());
    }

    /**
     * 判断是否为法定节假日（原checkIfDateType的语义化封装）
     */
    private boolean isLegalHoliday(Long workDate, Integer dateType, WfmWorkingHourAnalyzeInfo analyzeInfo) {
        return checkIfDateType(workDate, dateType, analyzeInfo, 3); // 3：法定节假日类型
    }

    /**
     * 判断是否为标准工时
     */
    private boolean isStandardWorkHour(EmpWorkInfo empWorkInfo) {
        EnumSimple workHour = empWorkInfo.getWorkHour();
        return workHour != null && StringUtil.isNotBlank(workHour.getValue()) && STANDARD_WORK_HOUR_CODE.equals(workHour.getValue());
    }

    /**
     * 计算标准工时下的工作日加班时长
     */
    private BigDecimal calculateStandardWorkDayOt(WorkingHourAnalyzeDo analyze) {
        // 确保effectWorkTime非null（避免NPE）
        BigDecimal effectWorkTime = Optional.ofNullable(analyze.getEffectWorkTime()).orElse(BigDecimal.ZERO);
        return effectWorkTime.subtract(STANDARD_DAILY_WORK_MINUTES);
    }

    /**
     * 确保数值非负（提取重复逻辑）
     */
    private BigDecimal ensureNonNegative(BigDecimal value) {
        return value.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : value;
    }

    private boolean isWeekend(long timestamp) {
        // 将秒级时间戳转换为 LocalDate
        LocalDate date = Instant.ofEpochSecond(timestamp).atZone(DEFAULT_ZONE).toLocalDate();
        // 获取星期几
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        // 判断是否为周六或周日
        return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;
    }

    private void resetWorkTimeFields(WorkingHourAnalyzeDo analyze) {
        analyze.setActualWorkTime(BigDecimal.ZERO);
        analyze.setHolidayActualWorkTime(BigDecimal.ZERO);
        analyze.setEffectWorkTime(BigDecimal.ZERO);
        analyze.setProcessWorkTime(BigDecimal.ZERO);
        analyze.setWorkDayActualWorkTime(BigDecimal.ZERO);
        analyze.setCompletedQuantity(0);
        analyze.setActualCompletedQuantity(BigDecimal.ZERO);
        analyze.setStoredQuantity(0);
        analyze.setWorkDayOtDuration(BigDecimal.ZERO);
        analyze.setRestDayOtDuration(BigDecimal.ZERO);
    }

    private void setWorkTimeFromJobWorkingHour(WorkingHourAnalyzeDo analyze, AverageAttendanceTimeDto jobWorkingHour,
                                               EmpMultiShiftInfoDto empShift, WfmWorkingHourAnalyzeInfo analyzeInfo) {
        Optional.ofNullable(jobWorkingHour.getAverageAttendanceTime()).ifPresent(avgTime -> {
            analyze.setActualWorkTime(new BigDecimal(avgTime).multiply(BigDecimal.valueOf(60)));
            analyze.setProcessWorkTime(analyze.getActualWorkTime());
            analyze.setWorkDayActualWorkTime(analyze.getActualWorkTime());
        });
        Optional.ofNullable(jobWorkingHour.getProcessWorkingHours()).ifPresent(processWorkingHour -> analyze.setProcessWorkTime(new BigDecimal(processWorkingHour).multiply(BigDecimal.valueOf(60))));
        if (checkIfDateType(empShift.getWorkDate(), empShift.getDateType(), analyzeInfo, 3)) {
            analyze.setHolidayActualWorkTime(analyze.getActualWorkTime());
            analyze.setWorkDayActualWorkTime(BigDecimal.ZERO);
        }
        //有效工时
        //Optional.ofNullable(jobWorkingHour.getEffectiveWorkingHour()).ifPresent(effTime -> analyze.setEffectWorkTime(new BigDecimal(effTime).multiply(BigDecimal.valueOf(60))));
    }

    private boolean checkIfExistJobApplyWorkingHour(AverageAttendanceTimeDto jobWorkingHour) {
        return Optional.ofNullable(jobWorkingHour).isPresent();
    }

    private boolean checkWfmTypeAndWorkShopSection(WfmProcessInfo processInfo) {
        AtomicBoolean wfmTypeAndWorkShopSection = new AtomicBoolean(false);
        Optional.ofNullable(processInfo).ifPresent(process -> {
            //处理类型
            boolean jobApplicationType = process.getWfmType() != null && StringUtil.isNotBlank(process.getWfmType().getCode()) && "ApplyForWork".equals(process.getWfmType().getCode());
            wfmTypeAndWorkShopSection.set(jobApplicationType);
        });
        return wfmTypeAndWorkShopSection.get();
    }

    private void calWorkTime(EmpMultiShiftInfoDto empShift, WorkingHourAnalyzeDo analyze, WfmWorkingHourAnalyzeInfo analyzeInfo) {
        Long workDate = empShift.getWorkDate();
        //regStartTime 扫码开始时间，regEndTime 扫码结束时间
        WfmScanCheckInDto regStartRecord = getRegDateRecord("startCheckIn", empShift, analyzeInfo);
        WfmScanCheckInDto regEndRecord = getRegDateRecord("endCheckIn", empShift, analyzeInfo);
        /*Long regStartTime = null;
        Long regEndTime = null;*/
        if (Optional.ofNullable(regStartRecord).isPresent()) {
            analyze.setSignInId(regStartRecord.getRecordId());
            analyze.setRegStartTime(regStartRecord.getRegDateTime());
            //regStartTime = regStartRecord.getRegDateTime();
        }
        if (Optional.ofNullable(regEndRecord).isPresent()) {
            analyze.setSignOffId(regEndRecord.getRecordId());
            analyze.setRegEndTime(regEndRecord.getRegDateTime());
            //regEndTime = regEndRecord.getRegDateTime();
        }
        // 初始化工时字段
        resetWorkTimeFields(analyze);
        if (empShift.getDateType() != 1) {
            return;
        }
        // 有效扫码时间
        EffectiveTimeDto effectiveTimeDto = analyzeInfo.getEffectiveTime(empShift.getEmpId(), empShift.getLeafNumberId(), empShift.getProcessId(), empShift.getShiftDefId(), workDate * 1000);
        boolean isEffectiveTime = null == effectiveTimeDto;
        if (isEffectiveTime) {
            effectiveTimeDto = new EffectiveTimeDto();
            effectiveTimeDto.setOrderId(Optional.ofNullable(empShift.getLeafNumberId()).map(String::valueOf).orElse(null));
            effectiveTimeDto.setProcessId(Optional.ofNullable(empShift.getProcessId()).map(String::valueOf).orElse(null));
            effectiveTimeDto.setShiftId(Optional.ofNullable(empShift.getShiftDefId()).map(String::valueOf).orElse(null));
            effectiveTimeDto.setSchedulingTime(String.valueOf(empShift.getWorkDate() * 1000));
            List<MultiWorkTimeInfoSimpleVo> multiWorkTimes = empShift.getMultiWorkTimes();
            if (CollectionUtils.isNotEmpty(multiWorkTimes)) {
                multiWorkTimes.sort(Comparator.comparing(MultiWorkTimeInfoSimpleVo::doGetRealStartTime));
                effectiveTimeDto.setEffectiveStartTime(String.valueOf((workDate + multiWorkTimes.get(0).doGetRealStartTime() * 60) * 1000));
                effectiveTimeDto.setEffectiveEndTime(String.valueOf((workDate + multiWorkTimes.get(multiWorkTimes.size() - 1).doGetRealEndTime() * 60) * 1000));
            } else {
                effectiveTimeDto.setEffectiveStartTime(String.valueOf(((empShift.getStartTimeBelong() == 2 ? workDate + 86400 : workDate) + empShift.getStartTime() * 60) * 1000));
                effectiveTimeDto.setEffectiveEndTime(String.valueOf(((empShift.getEndTimeBelong() == 2 ? workDate + 86400 : workDate) + empShift.getEndTime() * 60) * 1000));
            }
        }
        // 实际班次时间(有效扫码开始结束时间)
        analyze.setEffectiveScanTime(FastjsonUtil.toJsonStr(!isEffectiveTime ? Collections.singletonList(new RegDateRangeTimePair(Long.parseLong(effectiveTimeDto.getEffectiveStartTime()) / 1000, Long.parseLong(effectiveTimeDto.getEffectiveEndTime()) / 1000)) : new ArrayList<>()));
        // 工序开始结束时间
        WfmProcessWorkHourDto processWorkingHour = analyzeInfo.getConfirmProcessCompletion(empShift.getEmpId(), empShift.getLeafNumberId(), empShift.getProcessId(), empShift.getShiftDefId(), workDate);
        // 工序开始结束时间
        Optional.ofNullable(processWorkingHour).ifPresent(time -> analyze.setProcessTime(FastjsonUtil.toJsonStr(Collections.singletonList(new RegDateRangeTimePair(time.getProcessStartTime(), time.getProcessEndTime())))));
        // 实际工时
        BigDecimal actualWorkTime = BigDecimal.ZERO;
        // 工作日实际工时
        BigDecimal workDayActualWorkTime = BigDecimal.ZERO;
        List<Pair<WfmScanCheckInDto, WfmScanCheckInDto>> pairedCheckRecords = getStrictCheckInOutPairs(empShift, analyzeInfo);
        // 签到记录
        analyze.setRegDateTime(convertPairsToJson(pairedCheckRecords));
        // 工序相关数据
        WfmProcessInfo processInfo = getWorkProcess(empShift.getProcessId(), analyzeInfo);
        Optional.ofNullable(processInfo).ifPresent(process -> {
            analyze.setProcessType(process.getWfmType().getCode());//工序类型
            analyze.setProcessName(Optional.ofNullable(process.getSalaryProcess()).orElse(process.getProcessName()));//计薪工序
            analyze.setProcessCode(process.getProcessCode());//工序编码
        });
        Optional.ofNullable(analyzeInfo.getWfmOrderInfo(empShift.getLeafNumberId())).ifPresent(order -> {
            // 缓存所有可能多次使用的值
            Long actualCompletionTime = order.getActualCompletionTime();
            Long actualStorageTime = order.getActualStorageTime();
            String completionType = order.getCompletionType();
            String storageType = order.getStorageType();
            // 使用缓存的值进行计算
            analyze.setCompletedQuantity("2".equals(completionType) ? order.getQuantity() : 0);//完成数量
            // 安全处理时间（避免拆箱NPE）
            analyze.setCompletedTime(actualCompletionTime != null ? actualCompletionTime / 1000 : null);//完成时间
            analyze.setStored("1".equals(storageType));//是否已入库
            analyze.setStoredTime(actualStorageTime != null ? actualStorageTime / 1000 : null); //入库时间
            boolean ifStored = Optional.ofNullable(analyze.getStored()).orElse(Boolean.FALSE);
            analyze.setStoredQuantity(ifStored ? order.getQuantity() : 0);
        });
        // 当前日期是否是法定假日
        boolean isHoliday = checkIfDateType(empShift.getWorkDate(), empShift.getDateType(), analyzeInfo, 3);
        boolean processCrossDateType = checkCrossDateType(processWorkingHour, empShift, analyzeInfo);
        List<WfmScanCheckInDto> sortedRecords = getRegDateRecords(empShift, analyzeInfo);
        List<Pair<WfmScanCheckInDto, WfmScanCheckInDto>> checkRecords = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sortedRecords)) {
            if (sortedRecords.size() > 1) {
                checkRecords.add(Pair.of(sortedRecords.get(0), sortedRecords.get(sortedRecords.size() - 1)));
            } else {
                checkRecords.add(Pair.of(sortedRecords.get(0), null));
            }
        }
        // 筛选有效扫码时间
        checkRecords = getEffectPairedRecords(null, effectiveTimeDto, checkRecords);
        for (Pair<WfmScanCheckInDto, WfmScanCheckInDto> pair : checkRecords) {
            WfmScanCheckInDto checkIn = pair.getLeft();
            WfmScanCheckInDto checkOut = pair.getRight();
            if (null == checkIn || null == checkOut) {
                continue;
            }
            //扫码开始时间
            Long checkInRegDateTime = checkIn.getRegDateTime();
            //扫码结束时间
            Long checkOutRegDateTime = checkOut.getRegDateTime();
            int currentIndex = checkRecords.indexOf(pair);
            /* 迟到豁免逻辑
             * 处理签到时间（包含早到、迟到逻辑）
             * 仅对配对记录中的第一条记录进行处理
             */
            if (currentIndex == 0) {
                checkInRegDateTime = getCheckInRegDateTime(checkInRegDateTime, null, effectiveTimeDto);
            }
            /* 早退豁免逻辑
             * 处理签退时间（包含晚走、早退逻辑）
             * 仅对配对记录中的最后一条记录进行处理
             */
            if (currentIndex == checkRecords.size() - 1) {
                checkOutRegDateTime = getCheckOutRegDateTime(checkOutRegDateTime, null, effectiveTimeDto);
            }
            // 实际工时 = 员工扫码结束 - 扫码开始时间 - 休息时间
            actualWorkTime = actualWorkTime.add(calActualWorkTime(checkInRegDateTime, checkOutRegDateTime, empShift));
            // 工作日实际工时
            if (processCrossDateType && checkDifferentTimestampCrossDay(checkInRegDateTime, checkOutRegDateTime)) {
                if (!isHoliday) {
                    workDayActualWorkTime = workDayActualWorkTime.add(calActualWorkTime(checkInRegDateTime, workDate + 86400, empShift));
                } else {
                    workDayActualWorkTime = workDayActualWorkTime.add(calActualWorkTime(workDate + 86400, checkOutRegDateTime, empShift));
                }
            } else if (!isHoliday) {
                workDayActualWorkTime = workDayActualWorkTime.add(actualWorkTime);
            }
            // 有效工时 = 员工扫码结束 - 扫码开始时间和有效扫码时间范围的交集 - 休息时间
            // effectiveWorkTime = effectiveWorkTime.add(calEffectiveWorkTime(checkIn.getRegDateTime(), checkOut.getRegDateTime(), empShift, effectiveTimeDto, processWorkingHour));
        }
        boolean applyForWork = checkWfmTypeAndWorkShopSection(processInfo);
        if (applyForWork && !isHoliday && !processCrossDateType) {
            // 报工工时逻辑
            AverageAttendanceTimeDto jobWorkingHour = analyzeInfo.getEmpAverageAttendanceTime(empShift.getEmpId(), workDate, empShift.getProcessId(), empShift.getLeafNumberId(), empShift.getShiftDefId());
            if (checkIfExistJobApplyWorkingHour(jobWorkingHour)) {
                setWorkTimeFromJobWorkingHour(analyze, jobWorkingHour, empShift, analyzeInfo);
            }
            analyze.setEffectWorkTime(analyze.getActualWorkTime().subtract(calculateTotalRestTime(empShift)));
            if (BigDecimal.ZERO.compareTo(analyze.getEffectWorkTime()) > 0) {
                analyze.setEffectWorkTime(BigDecimal.ZERO);
            }
            if (BigDecimal.ZERO.compareTo(analyze.getProcessWorkTime()) < 0) { //计算实际完成数量，除数不能为零
                analyze.setActualCompletedQuantity(analyze.getActualWorkTime().multiply(BigDecimal.valueOf(Optional.ofNullable(analyze.getCompletedQuantity()).orElse(0))).divide(analyze.getProcessWorkTime(), 2, RoundingMode.HALF_UP));
            }
            return;
        }
        // 有效工时 = 实际工时 - 休息时长
        // 有效工时
        BigDecimal effectiveWorkTime = actualWorkTime.subtract(calculateTotalRestTime(empShift));
        if (BigDecimal.ZERO.compareTo(effectiveWorkTime) > 0) {
            effectiveWorkTime = BigDecimal.ZERO;
        }
        analyze.setEffectWorkTime(effectiveWorkTime);
        // 计算工序工时，非报工逻辑
        analyze.setProcessWorkTime(calculateProcessWorkTime(empShift, processWorkingHour));
        // 实际工时
        analyze.setActualWorkTime(actualWorkTime);
        // 工作日实际工时
        analyze.setWorkDayActualWorkTime(workDayActualWorkTime);
        // 法定节假日实际工时
        analyze.setHolidayActualWorkTime(actualWorkTime.subtract(workDayActualWorkTime));
        // 三倍产量是否入库
        analyze.setTripleProductStored(analyze.getHolidayActualWorkTime().compareTo(BigDecimal.ZERO) > 0 && Optional.ofNullable(analyze.getStored()).orElse(false));
        if (BigDecimal.ZERO.compareTo(analyze.getProcessWorkTime()) < 0) { //计算实际完成数量，除数不能为零
            analyze.setActualCompletedQuantity(workDayActualWorkTime.multiply(BigDecimal.valueOf(Optional.ofNullable(analyze.getCompletedQuantity()).orElse(0))).divide(analyze.getProcessWorkTime(), 2, RoundingMode.HALF_UP));
        }
    }

    private BigDecimal calculateTotalRestTime(EmpMultiShiftInfoDto empShift) {
        BigDecimal totalRestTime = BigDecimal.ZERO;
        List<MultiWorkTimeInfoSimpleVo> multiWorkTimes = empShift.getMultiWorkTimes();
        if (CollectionUtils.isEmpty(multiWorkTimes)) {
            return totalRestTime;
        }
        // 定义时间常量（单位：分钟）
        final int T07_00 = 420;   // 07:00 = 7*60
        final int T12_00 = 720;   // 12:00 = 12*60
        final int T18_00 = 1080;  // 18:00 = 18*60
        final int T22_00 = 1320;  // 22:00 = 22*60
        final int END_OF_DAY = 24 * 60;  // 24:00 (1440分钟)
        // 次日时间点（加1440分钟）
        final int NEXT_07_00 = T07_00 + END_OF_DAY;
        final int NEXT_12_00 = T12_00 + END_OF_DAY;
        final int NEXT_18_00 = T18_00 + END_OF_DAY;
        final int NEXT_22_00 = T22_00 + END_OF_DAY;
        for (MultiWorkTimeInfoSimpleVo segment : multiWorkTimes) {
            int x = segment.doGetRealStartTime();
            int y = segment.doGetRealEndTime();
            if (null != segment.getStartTimeBelong() && segment.getStartTimeBelong() == 2 && null != segment.getEndTimeBelong() && segment.getEndTimeBelong() == 2) {
                x = segment.getStartTime();
                y = segment.getEndTime();
            }
            int z = 0;
            // 按规则顺序匹配当前班段的休息时长
            if (x <= T07_00) {
                if (T07_00 < y && y <= T12_00)
                    z = 30;
                else if (T12_00 < y && y <= T18_00)
                    z = 60;
                else if (T18_00 < y && y <= T22_00)
                    z = 90;
                else if (y > T22_00)
                    z = 120;  // y > T22_00
            } else if (x <= T12_00) {
                if (y > T12_00 && y <= T18_00)
                    z = 30;
                else if (y > T18_00 && y <= T22_00)
                    z = 60;
                else if (y > T22_00)
                    z = 90;
            } else if (x <= T18_00) {
                if (y > T18_00 && y <= T22_00)
                    z = 30;
                else if (y > T22_00)
                    z = 60;
            } else if (x <= T22_00) {
                if (y > T22_00)
                    z = 30;
            } else {
                // 仅当班次跨午夜（调整后y在次日）时才应用新增规则
                if (y >= END_OF_DAY) {
                    if (NEXT_07_00 < y && y <= NEXT_12_00)
                        z = 30;       // 次日12:00前结束
                    else if (NEXT_12_00 < y && y <= NEXT_18_00)
                        z = 60;  // 次日18:00前结束
                    else if (NEXT_18_00 < y && y <= NEXT_22_00)
                        z = 90;  // 次日22:00前结束
                }
            }
            totalRestTime = totalRestTime.add(BigDecimal.valueOf(z));
        }
        return totalRestTime;
    }

    private boolean checkCrossDateType(WfmProcessWorkHourDto processWorkingHour, EmpMultiShiftInfoDto empShift, WfmWorkingHourAnalyzeInfo analyzeInfo) {
        if (null == processWorkingHour) {
            return false;
        }
        long processStartTime = processWorkingHour.getProcessStartTime();
        long processEndTime = processWorkingHour.getProcessEndTime();
        Long workDate = empShift.getWorkDate();
        if (processEndTime <= 0) {
            List<MultiWorkTimeInfoSimpleVo> multiWorkTimes = empShift.getMultiWorkTimes();
            if (CollectionUtils.isNotEmpty(multiWorkTimes)) {
                multiWorkTimes.sort(Comparator.comparing(MultiWorkTimeInfoSimpleVo::doGetRealStartTime));
                processEndTime = workDate + multiWorkTimes.get(multiWorkTimes.size() - 1).doGetRealEndTime() * 60;
            }
        }
        if (checkDifferentTimestampCrossDay(processStartTime, processEndTime)) {
            Integer firstDateType = analyzeInfo.getSpecialDateType(empShift.getWorkDate(), empShift.getDateType());
            Integer secondDateType = analyzeInfo.getSpecialDateType(workDate + 86400, empShift.getDateType());
            return !firstDateType.equals(secondDateType);
        }
        return false;
    }

    /**
     * 计算工序工时，非报工逻辑
     *
     * @param empShift           班次
     * @param processWorkingHour 工序开始结束间
     * @return
     */
    private BigDecimal calculateProcessWorkTime(EmpMultiShiftInfoDto empShift, WfmProcessWorkHourDto processWorkingHour) {
        AtomicReference<BigDecimal> processWorkTimeAtomic = new AtomicReference<>(BigDecimal.ZERO);
        Optional.ofNullable(processWorkingHour).filter(item -> item.getProcessEndTime() > 0 && item.getProcessEndTime() > item.getProcessStartTime())
                .ifPresent(item -> {
                    long startTime = item.getProcessStartTime();
                    long endTime = item.getProcessEndTime();
                    Long workDate = empShift.getWorkDate();
                    // 检查是否跨天且开始日期与当前日期匹配
                    boolean shouldCalWorkTime = !checkDifferentTimestampCrossDayGreaterThan(startTime, endTime) || workDate.equals(DateUtil.getOnlyDate(new Date(startTime * 1000)));
                    if (shouldCalWorkTime) {
                        processWorkTimeAtomic.set(calculateOverlapDurationInMinutes(startTime, endTime, new DateTimeItemDto(startTime, endTime), getRestPeriods(empShift), true));
                    }
                });
        return processWorkTimeAtomic.get();
    }

    private List<DateTimeItemDto> getRestPeriods(EmpMultiShiftInfoDto empShift) {
        List<DateTimeItemDto> restPeriods = new ArrayList<>();
        if (wfmWorkingHourRestDeduction) {
            restPeriods.addAll(getDateTimeItemList(empShift));
        }
        List<MultiWorkTimeInfoSimpleVo> multiWorkTimes = Optional.ofNullable(empShift.getMultiWorkTimes()).orElse(Lists.newArrayList());
        List<DateTimeItemDto> multiFields = Lists.newArrayList();
        Long workDate = empShift.getWorkDate();
        for (MultiWorkTimeInfoSimpleVo multiWorkTime : multiWorkTimes) {
            multiFields.add(new DateTimeItemDto(getRealTime(workDate, multiWorkTime.doGetRealStartTime()), getRealTime(workDate, multiWorkTime.doGetRealEndTime())));
        }
        restPeriods.addAll(generateIntervals(multiFields));
        return restPeriods;
    }

    private List<DateTimeItemDto> generateIntervals(List<DateTimeItemDto> items) {
        List<DateTimeItemDto> intervals = new ArrayList<>();
        // 检查输入是否有效
        if (items == null || items.size() < 2) {
            return intervals; // 返回空列表
        }
        items.sort(Comparator.comparing(DateTimeItemDto::getStart));
        for (int i = 0; i < items.size() - 1; i++) {
            DateTimeItemDto current = items.get(i);
            DateTimeItemDto next = items.get(i + 1);
            // 跳过无效的班段
            if (current == null || next == null)
                continue;
            Long endOfCurrent = current.getEnd();
            Long startOfNext = next.getStart();
            // 检查时间值是否有效
            if (endOfCurrent == null || startOfNext == null) continue;
            // 创建新的时间间隔对象
            intervals.add(new DateTimeItemDto(endOfCurrent, startOfNext));
        }
        return intervals;
    }

    private long getRealTime(Long workDate, Integer realTime) {
        return workDate + realTime * 60;
    }

    /**
     * 获取有效地员工扫码记录
     *
     * @param processWorkingHour 工序开始结束间
     * @param effectiveTimeDto   有效扫码开始结束时间
     * @param pairedCheckRecords 成对地员工扫码记录
     * @return
     */
    private List<Pair<WfmScanCheckInDto, WfmScanCheckInDto>> getEffectPairedRecords(WfmProcessWorkHourDto processWorkingHour, EffectiveTimeDto effectiveTimeDto,
                                                                                    List<Pair<WfmScanCheckInDto, WfmScanCheckInDto>> pairedCheckRecords) {
        if (CollectionUtils.isEmpty(pairedCheckRecords)) {
            return new ArrayList<>();
        }
        return pairedCheckRecords.stream()
                .filter(pair -> {
                    final WfmScanCheckInDto checkIn = pair.getLeft();
                    final WfmScanCheckInDto checkOut = pair.getRight();
                    if (checkIn == null || checkOut == null) return false;
                    final long originalCheckInTime = checkIn.getRegDateTime();
                    final long originalCheckOutTime = checkOut.getRegDateTime();
                    final Pair<Long, Long> validPeriod = calculateValidPeriod(processWorkingHour, effectiveTimeDto, originalCheckInTime, originalCheckOutTime);
                    final long validStart = validPeriod.getLeft();
                    final long validEnd = validPeriod.getRight();
                    return isTimeInRange(originalCheckInTime, originalCheckOutTime, validStart, validEnd);
                }).collect(Collectors.toList());
    }

    private boolean checkIfDateType(Long workDate, Integer dateType, WfmWorkingHourAnalyzeInfo analyzeInfo, Integer targetDateType) {
        Integer specialDateType = analyzeInfo.getSpecialDateType(workDate, dateType);
        return Optional.ofNullable(specialDateType).isPresent() && specialDateType.equals(targetDateType);
    }

    private Pair<Long, Long> calculateValidPeriod(WfmProcessWorkHourDto process, EffectiveTimeDto effectiveTime, long defaultStart, long defaultEnd) {
        // 默认值：如果没有工序或有效时间，使用原始打卡时间
        MutableLong validStart = new MutableLong(defaultStart);
        MutableLong validEnd = new MutableLong(defaultEnd);
        // 优先处理工序时间
        if (process != null) {
            final long processStart = process.getProcessStartTime();
            final long processEnd = process.getProcessEndTime();
            validStart.setValue(processStart);
            if (processEnd > 0) { // 有效工序时间
                validEnd.setValue(processEnd);
                // 跨天时应用有效扫码结束时间
                if (checkDifferentTimestampCrossDayGreaterThan(processStart, processEnd)) {
                    applyEffectiveTimeAdjustment(effectiveTime, null, validEnd);
                }
            } else { // 无效工序时间，回退到有效扫码时间
                applyEffectiveTimeAdjustment(effectiveTime, null, validEnd);
            }
        } else { // 无工序信息，完全依赖有效扫码时间
            applyEffectiveTimeAdjustment(effectiveTime, validStart, validEnd);
        }
        return Pair.of(validStart.getValue(), validEnd.getValue());
    }

    private boolean checkDifferentTimestampCrossDayGreaterThan(long start, long end) {
        LocalDate startDate = Instant.ofEpochSecond(start).atZone(DEFAULT_ZONE).toLocalDate();
        LocalDate endDate = Instant.ofEpochSecond(end).atZone(DEFAULT_ZONE).toLocalDate();
        long daysBetween = Math.abs(ChronoUnit.DAYS.between(startDate, endDate));
        return daysBetween > 1;
    }

    private boolean checkDifferentTimestampCrossDay(long start, long end) {
        LocalDate startDate = Instant.ofEpochSecond(start).atZone(DEFAULT_ZONE).toLocalDate();
        LocalDate endDate = Instant.ofEpochSecond(end).atZone(DEFAULT_ZONE).toLocalDate();
        return !startDate.isEqual(endDate);
    }

    /**
     * 应用有效时间调整
     *
     * @param dto
     * @param adjustedStart
     * @param adjustedEnd
     */
    private void applyEffectiveTimeAdjustment(EffectiveTimeDto dto, MutableLong adjustedStart, MutableLong adjustedEnd) {
        if (dto == null) {
            return;
        }
        if (null != adjustedStart && dto.getEffectiveStartTime() != null) {
            adjustedStart.setValue(Long.parseLong(dto.getEffectiveStartTime()) / 1000);
        }
        if (null != adjustedEnd && dto.getEffectiveEndTime() != null) {
            adjustedEnd.setValue(Long.parseLong(dto.getEffectiveEndTime()) / 1000);
        }
    }

    /**
     * 判断时间是否在区间内
     *
     * @param start
     * @param end
     * @param processStart
     * @param processEnd
     * @return
     */
    private boolean isTimeInRange(long start, long end, long processStart, long processEnd) {
        return start < processEnd && end > processStart;
    }

    public static class MutableLong {
        long value;

        MutableLong(long value) {
            this.value = value;
        }

        void setValue(long value) {
            this.value = value;
        }

        long getValue() {
            return value;
        }
    }

    /**
     * 获取扫码开始时间
     *
     * @param checkInRegDateTime 扫码开始时间
     * @param processWorkingHour 工序开始结束时间
     * @param effectiveTimeDto   有效扫码开始结束时间
     * @return
     */
    private Long getCheckInRegDateTime(Long checkInRegDateTime, WfmProcessWorkHourDto processWorkingHour, EffectiveTimeDto effectiveTimeDto) {
        // 1. 处理早到情况（无论是否迟到都优先处理）
        if (processWorkingHour != null && processWorkingHour.getProcessStartTime() > 0) {
            // 有工序开始时间：早到则使用工序开始时间
            if (checkInRegDateTime < processWorkingHour.getProcessStartTime()) {
                checkInRegDateTime = processWorkingHour.getProcessStartTime();
            }
        } else if (effectiveTimeDto != null && effectiveTimeDto.getEffectiveStartTime() != null) {
            // 无工序开始时间但有效扫码时间：早到则使用有效开始时间
            long effectiveStartTime = Long.parseLong(effectiveTimeDto.getEffectiveStartTime()) / 1000;
            if (checkInRegDateTime < effectiveStartTime) {
                checkInRegDateTime = effectiveStartTime;
            }
        }
        // 2. 处理迟到豁免逻辑（15分钟宽容期）
        if (processWorkingHour != null) {
            long processStartTime = processWorkingHour.getProcessStartTime();
            long processEndTime = processWorkingHour.getProcessEndTime();
            if (processEndTime > 0) {
                // 计算工序开始和结束时间之间的天数差
                //Long days = DateUtils.getBetween(new Date(processStartTime * 1000), new Date(processEndTime * 1000));
                if (checkDifferentTimestampCrossDayGreaterThan(processStartTime, processEndTime)) {
                    // 跨天工序 - 使用有效扫码时间处理迟到
                    if (effectiveTimeDto != null && effectiveTimeDto.getEffectiveStartTime() != null) {
                        long effectiveStartTime = Long.parseLong(effectiveTimeDto.getEffectiveStartTime()) / 1000;
                        if (calculateTimeDifferenceLate(checkInRegDateTime, effectiveStartTime, 15) <= 0) {
                            checkInRegDateTime = effectiveStartTime;
                        }
                    }
                } else {
                    // 单天工序 - 使用工序开始时间处理迟到
                    if (calculateTimeDifferenceLate(checkInRegDateTime, processStartTime, 15) <= 0) {
                        checkInRegDateTime = processStartTime;
                    }
                }
            } else if (processStartTime > 0) {
                // 无工序结束时间 - 使用工序开始时间处理迟到
                if (calculateTimeDifferenceLate(checkInRegDateTime, processStartTime, 15) <= 0) {
                    checkInRegDateTime = processStartTime;
                }
            }
        } else if (effectiveTimeDto != null && effectiveTimeDto.getEffectiveStartTime() != null) {
            // 无工序信息 - 使用有效扫码时间处理迟到
            long effectiveStartTime = Long.parseLong(effectiveTimeDto.getEffectiveStartTime()) / 1000;
            if (calculateTimeDifferenceLate(checkInRegDateTime, effectiveStartTime, 15) <= 0) {
                checkInRegDateTime = effectiveStartTime;
            }
        }
        return checkInRegDateTime;
    }

    /**
     * 获取扫码结束时间
     *
     * @param checkOutRegDateTime 扫码结束时间
     * @param processWorkingHour  工序开始结束时间
     * @param effectiveTimeDto    有效扫码开始结束时间
     * @return
     */
    private Long getCheckOutRegDateTime(Long checkOutRegDateTime, WfmProcessWorkHourDto processWorkingHour, EffectiveTimeDto effectiveTimeDto) {
        // 1. 处理晚走情况（无论是否早退都优先处理）
        if (processWorkingHour != null && processWorkingHour.getProcessEndTime() > 0) {
            // 有工序结束时间：晚走则使用工序结束时间
            if (checkOutRegDateTime > processWorkingHour.getProcessEndTime()) {
                checkOutRegDateTime = processWorkingHour.getProcessEndTime();
            }
        } else if (effectiveTimeDto != null && effectiveTimeDto.getEffectiveEndTime() != null) {
            // 无工序结束时间但有效扫码时间：晚走则使用有效结束时间
            long effectiveEndTime = Long.parseLong(effectiveTimeDto.getEffectiveEndTime()) / 1000;
            if (checkOutRegDateTime > effectiveEndTime) {
                checkOutRegDateTime = effectiveEndTime;
            }
        }
        // 2. 处理早退豁免逻辑（15分钟宽容期）
        if (processWorkingHour != null) {
            long processStartTime = processWorkingHour.getProcessStartTime();
            long processEndTime = processWorkingHour.getProcessEndTime();
            if (processEndTime > 0) {
                // 计算工序开始和结束时间之间的天数差
                // Long days = DateUtils.getBetween(new Date(processStartTime * 1000), new Date(processEndTime * 1000));
                if (checkDifferentTimestampCrossDayGreaterThan(processStartTime, processEndTime)) {
                    // 跨天工序 - 使用有效扫码时间处理早退
                    if (effectiveTimeDto != null && effectiveTimeDto.getEffectiveEndTime() != null) {
                        long effectiveEndTime = Long.parseLong(effectiveTimeDto.getEffectiveEndTime()) / 1000;
                        if (calculateTimeDifferenceEarly(checkOutRegDateTime, effectiveEndTime, 15) >= 0) {
                            checkOutRegDateTime = effectiveEndTime;
                        }
                    }
                } else {
                    // 单天工序 - 使用工序结束时间处理早退
                    if (calculateTimeDifferenceEarly(checkOutRegDateTime, processEndTime, 15) >= 0) {
                        checkOutRegDateTime = processEndTime;
                    }
                }
            } else if (effectiveTimeDto != null && effectiveTimeDto.getEffectiveEndTime() != null) {
                // 无工序结束时间 - 使用有效结束时间处理早退
                long effectiveEndTime = Long.parseLong(effectiveTimeDto.getEffectiveEndTime()) / 1000;
                if (calculateTimeDifferenceEarly(checkOutRegDateTime, effectiveEndTime, 15) >= 0) {
                    checkOutRegDateTime = effectiveEndTime;
                }
            }
        } else if (effectiveTimeDto != null && effectiveTimeDto.getEffectiveEndTime() != null) {
            long effectiveEndTime = Long.parseLong(effectiveTimeDto.getEffectiveEndTime()) / 1000;
            if (calculateTimeDifferenceEarly(checkOutRegDateTime, effectiveEndTime, 15) >= 0) {
                checkOutRegDateTime = effectiveEndTime;
            }
        }
        return checkOutRegDateTime;
    }

    /**
     * 计算时间差（考虑分钟偏移）
     *
     * @param time1   时间1（秒级时间戳）
     * @param time2   时间2（秒级时间戳）
     * @param minutes 偏移分钟数
     * @return 计算后的时间差（秒）
     */
    private long calculateTimeDifferenceLate(long time1, long time2, int minutes) {
        return time1 - time2 - minutes * 60L;
    }

    /**
     * 计算时间差（考虑分钟偏移）
     *
     * @param time1   时间1（秒级时间戳）
     * @param time2   时间2（秒级时间戳）
     * @param minutes 偏移分钟数
     * @return 计算后的时间差（秒）
     */
    private long calculateTimeDifferenceEarly(long time1, long time2, int minutes) {
        return time1 - time2 + minutes * 60L;
    }

    private WfmScanCheckInDto getRegDateRecord(String checkType, EmpMultiShiftInfoDto empShift, WfmWorkingHourAnalyzeInfo analyzeInfo) {
        List<WfmScanCheckInDto> regList = getRegDateRecords(empShift, analyzeInfo);
        if (CollectionUtils.isEmpty(regList)) {
            return null;
        }
        boolean startCheckIn = "startCheckIn".equals(checkType);
        if (startCheckIn) {
            return regList.stream().filter(record -> checkType.equals(record.getCheckInType())).min(Comparator.comparing(WfmScanCheckInDto::getRegDateTime)).orElse(null);
        } else {
            return regList.stream().filter(record -> checkType.equals(record.getCheckInType())).max(Comparator.comparing(WfmScanCheckInDto::getRegDateTime)).orElse(null);
        }
    }

    /**
     * 获取原始排序后的打卡记录（私有方法）
     */
    private List<WfmScanCheckInDto> getRegDateRecords(EmpMultiShiftInfoDto empShift, WfmWorkingHourAnalyzeInfo analyzeInfo) {
        return Optional.ofNullable(analyzeInfo.getEmpDayRegisterRecord(empShift.getEmpId(), empShift.getWorkDate(), empShift.getShiftDefId(), empShift.getLeafNumberId(), empShift.getProcessId(), null))
                .orElseGet(Collections::emptyList).stream().filter(Objects::nonNull).sorted(Comparator.comparing(WfmScanCheckInDto::getRegDateTime))
                .collect(Collectors.toCollection(ArrayList::new));
    }

    /**
     * 获取严格配对的签到签退记录（仅返回完整的签到-签退对，忽略任何未配对的记录）
     *
     * @return 成对的签到签退记录列表，不完整的记录将被忽略
     */
    public List<Pair<WfmScanCheckInDto, WfmScanCheckInDto>> getStrictCheckInOutPairs(EmpMultiShiftInfoDto empShift, WfmWorkingHourAnalyzeInfo analyzeInfo) {
        // 获取按时间排序的原始记录
        List<WfmScanCheckInDto> sortedRecords = getRegDateRecords(empShift, analyzeInfo);
        List<Pair<WfmScanCheckInDto, WfmScanCheckInDto>> pairs = new ArrayList<>();
        WfmScanCheckInDto checkIn = null;
        for (WfmScanCheckInDto record : sortedRecords) {
            if ("startCheckIn".equals(record.getCheckInType())) {
                if (checkIn != null) {
                    pairs.add(Pair.of(checkIn, null));
                }
                checkIn = record;
            } else if ("endCheckIn".equals(record.getCheckInType()) && checkIn != null) {
                pairs.add(Pair.of(checkIn, record));
                checkIn = null;
            }
        }
        if (checkIn != null) {
            pairs.add(Pair.of(checkIn, null));
        }
        return pairs;
    }

    private String convertPairsToJson(List<Pair<WfmScanCheckInDto, WfmScanCheckInDto>> pairs) {
        List<RegDateTimePair> jsonList = pairs.stream().map(pair -> new RegDateTimePair(pair.getLeft(), pair.getRight())).collect(Collectors.toList());
        return JSON.toJSONString(jsonList);
    }

    /**
     * 计算实际工时
     *
     * @param regStartTime 扫码开始时间
     * @param regEndTime   扫码结束时间
     * @param empShift     班次
     * @return BigDecimal
     */
    private BigDecimal calActualWorkTime(Long regStartTime, Long regEndTime, EmpMultiShiftInfoDto empShift) {
        if (null == regStartTime || null == regEndTime || regEndTime <= regStartTime) {
            return BigDecimal.ZERO;
        }
        // 将秒转换为分钟
        return calculateOverlapDurationInMinutes(regStartTime, regEndTime, new DateTimeItemDto(regStartTime, regEndTime), getRestPeriods(empShift), true);
    }

    /**
     * 计算实际工时，与班次/有效时间的交集
     *
     * @param regStartTime       扫码开始时间
     * @param regEndTime         扫码结束时间
     * @param empShift           排班
     * @param effectiveTimeDto   工序有效扫码时间
     * @param processWorkingHour 工序开始结束时间
     * @return 实际工时
     */
    private BigDecimal calEffectiveWorkTime(Long regStartTime, Long regEndTime, EmpMultiShiftInfoDto empShift, EffectiveTimeDto effectiveTimeDto, WfmProcessWorkHourDto processWorkingHour) {
        if (null == effectiveTimeDto) {
            return BigDecimal.ZERO;
        }
        long effectiveStartTime = Long.parseLong(effectiveTimeDto.getEffectiveStartTime()) / 1000;
        long effectiveEndTime = Long.parseLong(effectiveTimeDto.getEffectiveEndTime()) / 1000;
        AtomicReference<DateTimeItemDto> overlapAtomic = new AtomicReference<>();
        overlapAtomic.set(new DateTimeItemDto(effectiveStartTime, effectiveEndTime));
        Optional.ofNullable(processWorkingHour).ifPresent(process -> {
            if (process.getProcessEndTime() > 0) {
                overlapAtomic.set(getOverlap(effectiveStartTime, effectiveEndTime, new DateTimeItemDto(process.getProcessStartTime(), process.getProcessEndTime())));
            }
        });
        return calculateOverlapDurationInMinutes(regStartTime, regEndTime, overlapAtomic.get(), getRestPeriods(empShift), true);
    }

    /**
     * 多段班
     *
     * @param empShift 班次
     * @return 班次时间段
     */
    private List<DateTimeItemDto> getDateTimeItemList(EmpMultiShiftInfoDto empShift) {
        Long workDate = empShift.getWorkDate();
        List<DateTimeItemDto> list = Lists.newArrayList();
        List<MultiWorkTimeInfoSimpleVo> vos = empShift.getOriMultiWorkTimes();
        if (CollectionUtils.isEmpty(vos)) {
            return list;
        }
        for (MultiWorkTimeInfoSimpleVo vo : vos) {
            if (vo.getWorkType() != 2) {
                continue;
            }
            list.add(new DateTimeItemDto(workDate + vo.doGetRealStartTime() * 60, workDate + vo.doGetRealEndTime() * 60));
        }
        return list;
    }

    /**
     * 计算 regStartTime 和 regEndTime 与 validPeriod 的重叠部分，扣除休息时间段restPeriods，并计算有效时长（单位为分钟）
     *
     * @param regStartTime  开始时间（秒级时间戳）
     * @param regEndTime    结束时间（秒级时间戳）
     * @param validPeriod   有效时间段
     * @param restPeriods   休息时间段
     * @param isConvertUnit 是否进行分钟转换
     * @return 有效时长（单位：分钟）
     */
    public BigDecimal calculateOverlapDurationInMinutes(long regStartTime, long regEndTime, DateTimeItemDto validPeriod, List<DateTimeItemDto> restPeriods, boolean isConvertUnit) {
        if (regStartTime >= regEndTime || validPeriod == null) {
            return BigDecimal.ZERO;
        }
        // 1. 计算签到时间段与有效时间范围的交集
        DateTimeItemDto actualValidPeriod = getOverlap(regStartTime, regEndTime, validPeriod);
        if (actualValidPeriod == null) {
            return BigDecimal.ZERO;
        }
        // 2. 计算休息时间段与实际有效时间段的交集
        List<DateTimeItemDto> restInValidPeriod = getOverlappingIntervals(actualValidPeriod.getStart(), actualValidPeriod.getEnd(), restPeriods);
        // 3. 合并休息时间段
        List<DateTimeItemDto> mergedRest = mergeIntervals(restInValidPeriod);
        // 4. 从有效时间段中扣除休息时间
        List<DateTimeItemDto> finalIntervals = subtractIntervals(Collections.singletonList(actualValidPeriod), mergedRest);
        // 5. 计算总时长（分钟）
        long totalSeconds = 0;
        for (DateTimeItemDto interval : finalIntervals) {
            totalSeconds += (interval.getEnd() - interval.getStart());
        }
        if (isConvertUnit) {
            return BigDecimal.valueOf(totalSeconds).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
        } else {
            return BigDecimal.valueOf(totalSeconds);
        }
    }

    // 计算两个时间段的交集
    private DateTimeItemDto getOverlap(long startA, long endA, DateTimeItemDto periodB) {
        long overlapStart = Math.max(startA, periodB.getStart());
        long overlapEnd = Math.min(endA, periodB.getEnd());

        if (overlapStart >= overlapEnd) {
            return null; // 无交集
        }
        return new DateTimeItemDto(overlapStart, overlapEnd);
    }

    // 获取与时间段重叠的区间（已截断）
    private List<DateTimeItemDto> getOverlappingIntervals(long start, long end, List<DateTimeItemDto> periods) {
        List<DateTimeItemDto> overlaps = new ArrayList<>();
        if (periods == null) return overlaps;

        for (DateTimeItemDto period : periods) {
            if (period.getEnd() > start && period.getStart() < end) {
                long overlapStart = Math.max(period.getStart(), start);
                long overlapEnd = Math.min(period.getEnd(), end);
                overlaps.add(new DateTimeItemDto(overlapStart, overlapEnd));
            }
        }
        return overlaps;
    }

    // 合并重叠区间
    private List<DateTimeItemDto> mergeIntervals(List<DateTimeItemDto> intervals) {
        if (intervals.isEmpty()) return intervals;

        intervals.sort(Comparator.comparingLong(DateTimeItemDto::getStart));
        List<DateTimeItemDto> merged = new ArrayList<>();
        DateTimeItemDto current = intervals.get(0);

        for (int i = 1; i < intervals.size(); i++) {
            DateTimeItemDto next = intervals.get(i);
            if (next.getStart() <= current.getEnd()) {
                current = new DateTimeItemDto(current.getStart(), Math.max(current.getEnd(), next.getEnd()));
            } else {
                merged.add(current);
                current = next;
            }
        }
        merged.add(current);
        return merged;
    }

    // 从有效时间中扣除休息时间
    private List<DateTimeItemDto> subtractIntervals(List<DateTimeItemDto> valid, List<DateTimeItemDto> rest) {
        List<DateTimeItemDto> result = new ArrayList<>(valid);
        for (DateTimeItemDto restInterval : rest) {
            List<DateTimeItemDto> temp = new ArrayList<>();
            for (DateTimeItemDto validInterval : result) {
                // 无重叠：直接保留有效区间
                if (validInterval.getEnd() <= restInterval.getStart() ||
                        validInterval.getStart() >= restInterval.getEnd()) {
                    temp.add(validInterval);
                }
                // 有重叠：切割有效区间
                else {
                    // 左边剩余部分（休息区间左侧）
                    if (validInterval.getStart() < restInterval.getStart()) {
                        temp.add(new DateTimeItemDto(validInterval.getStart(), restInterval.getStart()));
                    }
                    // 右边剩余部分（休息区间右侧）
                    if (validInterval.getEnd() > restInterval.getEnd()) {
                        temp.add(new DateTimeItemDto(restInterval.getEnd(), validInterval.getEnd()));
                    }
                }
            }
            result = temp;
        }
        return result;
    }

    /**
     * 异常工时json
     *
     * @param list 异常申请
     * @return String
     */
    private String getEmpAbnormalWorkingHourContent(List<EmpAbnormalWorkingHourDto> list) {
        List<AbnormalDto> abnormalDtoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, List<EmpAbnormalWorkingHourDto>> abnormalDayMap = list.stream().collect(Collectors.groupingBy(EmpAbnormalWorkingHourDto::getAbnormalType));
            for (Map.Entry<String, List<EmpAbnormalWorkingHourDto>> entry : abnormalDayMap.entrySet()) {
                List<EmpAbnormalWorkingHourDto> items = entry.getValue();
                EmpAbnormalWorkingHourDto dto = items.get(0);
                BigDecimal hourlyUnitPrice = BigDecimal.ZERO;
                if (validateHourlyUnitPrice(dto.getHourlyUnitPrice())) {
                    hourlyUnitPrice = new BigDecimal(dto.getHourlyUnitPrice());
                }
                abnormalDtoList.add(new AbnormalDto(getEmpAbnormalWorkingHour(items), entry.getKey(), dto.getAbnormalTypeName(),
                        Long.valueOf(dto.getDate()), dto.getNotes(), hourlyUnitPrice));
            }
        }
        return JSON.toJSONString(abnormalDtoList);
    }

    public boolean validateHourlyUnitPrice(String hourlyUnitPrice) {
        if (hourlyUnitPrice == null) {
            return false;
        }
        String regex = "^(\\d+(\\.\\d+)?)$";
        return hourlyUnitPrice.matches(regex);
    }

    /**
     * 计算工时
     *
     * @param list 异常申请
     * @return BigDecimal
     */
    private BigDecimal getEmpAbnormalWorkingHour(List<EmpAbnormalWorkingHourDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return BigDecimal.ZERO;
        }
        return list.stream().map(EmpAbnormalWorkingHourDto::getAbnormalTime).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 工时分析基础数据
     *
     * @param tenantId  租户
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return WfmWorkingHourAnalyzeInfo
     */
    private WfmWorkingHourAnalyzeInfo getAnalyzeInfo(String tenantId, Long startDate, Long endDate) {
        WfmWorkingHourAnalyzeInfo analyzeInfo = new WfmWorkingHourAnalyzeInfo();
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            // 查询特殊日期（法定假日）
            List<WfmCalendarDateDto> specialDateList = wfmHolidayService.getCalendarDateList(tenantId, startDate, endDate + 86400);
            Map<Long, Integer> specialDateMap = specialDateList.stream().filter(specialDate -> null != specialDate.getDateType() && specialDate.getDateType() == 3).collect(Collectors.toMap(WfmCalendarDateDto::getCalendarDate, WfmCalendarDateDto::getDateType));
            analyzeInfo.setSpecialCalendarDateMap(specialDateMap);
            analyzeInfo.setShiftChangeOldShiftIdMap(wfmShiftService.getShiftChangeOldShiftIdMap(tenantId, startDate, endDate));
        } finally {
            UserContext.removeSecurityUserInfo();
        }
        return analyzeInfo;
    }

    private Map<String, AverageAttendanceTimeDto> getEmpAverageWorkingHourMap(String tenantId, Long startDate, Long endDate, List<Long> orderIds) {
        try {
            DataFilter dataFilter = DataFilter.eq("tenantId", tenantId)
                    .andIn("status", Arrays.asList("2", "10"))//审批通过
                    .andGe("schedulingTime", startDate.toString()) //大于等于
                    .andLe("schedulingTime", endDate.toString())   //小于等于
                    .andNe("deleted", Boolean.TRUE.toString());
            if (CollectionUtils.isNotEmpty(orderIds)) {
                dataFilter = dataFilter.andIn("orderId", orderIds.stream().map(String::valueOf).collect(Collectors.toList())); //订单
            }
            PageResult<AverageAttendanceTimeDto> pageResult = DataQuery.identifier(WFM_AVERAGE_WORKING_HOUR_IDENTIFIER).decrypt().dept()
                    .specifyLanguage().queryInvisible().limit(-1, 1).exp()
                    .filter(dataFilter, AverageAttendanceTimeDto.class, System.currentTimeMillis());
            if (CollectionUtils.isEmpty(pageResult.getItems())) {
                return Collections.emptyMap();
            }
            log.info("getEmpAverageWorkingHourMap : {}", FastjsonUtil.toJsonStr(pageResult.getItems()));
            return pageResult.getItems().stream().filter(Objects::nonNull)
                    .collect(Collectors.toMap(item -> {
                        Long workDate = Long.parseLong(item.getSchedulingTime()) / 1000;
                        return String.format("%s_%s_%s_%s_%s", item.getEmpId(), workDate, item.getProcessId(), item.getOrderId(), item.getShiftId());
                    }, Function.identity(), (existing, replacement) -> existing, () -> new HashMap<>(Math.max((int) (pageResult.getItems().size() / 0.75f) + 1, 16))));
        } catch (Exception e) {
            log.error("Query {} failed for tenantId: {}, processIds: {}", WFM_AVERAGE_WORKING_HOUR_IDENTIFIER, tenantId, e);
            return Collections.emptyMap();
        }
    }

    private Map<Long, WfmProcessInfo> getWorkingProcessMap(String tenantId, List<Long> processIds) {
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            DataFilter processFilter = DataFilter.eq("tenantId", tenantId);
            if (CollectionUtils.isNotEmpty(processIds)) {
                processFilter = processFilter.andIn("bid", processIds.stream().map(String::valueOf).collect(Collectors.toList()));
            }
            PageResult<WfmProcessInfo> pageResult = queryProcesses(processFilter, 1);
            List<WfmProcessInfo> workingProcessList = new ArrayList<>(pageResult.getItems());
            int totalCount = pageResult.getTotal();
            if (totalCount > MAX_PAGE_SIZE) {
                int totalPages = (totalCount + MAX_PAGE_SIZE - 1) / MAX_PAGE_SIZE;
                for (int pageNo = 2; pageNo <= totalPages; pageNo++) {
                    workingProcessList.addAll(queryProcesses(processFilter, pageNo).getItems());
                }
            }
            return workingProcessList.stream().filter(Objects::nonNull)
                    .collect(Collectors.toMap(info -> Long.parseLong(info.getBid()), Function.identity(),
                            (existing, replacement) -> existing, () -> new HashMap<>(Math.max((int) (workingProcessList.size() / 0.75f) + 1, 16))));
        } catch (Exception e) {
            log.error("Query {} failed for tenantId: {}, processIds: {}", WORK_PROCESS_IDENTIFIER, tenantId, processIds, e);
            return Collections.emptyMap();
        } finally {
            UserContext.removeSecurityUserInfo();
        }
    }

    private PageResult<WfmProcessInfo> queryProcesses(DataFilter filter, int pageNo) {
        return DataQuery.identifier(WORK_PROCESS_IDENTIFIER)
                .decrypt()
                .dept()
                .specifyLanguage()
                .queryInvisible()
                .limit(MAX_PAGE_SIZE, pageNo)
                .exp()
                .filter(filter, WfmProcessInfo.class, System.currentTimeMillis());
    }

    private List<WfmScanCheckInDto> getEmpRegisterRecordList(String tenantId, Long startDate, Long endDate, List<Long> empIds, List<Long> orderIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return Collections.emptyList();
        }
        List<WfmScanCheckInDto> list = Lists.newArrayList();
        List<List<Long>> empIdList = ListTool.split(empIds, 500);
        for (List<Long> rows : empIdList) {
            if (CollectionUtils.isEmpty(rows)) {
                continue;
            }
            list.addAll(getEmpRegisterRecords(tenantId, startDate, endDate, rows, orderIds));
        }
        return list;
    }

    private List<WfmScanCheckInDto> getEmpRegisterRecords(String tenantId, Long startDate, Long endDate, List<Long> empIds, List<Long> orderIds) {
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId) //租户
                .andIn("checkInReissueStatus", Arrays.asList("notReissue", "approved"))
                .andGe("scheduleDate", startDate.toString()) //大于等于
                .andLe("scheduleDate", endDate.toString()) //小于等于
                .andNe("deleted", Boolean.TRUE.toString());
        if (CollectionUtils.isNotEmpty(empIds)) {
            dataFilter = dataFilter.andIn("employeeId", empIds.stream().map(String::valueOf).collect(Collectors.toList())); //员工
        }
        if (CollectionUtils.isNotEmpty(orderIds)) {
            dataFilter = dataFilter.andIn("actualOrderId", orderIds.stream().map(String::valueOf).collect(Collectors.toList())); //订单
        }
        List<WfmScanCheckInDto> items = Lists.newArrayList();
        try {
            List<WfmScanCheckIn> list = DataQuery.identifier(WFM_REGISTER_RECORD_IDENTIFIER)
                    .decrypt().specifyLanguage().queryInvisible().limit(-1, 1)
                    .filter(dataFilter, WfmScanCheckIn.class).getItems();
            if (CollectionUtils.isNotEmpty(list)) {
                for (WfmScanCheckIn row : list) {
                    WfmScanCheckInDto dto = ObjectConverter.convert(row, WfmScanCheckInDto.class);
                    dto.setRecordId(Long.valueOf(row.getBid()));
                    dto.setEmpId(Long.valueOf(row.getEmployeeId()));
                    dto.setShiftId(Long.valueOf(row.getActualShiftId()));
                    dto.setOrderId(Long.valueOf(row.getActualOrderId()));
                    dto.setProcessId(Long.valueOf(row.getActualProcessId()));
                    dto.setWorkDate(Long.parseLong(row.getScheduleDate()) / 1000);
                    dto.setRegDateTime(Long.parseLong(row.getCheckInTime()) / 1000);
                    items.add(dto);
                }
            }
        } catch (Exception e) {
            log.error("getEmpRegisterRecords exception:{}", e.getMessage(), e);
        }
        return items;
    }

    private List<WfmProcessWorkHourDto> getConfirmProcessCompletions(String tenantId, Long startDate, Long endDate, List<Long> orderIds) {
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId) //租户
                .andGe("schedulingTime", startDate.toString()) //大于等于
                .andLe("schedulingTime", endDate.toString()) //小于等于
                .andNe("deleted", Boolean.TRUE.toString());
        if (CollectionUtils.isNotEmpty(orderIds)) {
            dataFilter = dataFilter.andIn("orderId", orderIds.stream().map(String::valueOf).collect(Collectors.toList())); //订单
        }
        return getConfirmProcessCompletions(dataFilter);
    }

    private List<WfmProcessWorkHourDto> getConfirmProcessCompletions(DataFilter dataFilter) {
        List<WfmProcessWorkHourDto> items = Lists.newArrayList();
        try {
            List<ConfirmProcessCompletionDto> list = DataQuery.identifier(WFM_CONFIRM_PROCESS_COMPLETION).decrypt().specifyLanguage().queryInvisible()
                    .limit(-1, 1)
                    .filter(dataFilter, ConfirmProcessCompletionDto.class).getItems();
            if (CollectionUtils.isNotEmpty(list)) {
                for (ConfirmProcessCompletionDto row : list) {
                    WfmProcessWorkHourDto dto = ObjectConverter.convert(row, WfmProcessWorkHourDto.class);
                    dto.setShiftId(Long.valueOf(row.getShiftId()));
                    dto.setOrderId(Long.valueOf(row.getOrderId()));
                    dto.setProcessId(Long.valueOf(row.getProcessId()));
                    dto.setWorkDate(Long.parseLong(row.getSchedulingTime()) / 1000);
                    dto.setProcessStartTime(dto.getProcessStartTime() / 1000);
                    dto.setProcessEndTime(dto.getProcessEndTime() / 1000);
                    long processWorkingHours = dto.getProcessWorkingHours();
                    if (dto.getProcessStartTime() < dto.getProcessEndTime()) {
                        processWorkingHours = dto.getProcessEndTime() - dto.getProcessStartTime();
                    }
                    dto.setProcessWorkingHours(processWorkingHours);
                    items.add(dto);
                }
            }
        } catch (Exception e) {
            log.error("getConfirmProcessCompletions exception:{}", e.getMessage(), e);
        }
        return items;
    }

    /**
     * 查询有效扫码时间
     *
     * @param tenantId  租户
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return List
     */
    private List<EffectiveTimeDto> getEffectiveTimeDtoList(String tenantId, Long startDate, Long endDate, List<Long> orderIds) {
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId) //租户
                .andGe("schedulingTime", startDate.toString()) //大于等于
                .andLe("schedulingTime", endDate.toString()) //小于等于
                .andNe("deleted", Boolean.TRUE.toString());
        if (CollectionUtils.isNotEmpty(orderIds)) {
            dataFilter = dataFilter.andIn("orderId", orderIds.stream().map(String::valueOf).collect(Collectors.toList()));
        }
        List<EffectiveTimeDto> items = Lists.newArrayList();
        try {
            items = DataQuery.identifier(WFM_PROCESS_EFFECTIVE_TIME_IDENTIFIER).decrypt().specifyLanguage().queryInvisible()
                    .limit(-1, 1)
                    .filter(dataFilter, EffectiveTimeDto.class).getItems();
        } catch (Exception e) {
            log.error("getEmpAbnormalWorkingHour exception:{}", e.getMessage(), e);
        }
        return items;
    }

    /**
     * 异常工时
     *
     * @param tenantId  租户
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param empIds    员工
     * @return List
     */
    private List<EmpAbnormalWorkingHourDto> getEmpAbnormalWorkingHours(String tenantId, Long startDate, Long endDate, List<Long> empIds, List<Long> orderIds, boolean nonProcess) {
        if (CollectionUtils.isEmpty(empIds)) {
            return Collections.emptyList();
        }
        List<EmpAbnormalWorkingHourDto> empAbnormalList = Lists.newArrayList();
        List<List<Long>> empIdList = ListTool.split(empIds, 500);
        for (List<Long> rows : empIdList) {
            if (CollectionUtils.isEmpty(rows)) {
                continue;
            }
            empAbnormalList.addAll(getEmpAbnormalWorkingHour(tenantId, startDate * 1000, endDate * 1000, rows, orderIds, nonProcess));
        }
        if (CollectionUtils.isEmpty(empAbnormalList)) {
            return Collections.emptyList();
        }
        List<String> recordIds = empAbnormalList.stream().map(EmpAbnormalWorkingHourDto::getAbnormalWorkHoursRecordId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, AbnormalWorkingHoursRecordDto> abnormalRecordMap = getEmpAbnormalWorkingHourRecord(tenantId, recordIds);
        Map<String, String> abnormalTypeMap = getAbnormalTypeMap();
        for (EmpAbnormalWorkingHourDto empAbnormal : empAbnormalList) {
            if (abnormalRecordMap.containsKey(empAbnormal.getAbnormalWorkHoursRecordId())) {
                empAbnormal.setNotes(abnormalRecordMap.get(empAbnormal.getAbnormalWorkHoursRecordId()).getNotes());
                empAbnormal.setHourlyUnitPrice(abnormalRecordMap.get(empAbnormal.getAbnormalWorkHoursRecordId()).getHourlyUnitPrice());
            }
            if (StringUtils.isNotBlank(empAbnormal.getAbnormalType())) {
                String[] arr = empAbnormal.getAbnormalType().split(",");
                String abnormalType = arr[arr.length - 1];
                empAbnormal.setAbnormalTypeName(abnormalTypeMap.get(abnormalType));
            }
            empAbnormal.setDate(String.valueOf(Long.parseLong(empAbnormal.getDate()) / 1000));
        }
        return empAbnormalList;
    }

    /**
     * 异常类型
     *
     * @return Map
     */
    private Map<String, String> getAbnormalTypeMap() {
        // 获取异常类型
        try {
            Result<List<WfmKeyValuePair>> result = wfmFeignClient.getAbnormalWorkHoursTypeKeyValueList();
            if (null == result || result.getCode() != 0 || !result.isSuccess()) {
                return new HashMap<>();
            }
            List<WfmKeyValuePair> items = result.getData();
            if (CollectionUtils.isEmpty(items)) {
                return new HashMap<>();
            }
            return items.stream().collect(Collectors.toMap(WfmKeyValuePair::getKey, WfmKeyValuePair::getValue, (v1, v2) -> v2));
        } catch (Exception e) {
            log.error("getAbnormalTypeMap exception:{}", e.getMessage(), e);
        }
        return new HashMap<>();
    }

    /**
     * 异常工时申请员工详情
     *
     * @param tenantId  租户
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param empIds    员工
     * @return List
     */
    private List<EmpAbnormalWorkingHourDto> getEmpAbnormalWorkingHour(String tenantId, Long startDate, Long endDate,
                                                                      List<Long> empIds, List<Long> orderIds, boolean nonProcess) {
        List<EmpAbnormalWorkingHourDto> items = Lists.newArrayList();
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId) //租户
                .andGe("date", startDate.toString()) //大于等于
                .andLe("date", endDate.toString()) //小于等于
                .andEq("status", "2") //审批通过
                .andNe("deleted", Boolean.TRUE.toString());
        if (nonProcess) {
            dataFilter = dataFilter.andEq("processId", null);
        }
        if (CollectionUtils.isNotEmpty(empIds)) {
            dataFilter = dataFilter.andIn("employeeId", empIds.stream().map(String::valueOf).collect(Collectors.toList())); //员工
        }
        if (CollectionUtils.isNotEmpty(orderIds)) {
            dataFilter = dataFilter.andIn("orderId", orderIds.stream().map(String::valueOf).collect(Collectors.toList())); //订单
        }
        try {
            items = DataQuery.identifier(WFM_EMP_ABNORMAL_WORKING_HOUR_IDENTIFIER).decrypt().specifyLanguage().queryInvisible()
                    .limit(-1, 1)
                    .filter(dataFilter, EmpAbnormalWorkingHourDto.class).getItems();
        } catch (Exception e) {
            log.error("getEmpAbnormalWorkingHour exception:{}", e.getMessage(), e);
        }
        return items;
    }

    /**
     * 异常工时申请
     *
     * @param tenantId  租户
     * @param recordIds 流程主键
     * @return 异常工时申请Map
     */
    private Map<String, AbnormalWorkingHoursRecordDto> getEmpAbnormalWorkingHourRecord(String tenantId, List<String> recordIds) {
        if (CollectionUtils.isEmpty(recordIds)) {
            return new HashMap<>(1);
        }
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId) //租户
                .andEq("status", "2") //审批通过
                .andIn("bid", recordIds) //异常工时申请
                .andNe("deleted", Boolean.TRUE.toString());
        List<AbnormalWorkingHoursRecordDto> items = DataQuery.identifier(WFM_ABNORMAL_WORKING_HOUR_IDENTIFIER).decrypt().specifyLanguage().queryInvisible()
                .limit(-1, 1)
                .filter(dataFilter, AbnormalWorkingHoursRecordDto.class).getItems();
        return items.stream().collect(Collectors.toMap(AbnormalWorkingHoursRecordDto::getBid, Function.identity(), (v1, v2) -> v2));
    }

    /**
     * 每日统计
     *
     * @param dto      参数
     * @param userInfo 用户
     * @return 每日统计
     */
    @Override
    public AttendancePageResult<WorkingHourAnalyzeDto> getDayAnalyseList(WfmDayAnalysePageDto dto, UserInfo userInfo) {
        PageBean pageBean = PageUtil.getNewPageBean(dto);
        Long startDate = Optional.ofNullable(dto.getStartDate()).orElse(DateUtil.getOnlyDate());
        Long endDate = Optional.ofNullable(dto.getEndDate()).orElse(startDate);
        String tenantId;
        if (userInfo != null) {
            tenantId = userInfo.getTenantId();
        } else {
            tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        }
        String sortString = pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(sortString));
        List<Long> empIds = cacheCommonService.getShiftGroupDataScopeEmpList(WFM_WORKING_HOUR_DAY_DATA_SCOPE_IDENTIFIER, startDate, endDate, dto.getKeywords(), null, userInfo);
        log.info("======= getShiftGroupDataScopeEmpList.  => {}", FastjsonUtil.toJsonStr(empIds));
        // 过滤基地权限： 列表字段产品>产品管理页面所属团队>组织
        // 取交集
        log.info("======= start to get emp data scope.  =>");
        List<Long> authedProductEmps = orgDataScopeService.getOrgScopeEmpIds(OrgDataScopeEnum.WFM_WORKING_HOUR_DAY, startDate, endDate, dto.getKeywords(), userInfo);
        log.info("======= end to get emp data scope.  => {}", FastjsonUtil.toJsonStr(authedProductEmps));
        if (CollectionUtils.isEmpty(authedProductEmps)) {
            return new AttendancePageResult<>(new ArrayList<>(), dto.getPageNo(), dto.getPageSize(), 0);
        }
        // 取交集：empIds ∩ authedProductEmps
        empIds = empIds.stream().filter(authedProductEmps::contains).collect(Collectors.toList());
        // 如果有选人（前端传入 empIds），再继续与之取交集
        if (CollectionUtils.isNotEmpty(dto.getEmpIds())) {
            empIds = empIds.stream().filter(dto.getEmpIds()::contains).collect(Collectors.toList());
        }
        if (empIds.isEmpty())
            return new AttendancePageResult<>(new PageList<>(), dto.getPageNo(), dto.getPageSize(), 0);
        String filter = pageBean.getFilter();
        List<Long> productIds = null;
        if (CollectionUtils.isNotEmpty(dto.getProductIds())) {
            productIds = dto.getProductIds().stream().map(Long::parseLong).collect(Collectors.toList());
        }
        PageList<WorkingHourAnalyzeDo> list = workingHourAnalyzeDo.getDayAnalyseList(tenantId, startDate, endDate, dto.getKeywords(), empIds, filter,
                dto.getDataScope(), pageBounds, productIds, dto.getProcessTypes(), dto.getFilterName());
        if (CollectionUtils.isEmpty(list)) {
            return new AttendancePageResult<>(new ArrayList<>(), dto.getPageNo(), dto.getPageSize(), 0);
        }
        List<Long> shiftIds = list.stream().map(WorkingHourAnalyzeDo::getShiftId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, MultiShiftSimpleVo> shiftMap = getShiftMap(tenantId, shiftIds);
        List<Long> processIds = list.stream().map(WorkingHourAnalyzeDo::getProcessId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, WfmProcessInfo> processInfoMap = getWorkingProcessMap(tenantId, processIds);
        List<WorkingHourAnalyzeDto> items = Lists.newArrayList();
        for (WorkingHourAnalyzeDo hourAnalyze : list) {
            WorkingHourAnalyzeDto item = ObjectConverter.convert(hourAnalyze, WorkingHourAnalyzeDto.class);
            item.setWorkTime(hourAnalyze.getWorkTime() == null ? BigDecimal.ZERO : BigDecimal.valueOf(hourAnalyze.getWorkTime()));
            item.convertTime();
            if (shiftMap.containsKey(hourAnalyze.getShiftId())) {
                MultiShiftSimpleVo shift = shiftMap.get(hourAnalyze.getShiftId());
                item.setShiftName(shift.getShiftDefName());
                item.setShiftTimes(shift.getMultiWorkTimes());
            }
            Optional.ofNullable(processInfoMap.get(hourAnalyze.getProcessId())).ifPresent(processInfo -> {
                // 处理工段
                item.setWorkshopSectionName(Optional.ofNullable(processInfo.getWorkshopSection()).map(DictSimple::getText).orElse(""));
                // 工序类型
                item.setWfmTypeName(Optional.ofNullable(processInfo.getWfmType()).map(DictSimple::getText).orElse(""));
            });
            item.setRegDateTime(FastjsonUtil.toList(hourAnalyze.getRegDateTime(), RegDateTimePair.class));
            Optional.ofNullable(hourAnalyze.getEffectiveScanTime()).ifPresent(time -> {
                List<RegDateRangeTimePair> timePairs = FastjsonUtil.toList(time, RegDateRangeTimePair.class);
                if (CollectionUtils.isNotEmpty(timePairs)) {
                    item.setEffectiveScanTime(timePairs.stream().map(timePair -> String.format("%s~%s",
                                    DateUtil.getTimeStrByTimesamp3(timePair.getStartTime()), DateUtil.getTimeStrByTimesamp3(timePair.getEndTime())))
                            .collect(Collectors.joining(",")));
                } else {
                    item.setEffectiveScanTime("");
                }
            });
            Optional.ofNullable(hourAnalyze.getProcessTime()).ifPresent(time -> {
                List<RegDateRangeTimePair> timePairs = FastjsonUtil.toList(time, RegDateRangeTimePair.class);
                if (CollectionUtils.isNotEmpty(timePairs)) {
                    item.setProcessTime(timePairs.stream().map(timePair -> {
                        String endTimeMin = timePair.getEndTime() > 0 ? DateUtil.getTimeStrByTimesamp3(timePair.getEndTime()) : "-";
                        return String.format("%s~%s", DateUtil.getTimeStrByTimesamp3(timePair.getStartTime()), endTimeMin);
                    }).collect(Collectors.joining(",")));
                } else {
                    item.setProcessTime("");
                }
            });
            items.add(item);
        }
        return new AttendancePageResult<>(items, dto.getPageNo(), dto.getPageSize(), list.getPaginator().getTotalCount());
    }

    /**
     * 查询订单
     *
     * @param tenantId 租户
     * @param orderIds 订单
     * @return 订单
     */
    private List<WfmOrderInfo> getWfmOrders(String tenantId, List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return new ArrayList<>();
        }
        List<WfmOrderInfo> orders = new ArrayList<>(orderIds.size());
        List<List<Long>> orderIdList = ListTool.split(orderIds, 500);
        for (List<Long> ids : orderIdList) {
            if (CollectionUtils.isEmpty(ids)) {
                continue;
            }
            orders.addAll(getWfmOrdersByCondition(tenantId, DataFilter.eq("tenantId", tenantId).andIn("bid", ids.stream().map(String::valueOf).collect(Collectors.toList()))
                    .andNe("deleted", Boolean.TRUE.toString())));
        }
        return orders;
    }

    private List<WfmOrderInfo> getWfmOrdersByCondition(String tenantId, DataFilter dataFilter) {
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            return DataQuery.identifier(WFM_ORDER_IDENTIFIER).decrypt().specifyLanguage()
                    .queryInvisible().limit(-1, 1).filter(dataFilter, WfmOrderInfo.class).getItems();
        } finally {
            UserContext.removeSecurityUserInfo();
        }
    }

    /**
     * 查排班
     *
     * @param shiftIds 排班
     * @return Map
     */
    private Map<Long, MultiShiftSimpleVo> getShiftMap(String tenantId, List<Long> shiftIds) {
        if (CollectionUtils.isEmpty(shiftIds)) {
            return new HashMap<>();
        }
        List<List<Long>> shiftIdList = ListTool.split(shiftIds, 500);
        List<MultiShiftSimpleVo> shifts = Lists.newArrayList();
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            for (List<Long> rows : shiftIdList) {
                if (CollectionUtils.isEmpty(rows)) {
                    continue;
                }
                shifts.addAll(empScheduleService.getMultiShiftInfos(shiftIds));
            }
        } catch (Exception e) {
            return new HashMap<>();
        } finally {
            UserContext.removeSecurityUserInfo();
        }
        return shifts.stream().collect(Collectors.toMap(MultiShiftSimpleVo::getShiftDefId, Function.identity(), (v1, v2) -> v2));
    }

    /**
     * 月度汇总
     *
     * @param dto      请求参数
     * @param userInfo 当前用户
     * @return 月度汇总
     */
    @Override
    public AttendancePageResult<WorkingHourMonthAnalyzeDto> getMonthAnalyseList(WfmMonthAnalysePageDto dto, UserInfo userInfo) {
        PageBean pageBean = PageUtil.getNewPageBean(dto);
        String tenantId = userInfo != null ? userInfo.getTenantId() : SecurityUserUtil.getSecurityUserInfo().getTenantId();
        Long startDate = Optional.ofNullable(dto.getStartDate()).orElse(DateUtil.getOnlyDate());
        Long endDate = Optional.ofNullable(dto.getEndDate()).orElse(startDate);
        String filter = pageBean.getFilter();
        List<Long> empIds = cacheCommonService.getShiftGroupDataScopeEmpList(WFM_WORKING_HOUR_MONTH_DATA_SCOPE_IDENTIFIER, startDate, endDate, dto.getKeywords(), null, userInfo);
        // 过滤基地权限： 列表字段产品>产品管理页面所属团队>组织
        // 取交集
        List<Long> authedProductEmps = orgDataScopeService.getOrgScopeEmpIds(OrgDataScopeEnum.WFM_WORKING_HOUR_MONTH, startDate, endDate, dto.getKeywords(), userInfo);
        if (CollectionUtils.isEmpty(authedProductEmps)) {
            return new AttendancePageResult<>(new ArrayList<>(), dto.getPageNo(), dto.getPageSize(), 0);
        }
        empIds = empIds.stream().filter(authedProductEmps::contains).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(dto.getEmpIds())) {
            empIds = empIds.stream().filter(dto.getEmpIds()::contains).collect(Collectors.toList());
        }
        if (empIds.isEmpty())
            return new AttendancePageResult<>(new ArrayList<>(), dto.getPageNo(), dto.getPageSize(), 0);
        List<Long> productIds = null;
        if (CollectionUtils.isNotEmpty(dto.getProductIds())) {
            productIds = dto.getProductIds().stream().map(Long::parseLong).collect(Collectors.toList());
        }
        String sortString = pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(sortString));
        PageList<Map> pageList = workingHourAnalyzeDo.getDayAnalyseListOrderByOrder(tenantId, startDate, endDate, dto.getKeywords(), empIds, filter,
                dto.getDataScope(), pageBounds, productIds, dto.getProcessTypes(), dto.getFilterName());
        if (CollectionUtils.isEmpty(pageList)) {
            return new AttendancePageResult<>(new ArrayList<>(), dto.getPageNo(), dto.getPageSize(), 0);
        }
        List<WorkingHourAnalyzeDo> list = getWorkingHourAnalyzeList(tenantId, startDate, endDate, pageList, false, false);
        Map<String, List<WorkingHourAnalyzeDo>> monthAnalyzeMap = list.stream().collect(Collectors.groupingBy(row -> String.format("%s_%s_%s", row.getEmpId(), row.getProductId(), row.getProcessId())));
        List<Long> shiftIds = list.stream().map(WorkingHourAnalyzeDo::getShiftId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, MultiShiftSimpleVo> shiftMap = getShiftMap(tenantId, shiftIds);
        List<WfmOrderInfo> orders = getWfmOrders(tenantId, extractOrderIds(list));
        Map<Long, Integer> specialDateMap = getSpecialDateMap(tenantId, startDate, endDate);
        Map<String, BigDecimal> inventoryQuantityMap = new HashMap<>(pageList.size());
        Map<String, List<WfmOrderInfo>> inventoryOrderMap = new HashMap<>(pageList.size());
        summarizeEligibleOrders(tenantId, startDate, endDate, pageList, null, inventoryQuantityMap, inventoryOrderMap, true, false);
        Map<String, Integer> completedQuantityMap = new HashMap<>(pageList.size());
        Map<String, BigDecimal> actualCompletedQuantityMap = new HashMap<>(pageList.size());
        Map<String, List<WfmOrderInfo>> completedOrderMap = new HashMap<>(pageList.size());
        summarizeEligibleOrders(tenantId, startDate, endDate, pageList, completedQuantityMap, actualCompletedQuantityMap, completedOrderMap, false, true);
        for (Map map : pageList) {
            map.putAll(getMonthStatisticMap(startDate, endDate, map, monthAnalyzeMap, shiftMap, orders, specialDateMap));
            if (map.containsKey("actualWorkTime") && null != map.get("actualWorkTime")) {
                BigDecimal actualWorkTime = (BigDecimal) map.get("actualWorkTime");
                map.put("actualWorkTime", actualWorkTime.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toPlainString());
            }
            if (map.containsKey("holidayActualWorkTime") && null != map.get("holidayActualWorkTime")) {
                BigDecimal holidayActualWorkTime = (BigDecimal) map.get("holidayActualWorkTime");
                map.put("holidayActualWorkTime", holidayActualWorkTime.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toPlainString());
            }
            String mapKey = getMapKey(map);
            map.put("inventoryQuantity", inventoryQuantityMap.getOrDefault(mapKey, BigDecimal.ZERO));
            map.put("inventoryOrders", inventoryOrderMap.getOrDefault(mapKey, Lists.newArrayList()));
            map.put("completedQuantity", completedQuantityMap.getOrDefault(mapKey, 0));
            map.put("actualCompletedQuantity", actualCompletedQuantityMap.getOrDefault(mapKey, BigDecimal.ZERO));
            map.put("completedOrders", completedOrderMap.getOrDefault(mapKey, Lists.newArrayList()));
        }
        return new AttendancePageResult<>(FastjsonUtil.convertList(pageList, WorkingHourMonthAnalyzeDto.class), dto.getPageNo(), dto.getPageSize(), pageList.getPaginator().getTotalCount());
    }

    /**
     * 工时统计-计薪工序汇总-分页列表查询
     *
     * @param queryDto
     * @param userInfo
     * @return
     */
    @Override
    public PageResult<ProcessStatisticsPageResultDto> getProcessStatisticsPageResult(ProcessStatisticsPageQueryDto queryDto, UserInfo userInfo) {
        // 1. 参数预处理
        preprocessQueryDto(queryDto);
        String tenantId = getTenantId(userInfo);

        // 2. 数据权限检查
        List<Long> authorizedProductIds = getAuthorizedProductIds(queryDto, userInfo);
        if (authorizedProductIds != null && authorizedProductIds.isEmpty()) {
            return createEmptyPageResult(queryDto);
        }

        // 3. 查询基础工序数据
        PageResult<WorkingHourAnalyzeDo> processStatisticsPageResult =
                workingHourAnalyzeDo.getProcessStatisticsPageResult(queryDto, tenantId, authorizedProductIds);

        if (isEmptyResult(processStatisticsPageResult)) {
            return createEmptyPageResult(queryDto);
        }

        // 4. 转换和统计
        List<ProcessStatisticsPageResultDto> processStatisticsDtos =
                ObjectConverter.convertList(processStatisticsPageResult.getItems(), ProcessStatisticsPageResultDto.class);

        // 5. 统计完成和入库数量
        Map<String, Map<String, Integer>> processStatisticsMap =
                calculateProcessStatistics(queryDto, processStatisticsDtos, tenantId);

        // 6. 填充统计数据
        fillStatisticsData(processStatisticsDtos, processStatisticsMap);

        return new PageResult<>(processStatisticsDtos, processStatisticsPageResult.getPageNo(),
                processStatisticsPageResult.getPageSize(), processStatisticsPageResult.getTotal());
    }

    /**
     * 预处理查询参数
     */
    private void preprocessQueryDto(ProcessStatisticsPageQueryDto queryDto) {
        if (queryDto.getEndDate() != null) {
            queryDto.setEndDate(DateUtil.getOnlyDate(new Date(queryDto.getEndDate() * 1000)) + 86399);
        }
    }

    /**
     * 获取租户ID
     */
    private String getTenantId(UserInfo userInfo) {
        return userInfo != null ? userInfo.getTenantId() : SecurityUserUtil.getSecurityUserInfo().getTenantId();
    }

    /**
     * 获取授权的产品ID列表
     */
    private List<Long> getAuthorizedProductIds(ProcessStatisticsPageQueryDto queryDto, UserInfo userInfo) {
        if (queryDto.isIgnoreDataScope()) {
            return null;
        }

        OrgScopeProductResult orgScopeProductResult =
                orgDataScopeService.getOrgScopeProduct(OrgDataScopeEnum.WFM_WORKING_HOUR_STATISTICS_PROCESS, userInfo);

        if (!orgScopeProductResult.isIfConfigurePermissions()) {
            return null;
        }

        if (CollectionUtils.isEmpty(orgScopeProductResult.getAuthorizedProductIds())) {
            return new ArrayList<>();
        }

        return orgScopeProductResult.getAuthorizedProductIds().stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    /**
     * 创建空的分页结果
     */
    private PageResult<ProcessStatisticsPageResultDto> createEmptyPageResult(ProcessStatisticsPageQueryDto queryDto) {
        return new PageResult<>(new ArrayList<>(), queryDto.getPageNo(), queryDto.getPageSize(), 0);
    }

    /**
     * 判断是否为空结果
     */
    private boolean isEmptyResult(PageResult<WorkingHourAnalyzeDo> result) {
        return result == null || CollectionUtils.isEmpty(result.getItems());
    }

    /**
     * 计算工序统计数据
     */
    private Map<String, Map<String, Integer>> calculateProcessStatistics(
            ProcessStatisticsPageQueryDto queryDto,
            List<ProcessStatisticsPageResultDto> processStatisticsDtos,
            String tenantId) {

        // 提取产品ID和工序ID
        List<Long> filterProductIds = extractProductIds(processStatisticsDtos);
        List<Long> filterProcessIds = extractProcessIds(processStatisticsDtos);

        // 构建订单统计查询参数
        ProcessOrderStatisticsPageQueryDto orderQueryDto = buildOrderStatisticsQuery(queryDto, filterProductIds, filterProcessIds);

        // 获取完成和入库统计数据
        Map<String, List<WorkingHourAnalyzeDo>> completedQuantityMap = getCompletedQuantityMap(orderQueryDto, tenantId);
        Map<String, List<WorkingHourAnalyzeDo>> storedQuantityMap = getStoredQuantityMap(orderQueryDto, tenantId);

        // 计算统计结果
        return mergeStatisticsData(completedQuantityMap, storedQuantityMap);
    }

    /**
     * 提取产品ID列表
     */
    private List<Long> extractProductIds(List<ProcessStatisticsPageResultDto> processStatisticsDtos) {
        return processStatisticsDtos.stream()
                .map(ProcessStatisticsPageResultDto::getProductId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 提取工序ID列表
     */
    private List<Long> extractProcessIds(List<ProcessStatisticsPageResultDto> processStatisticsDtos) {
        return processStatisticsDtos.stream()
                .map(ProcessStatisticsPageResultDto::getProcessId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 构建订单统计查询参数
     */
    private ProcessOrderStatisticsPageQueryDto buildOrderStatisticsQuery(
            ProcessStatisticsPageQueryDto queryDto,
            List<Long> productIds,
            List<Long> processIds) {

        ProcessOrderStatisticsPageQueryDto orderQueryDto = new ProcessOrderStatisticsPageQueryDto();
        orderQueryDto.setPageNo(1);
        orderQueryDto.setPageSize(Integer.MAX_VALUE);
        orderQueryDto.setStartDate(queryDto.getStartDate());
        orderQueryDto.setEndDate(queryDto.getEndDate());
        orderQueryDto.setProductIds(productIds);
        orderQueryDto.setProcessIds(processIds);
        return orderQueryDto;
    }

    /**
     * 获取完成数量统计Map
     */
    private Map<String, List<WorkingHourAnalyzeDo>> getCompletedQuantityMap(
            ProcessOrderStatisticsPageQueryDto orderQueryDto, String tenantId) {

        PageResult<WorkingHourAnalyzeDo> completedQuantityResult =
                workingHourAnalyzeDo.getCompletedStatisticsPage(orderQueryDto, tenantId);

        if (isEmptyResult(completedQuantityResult)) {
            return new HashMap<>();
        }

        return completedQuantityResult.getItems().stream()
                .collect(Collectors.groupingBy(this::generateStatisticsKey));
    }

    /**
     * 获取入库数量统计Map
     */
    private Map<String, List<WorkingHourAnalyzeDo>> getStoredQuantityMap(
            ProcessOrderStatisticsPageQueryDto orderQueryDto, String tenantId) {

        PageResult<WorkingHourAnalyzeDo> storedQuantityResult =
                workingHourAnalyzeDo.getStoredStatisticsPage(orderQueryDto, tenantId);

        if (isEmptyResult(storedQuantityResult)) {
            return new HashMap<>();
        }

        return storedQuantityResult.getItems().stream()
                .collect(Collectors.groupingBy(this::generateStatisticsKey));
    }

    /**
     * 生成统计键
     */
    private String generateStatisticsKey(WorkingHourAnalyzeDo item) {
        return String.format("%s_%s_%s", item.getProductId(), item.getProcessId(), item.getOrderId());
    }

    /**
     * 合并统计数据
     */
    private Map<String, Map<String, Integer>> mergeStatisticsData(
            Map<String, List<WorkingHourAnalyzeDo>> completedQuantityMap,
            Map<String, List<WorkingHourAnalyzeDo>> storedQuantityMap) {

        Map<String, Map<String, Integer>> processStatisticsMap = new HashMap<>();

        // 处理完成数量数据
        processCompletedQuantityData(completedQuantityMap, storedQuantityMap, processStatisticsMap);

        // 处理剩余的入库数量数据
        processRemainingStoredQuantityData(storedQuantityMap, processStatisticsMap);

        return processStatisticsMap;
    }

    /**
     * 处理完成数量数据
     */
    private void processCompletedQuantityData(
            Map<String, List<WorkingHourAnalyzeDo>> completedQuantityMap,
            Map<String, List<WorkingHourAnalyzeDo>> storedQuantityMap,
            Map<String, Map<String, Integer>> processStatisticsMap) {

        for (Map.Entry<String, List<WorkingHourAnalyzeDo>> entry : completedQuantityMap.entrySet()) {
            String mapKey = entry.getKey();
            String processKey = extractProcessKey(mapKey);

            // 计算完成数量
            int totalCompletedQuantity = calculateTotalCompletedQuantity(entry.getValue());

            // 计算入库数量
            int totalStoredQuantity = 0;
            if (storedQuantityMap.containsKey(mapKey)) {
                totalStoredQuantity = calculateTotalStoredQuantity(storedQuantityMap.get(mapKey));
                storedQuantityMap.remove(mapKey); // 移除已处理的数据
            }

            // 更新统计数据
            updateProcessStatistics(processStatisticsMap, processKey, totalCompletedQuantity, totalStoredQuantity);
        }
    }

    /**
     * 处理剩余的入库数量数据
     */
    private void processRemainingStoredQuantityData(
            Map<String, List<WorkingHourAnalyzeDo>> storedQuantityMap,
            Map<String, Map<String, Integer>> processStatisticsMap) {

        for (Map.Entry<String, List<WorkingHourAnalyzeDo>> entry : storedQuantityMap.entrySet()) {
            String mapKey = entry.getKey();
            String processKey = extractProcessKey(mapKey);

            // 入库数量
            int totalStoredQuantity = calculateTotalStoredQuantity(entry.getValue());

            // 更新统计数据
            updateProcessStatistics(processStatisticsMap, processKey, 0, totalStoredQuantity);
        }
    }

    /**
     * 从mapKey中提取processKey
     */
    private String extractProcessKey(String mapKey) {
        String[] keyParts = mapKey.split("_");
        return String.format("%s_%s", keyParts[0], keyParts[1]);
    }

    /**
     * 计算总完成数量
     */
    private int calculateTotalCompletedQuantity(List<WorkingHourAnalyzeDo> items) {
        return items.stream()
                .mapToInt(item -> Optional.ofNullable(item.getCompletedQuantity()).orElse(0))
                .sum();
    }

    /**
     * 计算总入库数量
     */
    private int calculateTotalStoredQuantity(List<WorkingHourAnalyzeDo> items) {
        return items.stream()
                .mapToInt(item -> Optional.ofNullable(item.getStoredQuantity()).orElse(0))
                .sum();
    }

    /**
     * 更新工序统计数据
     */
    private void updateProcessStatistics(
            Map<String, Map<String, Integer>> processStatisticsMap,
            String processKey,
            int completedQuantity,
            int storedQuantity) {

        Map<String, Integer> statistics = processStatisticsMap.computeIfAbsent(processKey, k -> {
            Map<String, Integer> newStats = new HashMap<>();
            newStats.put("completedQuantity", 0);
            newStats.put("storedQuantity", 0);
            return newStats;
        });

        statistics.put("completedQuantity", statistics.get("completedQuantity") + completedQuantity);
        statistics.put("storedQuantity", statistics.get("storedQuantity") + storedQuantity);
    }

    /**
     * 填充统计数据到DTO
     */
    private void fillStatisticsData(
            List<ProcessStatisticsPageResultDto> processStatisticsDtos,
            Map<String, Map<String, Integer>> processStatisticsMap) {

        processStatisticsDtos.forEach(dto -> {
            String processKey = String.format("%s_%s", dto.getProductId(), dto.getProcessId());
            Map<String, Integer> statistics = processStatisticsMap.get(processKey);

            if (statistics != null) {
                dto.setCompletedQuantity(statistics.get("completedQuantity"));
                dto.setStoredQuantity(statistics.get("storedQuantity"));
            }
        });
    }

    private void summarizeEligibleOrders(String tenantId, Long startDate, Long endDate, List<Map> pageList,
                                         Map<String, Integer> quantityMap, Map<String, BigDecimal> actualQuantityMap,
                                         Map<String, List<WfmOrderInfo>> orderMap,
                                         Boolean summaryStored, Boolean summaryCompleted) {
        if (CollectionUtils.isEmpty(pageList)) {
            return;
        }
        List<WorkingHourAnalyzeDo> list = getWorkingHourAnalyzeList(tenantId, startDate, endDate, pageList, summaryStored, summaryCompleted);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<WfmOrderInfo> orders = getWfmOrders(tenantId, extractOrderIds(list));
        Map<String, List<WorkingHourAnalyzeDo>> monthAnalyzeMap = list.stream().collect(Collectors.groupingBy(row -> String.format("%s_%s_%s", row.getEmpId(), row.getProductId(), row.getProcessId())));
        for (Map map : pageList) {
            String mapKey = getMapKey(map);
            Integer quantity = 0;
            BigDecimal actualQuantity = BigDecimal.ZERO;
            List<WfmOrderInfo> filterOrders = Lists.newArrayList();
            if (monthAnalyzeMap.containsKey(mapKey)) {
                List<WorkingHourAnalyzeDo> empMonth = monthAnalyzeMap.get(mapKey);
                List<Long> filterOrderIds = empMonth.stream().map(WorkingHourAnalyzeDo::getOrderId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                if (summaryCompleted) {
                    if (null != quantityMap) {
                        //quantity = empMonth.stream().map(WorkingHourAnalyzeDo::getCompletedQuantity).filter(Objects::nonNull).reduce(0, Integer::sum);
                        List<Long> finalFilterOrderIds1 = filterOrderIds;
                        quantity = orders.stream().filter(order -> finalFilterOrderIds1.contains(Long.parseLong(order.getBid()))).map(WfmOrderInfo::getQuantity).filter(Objects::nonNull).reduce(0, Integer::sum);
                    }
                }
                if (null != actualQuantityMap) {
                    actualQuantity = empMonth.stream().map(WorkingHourAnalyzeDo::getActualCompletedQuantity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                if (null != orderMap) {
                    if (summaryStored) {
                        filterOrderIds = empMonth.stream().filter(day -> day.getActualCompletedQuantity() != null && BigDecimal.ZERO.compareTo(day.getActualCompletedQuantity()) < 0).map(WorkingHourAnalyzeDo::getOrderId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                    }
                    List<Long> finalFilterOrderIds = filterOrderIds;
                    filterOrders = orders.stream().filter(order -> finalFilterOrderIds.contains(Long.parseLong(order.getBid()))).collect(Collectors.toList());
                }
            }
            if (null != orderMap) {
                orderMap.put(mapKey, filterOrders);
            }
            if (null != quantityMap) {
                quantityMap.put(mapKey, quantity);
            }
            if (null != actualQuantityMap) {
                actualQuantityMap.put(mapKey, actualQuantity);
            }
        }
    }

    private List<WfmProcessWorkHourDto> getProcessWorkHours(String tenantId, Long startDate, Long endDate, List<Long> orderIds, List<Long> shiftIds, List<Long> processIds) {
        endDate += 86399000;
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId) //租户
                .andGe("processEndTime", startDate.toString()) //大于等于
                .andLe("processEndTime", endDate.toString()) //小于等于
                .andNe("deleted", Boolean.TRUE.toString());
        if (CollectionUtils.isNotEmpty(orderIds)) {
            dataFilter = dataFilter.andIn("orderId", orderIds.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(shiftIds)) {
            dataFilter = dataFilter.andIn("shiftId", shiftIds.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(processIds)) {
            dataFilter = dataFilter.andIn("processId", processIds.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList()));
        }
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            return getConfirmProcessCompletions(dataFilter);
        } catch (Exception e) {
            log.error("getProcessWorkHours exception:{}", e.getMessage(), e);
        } finally {
            UserContext.removeSecurityUserInfo();
        }
        return Lists.newArrayList();
    }

    private Map getMonthStatisticMap(Long startDate, Long endDate, Map map, Map<String, List<WorkingHourAnalyzeDo>> monthAnalyzeMap, Map<Long, MultiShiftSimpleVo> shiftMap, List<WfmOrderInfo> orders, Map<Long, Integer> specialDateMap) {
        Map monthStatisticMap = new HashMap();
        String mapKey = getMapKey(map);
        if (!monthAnalyzeMap.containsKey(mapKey)) {
            return monthStatisticMap;
        }
        List<WorkingHourAnalyzeDo> empMonth = monthAnalyzeMap.get(mapKey);
        monthStatisticMap.putAll(handleMonthAnalyze(startDate, endDate, empMonth, shiftMap, orders, specialDateMap));
        return monthStatisticMap;
    }

    private String getMapKey(Map map) {
        return String.format("%s_%s_%s", map.get("empId"), map.get("productId"), map.get("processId"));
    }

    private Map<Long, Integer> getSpecialDateMap(String tenantId, Long startDate, Long endDate) {
        Map<Long, Integer> specialDateMap;
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            // 查询特殊日期（法定假日）
            List<WfmCalendarDateDto> specialDateList = wfmHolidayService.getCalendarDateList(tenantId, startDate, endDate);
            specialDateMap = specialDateList.stream().filter(specialDate -> null != specialDate.getDateType() && specialDate.getDateType() == 3).collect(Collectors.toMap(WfmCalendarDateDto::getCalendarDate, WfmCalendarDateDto::getDateType));
        } finally {
            UserContext.removeSecurityUserInfo();
        }
        return specialDateMap;
    }

    private Map handleMonthAnalyze(Long startDate, Long endDate, List<WorkingHourAnalyzeDo> monthData, Map<Long, MultiShiftSimpleVo> shiftMap, List<WfmOrderInfo> orders, Map<Long, Integer> specialDateMap) {
        Map map = new HashMap<>();
        if (CollectionUtils.isEmpty(monthData)) {
            return map;
        }
        int workTime = 0;//排班工时（不包括法定假日）
        int holidayWorkTime = 0;//法定假日排班工时
        BigDecimal abnormalWorkTime = BigDecimal.ZERO;
        List<AbnormalDto> abnormalDtoList = Lists.newArrayList();
        List<Long> oderIds = Lists.newArrayList();
        List<Long> processIds = Lists.newArrayList();
        List<Long> shiftIds = Lists.newArrayList();
        BigDecimal effectWorkTime = BigDecimal.ZERO;//有效工时（不包括法定假日）
        BigDecimal holidayEffectWorkTime = BigDecimal.ZERO;//法定假日有效工时
        BigDecimal actualWorkTime = BigDecimal.ZERO;//实际工时
        BigDecimal processWorkTime = BigDecimal.ZERO;//工序工时
        BigDecimal workDayActualWorkTime = BigDecimal.ZERO;//工作日实际工时
        BigDecimal workDayOtDuration = BigDecimal.ZERO;//工作日加班时长
        BigDecimal restDayOtDuration = BigDecimal.ZERO;//休息日加班时长
        //完成数量
        Integer completedQuantity = 0;
        //实际完成数量
        BigDecimal actualCompletedQuantity = BigDecimal.ZERO;
        for (WorkingHourAnalyzeDo row : monthData) {
            if (!processIds.contains(row.getProcessId())) {
                processIds.add(row.getProcessId());
            }
            if (!shiftIds.contains(row.getShiftId())) {
                shiftIds.add(row.getShiftId());
            }
            if (!oderIds.contains(row.getOrderId())) {
                oderIds.add(row.getOrderId());
            }
            actualWorkTime = actualWorkTime.add(Optional.ofNullable(row.getActualWorkTime()).orElse(BigDecimal.ZERO));
            processWorkTime = processWorkTime.add(Optional.ofNullable(row.getProcessWorkTime()).orElse(BigDecimal.ZERO));
            MultiShiftSimpleVo shiftSimpleVo = shiftMap.get(row.getShiftId());
            if (Optional.ofNullable(shiftSimpleVo).isPresent()) {
                Integer dateType = Optional.ofNullable(specialDateMap.get(row.getBelongDate())).orElse(shiftSimpleVo.getDateType());
                if (dateType == 3) {
                    holidayWorkTime += shiftSimpleVo.getWorkTotalTime();
                    holidayEffectWorkTime = holidayEffectWorkTime.add(Optional.ofNullable(row.getEffectWorkTime()).orElse(BigDecimal.ZERO));
                }
                workTime += shiftSimpleVo.getWorkTotalTime();
                effectWorkTime = effectWorkTime.add(Optional.ofNullable(row.getEffectWorkTime()).orElse(BigDecimal.ZERO));
            }
            abnormalWorkTime = abnormalWorkTime.add(Optional.ofNullable(row.getAbnormalWorkTime()).orElse(BigDecimal.ZERO));
            abnormalDtoList.addAll(getAbnormalList(row.getAbnormalContent()));
            if (null != row.getCompletedTime() && row.getCompletedTime() >= startDate && row.getCompletedTime() <= endDate + 86399) {
                BigDecimal rowActualCompletedQuantity = Optional.ofNullable(row.getActualCompletedQuantity()).orElse(BigDecimal.ZERO);
                actualCompletedQuantity = actualCompletedQuantity.add(rowActualCompletedQuantity);
                completedQuantity += Optional.ofNullable(row.getCompletedQuantity()).orElse(0);
            }
            workDayActualWorkTime = workDayActualWorkTime.add(Optional.ofNullable(row.getWorkDayActualWorkTime()).orElse(BigDecimal.ZERO));
            //工作日加班时长
            workDayOtDuration = workDayOtDuration.add(Optional.ofNullable(row.getWorkDayOtDuration()).orElse(BigDecimal.ZERO));
            //休息日加班时长
            restDayOtDuration = restDayOtDuration.add(Optional.ofNullable(row.getRestDayOtDuration()).orElse(BigDecimal.ZERO));
        }
        //排班工时
        map.put("workTime", BigDecimal.valueOf(workTime).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toPlainString());
        //工作日实际工时
        map.put("workDayActualWorkTime", workDayActualWorkTime.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toPlainString());
        //法定假日排班工时
        map.put("holidayWorkTime", BigDecimal.valueOf(holidayWorkTime).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toPlainString());
        //有效工时
        map.put("effectWorkTime", effectWorkTime.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toPlainString());
        //法定假日有效工时
        map.put("holidayEffectWorkTime", holidayEffectWorkTime.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toPlainString());
        List<WfmOrderInfo> empOrders = orders.stream().filter(order -> oderIds.contains(Long.valueOf(order.getBid()))).collect(Collectors.toList());
        List<WfmOrderInfo> completedOrders = empOrders.stream()
                .filter(order -> "2".equals(order.getCompletionType())
                        && null != order.getActualCompletionTime()
                        && order.getActualCompletionTime() >= startDate
                        && order.getActualCompletionTime() <= endDate + 86399)
                .collect(Collectors.toList());
        //Integer completedQuantity = completedOrders.stream().map(WfmOrderInfo::getQuantity).filter(Objects::nonNull).reduce(0, Integer::sum);
        //完成数量
        map.put("completedQuantity", completedQuantity);
        //实际完成数量
        map.put("actualCompletedQuantity", actualCompletedQuantity.toPlainString());
        // 工序工时
        map.put("processWorkTime", processWorkTime.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toPlainString());
        // 完成订单
        map.put("completedOrders", completedOrders);
        List<WfmOrderInfo> storageOrders = empOrders.stream().filter(order -> "1".equals(order.getStorageType())).collect(Collectors.toList());
        // 入库数量
        map.put("inventoryQuantity", storageOrders.stream().map(WfmOrderInfo::getQuantity).filter(Objects::nonNull).reduce(0, Integer::sum));
        // 入库订单
        map.put("inventoryOrders", storageOrders);
        // 异常工时
        map.put("abnormalWorkTime", abnormalWorkTime.toPlainString());
        BigDecimal abnormalWorkTimeWage = BigDecimal.ZERO;
        for (AbnormalDto abnormalDto : abnormalDtoList) {
            BigDecimal perAbnormalWorkTime = Optional.ofNullable(abnormalDto.getAbnormalWorkTime()).orElse(BigDecimal.ZERO);
            BigDecimal perHourlyUnitPrice = Optional.ofNullable(abnormalDto.getHourlyUnitPrice()).orElse(BigDecimal.ZERO);
            abnormalWorkTimeWage = abnormalWorkTimeWage.add(perAbnormalWorkTime.multiply(perHourlyUnitPrice));
        }
        //异常工时工资
        map.put("abnormalWorkTimeWage", abnormalWorkTimeWage.toPlainString());
        //异常工时数据
        map.put("abnormalWorkTimes", abnormalDtoList);
        //工作日加班时长
        map.put("workDayOtDuration", workDayOtDuration.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toPlainString());
        //休息日加班时长
        map.put("restDayOtDuration", restDayOtDuration.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toPlainString());
        return map;
    }

    private List<AbnormalDto> getAbnormalList(String abnormalContent) {
        List<AbnormalDto> abnormalDtoList = Lists.newArrayList();
        if (null != abnormalContent && !"[]".equals(abnormalContent)) {
            abnormalDtoList.addAll(JSON.parseArray(abnormalContent, AbnormalDto.class));
        }
        abnormalDtoList.forEach(abnormalDto -> abnormalDto.setBelongDate(abnormalDto.getBelongDate() * 1000));
        return abnormalDtoList;
    }

    private List<WorkingHourAnalyzeDo> getWorkingHourAnalyzeList(String tenantId, Long startDate, Long endDate, List<Map> pageList, Boolean summaryStored, Boolean summaryCompleted) {
        List<WorkingHourAnalyzeDo> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(pageList)) {
            return list;
        }
        List<List<Map>> results = ListTool.split(pageList, 500);
        for (List<Map> rows : results) {
            if (CollectionUtils.isEmpty(rows)) {
                continue;
            }
            List<String> empIds = rows.stream().map(row -> row.get("empId")).filter(Objects::nonNull).map(String::valueOf).distinct().collect(Collectors.toList());
            List<String> productIds = rows.stream().map(row -> row.get("productId")).filter(Objects::nonNull).map(String::valueOf).distinct().collect(Collectors.toList());
            List<String> processIds = rows.stream().map(row -> row.get("processId")).filter(Objects::nonNull).map(String::valueOf).distinct().collect(Collectors.toList());
            String anyEmpIds = CollectionUtils.isEmpty(empIds) ? null : "'{" + StringUtils.join(empIds, ",").concat("}'");
            String anyProductIds = CollectionUtils.isEmpty(productIds) ? null : "'{" + StringUtils.join(productIds, ",").concat("}'");
            String anyProcessIds = CollectionUtils.isEmpty(processIds) ? null : "'{" + StringUtils.join(processIds, ",").concat("}'");
            list.addAll(workingHourAnalyzeDo.getDayAnalyseListByRange(tenantId, startDate, endDate, anyEmpIds, anyProductIds, anyProcessIds, summaryStored, summaryCompleted));
        }
        return distinctByKey(list, workingHour -> String.format("%s", workingHour.getAnalyzeId()));
    }

    @Override
    public PageResult<ArrangeMonthAnalyzeDto> getEmpArrangeMonthAnalyzeList(Integer pageNo, Integer pageSize, Long startDate, Long endDate, String tenantId, String accountId) {
        // 1. 参数初始化与验证
        startDate = Optional.ofNullable(startDate).orElse(DateUtil.getOnlyDate());
        endDate = Optional.ofNullable(endDate).orElse(startDate);
        pageNo = Optional.ofNullable(pageNo).orElse(DEFAULT_PAGE_NO);
        pageSize = Optional.ofNullable(pageSize).orElse(DEFAULT_PAGE_SIZE);
        Map<Long, AccountEmpDto> accountEmpMap = new HashMap<>();
        // 2. 构建查询DTO
        WfmMonthAnalysePageDto queryDto = buildQueryDto(pageNo, pageSize, startDate, endDate, tenantId, accountId, accountEmpMap);
        // 3. 执行分页查询
        PageList<Map> pageList = executePageQuery(tenantId, queryDto);
        if (CollectionUtils.isEmpty(pageList)) {
            return emptyPageResult(queryDto);
        }
        // 4. 获取关联数据
        List<WorkingHourAnalyzeDo> workingHours = getWorkingHourAnalyzeList(tenantId, startDate, endDate, pageList, false, false);
        Map<String, List<WorkingHourAnalyzeDo>> monthAnalyzeMap = groupWorkingHours(workingHours);
        // 5. 准备辅助数据
        Map<Long, MultiShiftSimpleVo> shiftMap = getShiftMap(tenantId, extractShiftIds(workingHours));
        Map<Long, Integer> specialDateMap = getSpecialDateMap(tenantId, startDate, endDate);
        List<WfmOrderInfo> orders = getWfmOrders(tenantId, extractOrderIds(workingHours));
        List<Long> processIds = workingHours.stream().map(WorkingHourAnalyzeDo::getProcessId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, WfmProcessInfo> processInfoMap = getWorkingProcessMap(tenantId, processIds);
        Map<String, BigDecimal> inventoryQuantityMap = new HashMap<>(pageList.size());
        summarizeEligibleOrders(tenantId, startDate, endDate, pageList, null, inventoryQuantityMap, null, true, false);
        // 6. 处理结果集
        processResultMaps(tenantId, pageList, monthAnalyzeMap, shiftMap, orders, specialDateMap, accountEmpMap, processInfoMap, inventoryQuantityMap, accountId, startDate, endDate);
        // 7. 返回分页结果
        return buildFinalResult(pageList, queryDto);
    }

    private PageResult<ArrangeMonthAnalyzeDto> emptyPageResult(WfmMonthAnalysePageDto dto) {
        return new PageResult<>(Collections.emptyList(), dto.getPageNo(), dto.getPageSize(), 0);
    }

    /**
     * 从工时分析数据中提取唯一的订单ID集合
     *
     * @param workingHours 工时分析数据列表
     * @return 去重后的订单ID列表（可能为空，但不会为null）
     */
    private List<Long> extractOrderIds(List<WorkingHourAnalyzeDo> workingHours) {
        if (CollectionUtils.isEmpty(workingHours)) {
            return Collections.emptyList();
        }
        return workingHours.stream().map(WorkingHourAnalyzeDo::getOrderId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    /**
     * 从分页查询结果中提取唯一的员工ID集合
     *
     * @param pageList 分页查询结果集，包含员工信息（每个Map中必须包含"empId"键）
     * @return 去重后的员工ID字符串列表（可能为空列表，但不会返回null）
     * 1. 方法会跳过null的empId值（但实际场景中empId通常不应为null）
     * 2. 返回的列表是经过distinct()去重的
     * 3. 使用{@code toString()}强制转换empId，确保结果为String类型
     */
    private List<String> extractEmpIds(PageList<Map> pageList) {
        return pageList.stream().map(row -> row.get("empId").toString()).distinct().collect(Collectors.toList());
    }

    // 提取班次ID集合
    private List<Long> extractShiftIds(List<WorkingHourAnalyzeDo> list) {
        return list.stream().map(WorkingHourAnalyzeDo::getShiftId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    private void processResultMaps(String tenantId, PageList<Map> pageList, Map<String, List<WorkingHourAnalyzeDo>> monthAnalyzeMap,
                                   Map<Long, MultiShiftSimpleVo> shiftMap, List<WfmOrderInfo> orders, Map<Long, Integer> specialDateMap,
                                   Map<Long, AccountEmpDto> accountEmpMap, Map<Long, WfmProcessInfo> processInfoMap,
                                   Map<String, BigDecimal> inventoryQuantityMap, String accountId, Long startDate, Long endDate) {
        List<Long> empIds = pageList.stream().map(row -> Long.parseLong(row.get("empId").toString())).distinct().collect(Collectors.toList());
        Map<Long, EmpWorkInfo> empInfoMap = getTenantEmpInfoMap(tenantId, empIds);
        pageList.forEach(map -> {
            Long empId = Long.parseLong(map.get("empId").toString());
            Optional.ofNullable(empInfoMap.get(empId)).ifPresent(empWorkInfo -> {
                map.put("workno", empWorkInfo.getWorkno());
                map.put("empName", empWorkInfo.getName());
            });
            // 添加统计信息
            map.putAll(getMonthStatisticMap(startDate, endDate, map, monthAnalyzeMap, shiftMap, orders, specialDateMap));
            // 添加账套信息
            Optional.ofNullable(accountEmpMap.get(empId)).ifPresent(accountEmp -> map.put("accountName", accountEmp.getLedgerName()));
            // 添加日期信息
            map.put("accountId", accountId);
            map.put("startDate", startDate * 1000);
            map.put("endDate", endDate * 1000);
            // 格式化工作时间
            Optional.ofNullable(map.get("actualWorkTime"))
                    .ifPresent(time -> {
                        BigDecimal actualWorkTime = (BigDecimal) time;
                        map.put("actualWorkTime", actualWorkTime.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toPlainString());
                    });
            Long processId = Long.parseLong(map.get("processId").toString());
            // 添加员工工作信息
            Optional.ofNullable(processInfoMap.get(processId)).ifPresent(processInfo -> map.put("workshopSection", processInfo.getWorkshopSection()));
            String mapKey = getMapKey(map);
            map.put("inventoryQuantity", inventoryQuantityMap.getOrDefault(mapKey, BigDecimal.ZERO));
        });
    }

    private PageResult<ArrangeMonthAnalyzeDto> buildFinalResult(PageList<Map> pageList, WfmMonthAnalysePageDto dto) {
        return new PageResult<>(FastjsonUtil.convertList(pageList, ArrangeMonthAnalyzeDto.class), dto.getPageNo(), dto.getPageSize(), pageList.getPaginator() != null ? pageList.getPaginator().getTotalCount() : dto.getTotal());
    }

    private Map<String, List<WorkingHourAnalyzeDo>> groupWorkingHours(List<WorkingHourAnalyzeDo> list) {
        return list.stream().collect(Collectors.groupingBy(
                row -> String.format("%s_%s_%s", row.getEmpId(), row.getProductId(), row.getProcessId())
        ));
    }

    private WfmMonthAnalysePageDto buildQueryDto(Integer pageNo, Integer pageSize, Long startDate, Long endDate, String tenantId, String accountId, Map<Long, AccountEmpDto> accountEmpMap) {
        WfmMonthAnalysePageDto dto = new WfmMonthAnalysePageDto();
        dto.setStartDate(startDate);
        dto.setEndDate(endDate);
        dto.setPageNo(pageNo);
        dto.setPageSize(pageSize);
        accountEmpMap.putAll(orgDataScopeService.getAccountEmployeesMap(tenantId, accountId));
        dto.setEmpIds(getFilterEmpIds(accountEmpMap));
        return dto;
    }

    private List<Long> getFilterEmpIds(Map<Long, AccountEmpDto> accountEmpMap) {
        if (Optional.ofNullable(accountEmpMap).isPresent() && !accountEmpMap.isEmpty()) {
            return new ArrayList<>(accountEmpMap.keySet());
        }
        return Collections.emptyList();
    }

    private PageList<Map> executePageQuery(String tenantId, WfmMonthAnalysePageDto dto) {
        PageBean pageBean = PageUtil.getNewPageBean(dto);
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(pageBean.getOrder()));
        return workingHourAnalyzeDo.getDayAnalyseListOrderByOrder(tenantId, dto.getStartDate(), dto.getEndDate(), dto.getKeywords(), dto.getEmpIds(), pageBean.getFilter(), dto.getDataScope(), pageBounds, null, null, null);
    }

    private Map<Long, EmpWorkInfo> getEmpInfoMap(String tenantId, List<String> empIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return new HashMap<>();
        }
        List<List<String>> empIdList = ListTool.split(empIds, 500);
        List<EmpWorkInfo> empList = Lists.newArrayList();
        for (List<String> rows : empIdList) {
            if (CollectionUtils.isEmpty(rows)) {
                continue;
            }
            DataFilter filter = DataFilter.eq("tenantId", tenantId).andIn("empId", rows).andNe("deleted", Boolean.TRUE.toString());
            try {
                empList.addAll(DataQuery.identifier(EMP_WORK_INFO_IDENTIFIER).decrypt().dept().specifyLanguage()
                        .queryInvisible().exp().limit(-1, 1).filter(filter, EmpWorkInfo.class, System.currentTimeMillis())
                        .getItems());
            } catch (Exception e) {
                log.error("Query {} exception:{}", EMP_WORK_INFO_IDENTIFIER, e.getMessage(), e);
            }
        }
        return empList.stream().collect(Collectors.toMap(emp -> Long.valueOf(emp.getEmpId()), Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public PageResult<ArrangeMonthAnalyzeDto> getArrangeMonthAnalyzeList(Integer pageNo, Integer pageSize, Long startDate, Long endDate, String tenantId, String accountId) {
        // 1. 参数初始化与验证
        startDate = Optional.ofNullable(startDate).orElse(DateUtil.getOnlyDate());
        endDate = Optional.ofNullable(endDate).orElse(startDate);
        pageNo = Optional.ofNullable(pageNo).orElse(DEFAULT_PAGE_NO);
        pageSize = Optional.ofNullable(pageSize).orElse(DEFAULT_PAGE_SIZE);

        log.info("getArrangeMonthAnalyzeList.Params: pageNo={}, pageSize={}, startDate={}, endDate={}, tenantId={}, accountId={}",
                pageNo, pageSize, startDate, endDate, tenantId, accountId);

        // 2. 构建查询DTO
        Map<Long, AccountEmpDto> accountEmpMap = new HashMap<>();
        WfmMonthAnalysePageDto queryDto = buildQueryDto(pageNo, pageSize, startDate, endDate, tenantId, accountId, accountEmpMap);
        List<Long> processIds = null;//getShiftGroupAndProcess(tenantId, startDate, endDate, queryDto.getEmpIds());

        PageBean pageBean = PageUtil.getNewPageBean(queryDto);
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(pageBean.getOrder()));
        String anyEmpIds = null;
        if (CollectionUtils.isEmpty(queryDto.getEmpIds())) {
            anyEmpIds = "'{" + StringUtils.join(queryDto.getEmpIds(), ",") + "}'";
        }
        PageList<Map> pageList = workingHourAnalyzeDo.getDayAnalyseListOrderByProcess(tenantId, startDate, endDate, processIds, pageBounds, anyEmpIds);
        if (CollectionUtils.isEmpty(pageList)) {
            log.warn("getArrangeMonthAnalyzeList.Return cause: getDayAnalyseListOrderByProcess Empty");
            return emptyPageResult(queryDto);
        }

        // 4. 获取关联数据
        List<WorkingHourAnalyzeDo> workingHours = getWorkingHourAnalyzeList(tenantId, startDate, endDate, pageList, false, false);
        Map<Long, MultiShiftSimpleVo> shiftMap = getShiftMap(tenantId, extractShiftIds(workingHours));
        Map<String, List<WorkingHourAnalyzeDo>> monthAnalyzeMap = workingHours.stream().collect(Collectors.groupingBy(row -> String.format("%s_%s", row.getProductId(), row.getProcessId())));
        Map<Long, Integer> specialDateMap = getSpecialDateMap(tenantId, startDate, endDate);
        List<WfmOrderInfo> orders = getWfmOrders(tenantId, extractOrderIds(workingHours));

        String accountName = "";
        List<String> ledgerNames = accountEmpMap.values().stream().map(AccountEmpDto::getLedgerName).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ledgerNames)) {
            accountName = ledgerNames.get(0);
        }
        processIds = extractWorkProcessIds(pageList);
        // 工时-计薪工序汇总
        Map<String, SalaryProcessStatisticResultDto> salaryProcessStatisticMap = getSalaryProcessStatisticMap(startDate, endDate, accountId, processIds);

        for (Map map : pageList) {
            String mapKey = String.format("%s_%s", map.get("productId"), map.get("processId"));
            Long finalStartDate = startDate;
            Long finalEndDate = endDate;
            Optional.ofNullable(monthAnalyzeMap.get(mapKey)).ifPresent(empMonth -> map.putAll(handleMonthAnalyze(finalStartDate, finalEndDate, empMonth, shiftMap, orders, specialDateMap)));
            map.put("startDate", startDate * 1000);
            map.put("endDate", endDate * 1000);
            map.put("accountId", accountId);
            map.put("accountName", accountName);
            SalaryProcessStatisticResultDto salaryProcessStatistic;
            if (salaryProcessStatisticMap.containsKey(mapKey) && null != (salaryProcessStatistic = salaryProcessStatisticMap.get(mapKey))) {
                map.put("completedQuantity", salaryProcessStatistic.getCompletedQuantity());
                map.put("inventoryQuantity", salaryProcessStatistic.getInventoryQuantity());
            } else {
                map.put("completedQuantity", 0);
                map.put("inventoryQuantity", 0);
            }
        }
        return buildFinalResult(pageList, queryDto);
    }

    private List<Long> extractWorkProcessIds(List<Map> processList) {
        return processList.stream()
                .map(process -> process.get("processId").toString()).map(Long::parseLong)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 工时-计薪工序汇总 Map
     *
     * @param startDate
     * @param endDate
     * @param accountId
     * @param processIds
     * @return
     */
    private Map<String, SalaryProcessStatisticResultDto> getSalaryProcessStatisticMap(Long startDate, Long endDate, String accountId, List<Long> processIds) {
        PageResult<SalaryProcessStatisticResultDto> salaryProcessStatisticPageResult = getProcessStatisticListForPay(
                DEFAULT_PAGE_NO, Integer.MAX_VALUE, startDate, endDate, accountId, processIds);
        Map<String, SalaryProcessStatisticResultDto> statisticResultMap = new HashMap<>();
        List<SalaryProcessStatisticResultDto> salaryProcessStatisticList;
        if (null != salaryProcessStatisticPageResult && CollectionUtils.isNotEmpty(salaryProcessStatisticList = salaryProcessStatisticPageResult.getItems())) {
            statisticResultMap = salaryProcessStatisticList.stream()
                    .collect(Collectors.toMap(it -> String.format("%s_%s", it.getProductId(), it.getProcessId()), Function.identity(), (v1, v2) -> v1));
        }
        return statisticResultMap;
    }

    /**
     * 工时-计薪工序汇总（薪资计算引擎拉取数据使用）
     *
     * @param pageNo
     * @param pageSize
     * @param startDate
     * @param endDate
     * @param accountId
     * @param processIds
     * @return
     */
    @Override
    public PageResult<SalaryProcessStatisticResultDto> getProcessStatisticListForPay(Integer pageNo, Integer pageSize,
                                                                                     Long startDate, Long endDate,
                                                                                     String accountId, List<Long> processIds) {
        UserInfo userInfo = UserContext.preCheckUser();
        String tenantId = userInfo.getTenantId();

        pageNo = Optional.ofNullable(pageNo).orElse(DEFAULT_PAGE_NO);
        pageSize = Optional.ofNullable(pageSize).orElse(DEFAULT_PAGE_SIZE);

        log.info("getProcessStatisticListForPay.Params: pageNo={}, pageSize={}, startDate={}, endDate={}, accountId={}, userInfo={}",
                pageNo, pageSize, startDate, endDate, accountId, FastjsonUtil.toJsonStr(userInfo));

        ProcessStatisticsPageQueryDto pageQueryDto = new ProcessStatisticsPageQueryDto();
        pageQueryDto.setPageNo(pageNo);
        pageQueryDto.setPageSize(pageSize);
        if (null != startDate) {
            pageQueryDto.setStartDate(DateUtil.getOnlyDate(new Date(startDate * 1000)));
        }
        if (null != endDate) {
            pageQueryDto.setEndDate(DateUtil.getOnlyDate(new Date(endDate * 1000)));
        }
        pageQueryDto.setIgnoreDataScope(Boolean.TRUE);

        if (StringUtils.isNotBlank(accountId) && CollectionUtils.isEmpty(processIds)) {
            Map<Long, AccountEmpDto> accountEmpMap = new HashMap<>();
            WfmMonthAnalysePageDto queryDto = buildQueryDto(pageNo, pageSize, startDate, endDate, tenantId, accountId, accountEmpMap);
            processIds = getShiftGroupAndProcess(tenantId, startDate, endDate, queryDto.getEmpIds());
        }
        pageQueryDto.setProcessIds(processIds);

        PageResult<ProcessStatisticsPageResultDto> pageResult = getProcessStatisticsPageResult(pageQueryDto, userInfo);
        List<ProcessStatisticsPageResultDto> items;
        if (null == pageResult || CollectionUtils.isEmpty(items = pageResult.getItems())) {
            return new PageResult<>(Collections.emptyList(), pageNo, pageSize, 0);
        }

        List<SalaryProcessStatisticResultDto> resultDtoList = items.stream().map(it -> {
            SalaryProcessStatisticResultDto resultDto = new SalaryProcessStatisticResultDto();
            resultDto.setAccountId(accountId);
            resultDto.setStartDate(startDate);
            resultDto.setEndDate(endDate);
            resultDto.setProductId(it.getProductId());
            resultDto.setProductName(it.getProductName());
            resultDto.setProcessId(it.getProcessId());
            resultDto.setProcessCode(it.getProcessCode());
            resultDto.setProcessName(it.getProcessName());
            resultDto.setCompletedQuantity(it.getCompletedQuantity());
            resultDto.setInventoryQuantity(it.getStoredQuantity());
            return resultDto;
        }).collect(Collectors.toList());
        return new PageResult<>(resultDtoList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    private List<Long> getShiftGroupAndProcess(String tenantId, Long startDate, Long endDate, List<Long> empIds) {
        List<WaShiftGroupDo> shiftGroups = empScheduleService.getShiftGroups(tenantId, startDate, endDate, empIds);
        if (CollectionUtils.isEmpty(shiftGroups)) {
            return Collections.emptyList();
        }
        // 提取所有工序ID并去重
        return shiftGroups.stream().filter(group -> StringUtils.isNotBlank(group.getLeftAssociation())).flatMap(group -> Arrays.stream(group.getLeftAssociation().split(",")))
                .map(String::trim).filter(StringUtils::isNotBlank).map(Long::valueOf).distinct().collect(Collectors.toList());
    }

    /**
     * 获取当月第一天的零点时间戳（毫秒级）
     *
     * @param timestampMillis 毫秒级时间戳
     * @return 当月第一天零点的毫秒级时间戳
     */
    private long getFirstDayOfMonth(long timestampMillis, boolean isUnix) {
        if (!isUnix) {
            return Instant.ofEpochMilli(timestampMillis).atZone(DEFAULT_ZONE).toLocalDate()
                    .with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay(DEFAULT_ZONE).toInstant().toEpochMilli();
        } else {
            return Instant.ofEpochMilli(timestampMillis).atZone(DEFAULT_ZONE).toLocalDate()
                    .with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay(DEFAULT_ZONE).toInstant().toEpochMilli() / 1000;
        }
    }

    /**
     * 获取当月最后一天的零点时间戳（毫秒级）
     *
     * @param timestampMillis 毫秒级时间戳
     * @return 当月最后一天零点的毫秒级时间戳
     */
    private long getLastDayOfMonth(long timestampMillis, boolean isUnix) {
        if (!isUnix) {
            return Instant.ofEpochMilli(timestampMillis).atZone(DEFAULT_ZONE).toLocalDate()
                    .with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay(DEFAULT_ZONE).toInstant().toEpochMilli();
        } else {
            return Instant.ofEpochMilli(timestampMillis).atZone(DEFAULT_ZONE).toLocalDate()
                    .with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay(DEFAULT_ZONE).toInstant().toEpochMilli() / 1000;
        }
    }

    @Async
    @Override
    public void refreshEmpDayWorkingHourOrders(SecurityUserInfo userInfo, WorkingHourOrderDto dto) {
        String tenantId = userInfo.getTenantId();
        if (CollectionUtils.isEmpty(dto.getOrderIds())) {
            return;
        }
        List<WfmOrderInfo> orders = getWfmOrders(tenantId, dto.getOrderIds());
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        WfmOrderInfo order = orders.get(0);
        if (null == order.getStorageType() || !"1".equals(order.getStorageType()) || null == order.getActualStorageTime()) {
            return;
        }
        UserInfo user = new UserInfo();
        user.setTenantId(userInfo.getTenantId());
        user.setUserId(0L);
        String lockKey = String.format("%s_%s", WFM_WORKING_HOUR_ANALYZE, tenantId);
        if (cacheService.containsKey(lockKey)) {
            log.error("当前租户有其他任务正在执行，tenantId：{}", tenantId);
        }
        cacheService.cacheValue(lockKey, "0.5", 600);

        WorkingHourDayAnalyseDto workingHourDayAnalyseDto = new WorkingHourDayAnalyseDto();
        long startDate = getFirstDayOfMonth(order.getActualStorageTime(), true);
        long endDate = getLastDayOfMonth(order.getActualStorageTime(), true);
        workingHourDayAnalyseDto.setStartDate(startDate);
        workingHourDayAnalyseDto.setEndDate(endDate);
        workingHourDayAnalyseDto.setOrderIds(dto.getOrderIds());
        try {
            analyzeWorkingHour(workingHourDayAnalyseDto, user);
        } catch (Exception e) {
            log.error("refreshEmpDayWorkingHourOrders tenant:{} exception:{},", tenantId, e.getMessage(), e);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @Override
    public PageResult<ArrangeMonthAnalyzeDto> getNonProcessEmpArrangeMonthAnalyzeList(Integer pageNo, Integer pageSize, Long startDate, Long endDate, String tenantId, String accountId) {
        // 1. 参数初始化与验证
        startDate = Optional.ofNullable(startDate).orElse(DateUtil.getOnlyDate());
        endDate = Optional.ofNullable(endDate).orElse(startDate);
        pageNo = Optional.ofNullable(pageNo).orElse(DEFAULT_PAGE_NO);
        pageSize = Optional.ofNullable(pageSize).orElse(DEFAULT_PAGE_SIZE);
        Map<Long, AccountEmpDto> accountEmpMap = orgDataScopeService.getAccountEmployeesMap(tenantId, accountId);
        List<Long> empIds = getFilterEmpIds(accountEmpMap);
        WfmMonthAnalysePageDto queryDto = new WfmMonthAnalysePageDto();
        queryDto.setPageNo(pageNo);
        queryDto.setPageSize(pageSize);
        if (CollectionUtils.isEmpty(empIds)) {
            return emptyPageResult(queryDto);
        }
        List<EmpAbnormalWorkingHourDto> abnormalWorkingHourDtoList = getEmpAbnormalWorkingHourList(tenantId, startDate, endDate, empIds, null, true);
        Map<Long, EmpWorkInfo> empInfoMap = getTenantEmpInfoMap(tenantId, empIds);
        if (CollectionUtils.isEmpty(abnormalWorkingHourDtoList)) {
            return emptyPageResult(queryDto);
        }
        Map<Long, List<EmpAbnormalWorkingHourDto>> empAbnormalWorkingHourMap = abnormalWorkingHourDtoList.stream().collect(Collectors.groupingBy(abnormal -> Long.parseLong(abnormal.getEmployeeId())));
        queryDto.setTotal(empAbnormalWorkingHourMap.size());
        if (-1 != pageSize) {
            empAbnormalWorkingHourMap = sortAndPaginateMap(empAbnormalWorkingHourMap, pageNo - 1, pageSize);
        }
        PageList<Map> pageList = getNonProcessAbnormalTimePageList(accountId, startDate, endDate, empInfoMap, accountEmpMap, empAbnormalWorkingHourMap);
        return buildFinalResult(pageList, queryDto);
    }

    private Map<Long, EmpWorkInfo> getTenantEmpInfoMap(String tenantId, List<Long> empIds) {
        Map<Long, EmpWorkInfo> empInfoMap = new HashMap<>(empIds.size());
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            List<List<Long>> empIdList = ListTool.split(empIds, 500);
            for (List<Long> rows : empIdList) {
                if (CollectionUtils.isEmpty(rows)) {
                    continue;
                }
                empInfoMap.putAll(getEmpInfoMap(tenantId, rows.stream().map(String::valueOf).collect(Collectors.toList())));
            }
            return empInfoMap;
        } finally {
            UserContext.removeSecurityUserInfo();
        }
    }

    private List<EmpAbnormalWorkingHourDto> getEmpAbnormalWorkingHourList(String tenantId, Long startDate, Long endDate, List<Long> empIds, List<Long> orderIds, boolean nonProcess) {
        List<EmpAbnormalWorkingHourDto> abnormalWorkingHourDtoList = Lists.newArrayList();
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            List<List<Long>> empIdList = ListTool.split(empIds, 500);
            for (List<Long> rows : empIdList) {
                if (CollectionUtils.isEmpty(rows)) {
                    continue;
                }
                abnormalWorkingHourDtoList.addAll(getEmpAbnormalWorkingHours(tenantId, startDate, endDate, rows, orderIds, nonProcess));
            }
            return abnormalWorkingHourDtoList;
        } finally {
            UserContext.removeSecurityUserInfo();
        }
    }

    private Map<Long, List<EmpAbnormalWorkingHourDto>> sortAndPaginateMap(Map<Long, List<EmpAbnormalWorkingHourDto>> originalMap, int pageNo, int pageSize) {
        List<Long> sortedKeys = originalMap.keySet().stream().sorted().collect(Collectors.toList());
        int totalKeys = sortedKeys.size();
        int start = Math.min(pageNo * pageSize, totalKeys);
        int end = Math.min(start + pageSize, totalKeys);
        List<Long> pageKeys = sortedKeys.subList(start, end);
        Map<Long, List<EmpAbnormalWorkingHourDto>> resultMap = new LinkedHashMap<>();
        for (Long key : pageKeys) {
            resultMap.put(key, originalMap.get(key));
        }
        return resultMap;
    }

    private PageList<Map> getNonProcessAbnormalTimePageList(String accountId, Long startDate, Long endDate,
                                                            Map<Long, EmpWorkInfo> empInfoMap,
                                                            Map<Long, AccountEmpDto> accountEmpMap,
                                                            Map<Long, List<EmpAbnormalWorkingHourDto>> empAbnormalWorkingHourMap) {
        PageList<Map> pageList = new PageList<>();
        for (Map.Entry<Long, List<EmpAbnormalWorkingHourDto>> entry : empAbnormalWorkingHourMap.entrySet()) {
            Map map = new HashMap();
            Long empId = entry.getKey();
            map.put("empId", empId);
            // 添加账套信息
            map.put("accountId", accountId);
            Optional.ofNullable(accountEmpMap.get(empId)).ifPresent(accountEmp -> map.put("accountName", accountEmp.getLedgerName()));
            map.put("startDate", startDate * 1000);
            map.put("endDate", endDate * 1000);
            BigDecimal abnormalTime = entry.getValue().stream().map(EmpAbnormalWorkingHourDto::getAbnormalTime).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal abnormalWorkTimeWage = BigDecimal.ZERO;
            for (EmpAbnormalWorkingHourDto abnormalDto : entry.getValue()) {
                BigDecimal perAbnormalWorkTime = new BigDecimal(Optional.ofNullable(abnormalDto.getAbnormalTime()).orElse("0"));
                BigDecimal perHourlyUnitPrice = new BigDecimal(Optional.ofNullable(abnormalDto.getHourlyUnitPrice()).orElse("0"));
                abnormalWorkTimeWage = abnormalWorkTimeWage.add(perAbnormalWorkTime.multiply(perHourlyUnitPrice));
            }
            map.put("abnormalWorkTime", abnormalTime);
            map.put("abnormalWorkTimeWage", abnormalWorkTimeWage);
            map.put("processName", "异常工时");
            Optional.ofNullable(empInfoMap.get(empId)).ifPresent(empWorkInfo -> {
                map.put("workno", empWorkInfo.getWorkno());
                map.put("empName", empWorkInfo.getName());
            });
            pageList.add(map);
        }
        return pageList;
    }
}