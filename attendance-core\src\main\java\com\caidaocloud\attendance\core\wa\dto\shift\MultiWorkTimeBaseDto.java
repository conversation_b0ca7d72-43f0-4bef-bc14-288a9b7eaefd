package com.caidaocloud.attendance.core.wa.dto.shift;

import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;
import java.util.Optional;

/**
 * 班次设置（多段班时间信息）基础信息DTO
 *
 * <AUTHOR>
 * @Date 2025/2/10
 */
@Data
@ApiModel("班次设置-多段班时间设置基础信息DTO")
public class MultiWorkTimeBaseDto {
    @ApiModelProperty("上班时间(单位分钟),eg:780")
    private Integer startTime;
    @ApiModelProperty("下班时间(单位分钟),eg:1080")
    private Integer endTime;
    @ApiModelProperty("最早上班打卡时间(单位分钟),eg:540")
    private Integer onDutyStartTime;
    @ApiModelProperty("最晚上班打卡时间(单位分钟),eg:600")
    private Integer onDutyEndTime;
    @ApiModelProperty("最早下班打卡时间(单位分钟),eg:1200")
    private Integer offDutyStartTime;
    @ApiModelProperty("最晚下班打卡时间(单位分钟),eg:1380")
    private Integer offDutyEndTime;
    @ApiModelProperty("上班时间归属标记: 1 当日、2 次日")
    private Integer startTimeBelong;
    @ApiModelProperty("下班时间归属标记: 1 当日、2 次日")
    private Integer endTimeBelong;
    @ApiModelProperty("最早上班打卡时间归属标记: 1 当日、2 次日、3 前日")
    private Integer onDutyStartTimeBelong;
    @ApiModelProperty("最晚上班打卡时间归属标记: 1 当日、2 次日")
    private Integer onDutyEndTimeBelong;
    @ApiModelProperty("最早下班打卡时间归属标记: 1 当日、2 次日")
    private Integer offDutyStartTimeBelong;
    @ApiModelProperty("最晚下班打卡时间归属标记: 1 当日、2 次日")
    private Integer offDutyEndTimeBelong;
    @ApiModelProperty("上班打卡必须打卡标记: true 必须、false 不必须")
    private Boolean onDutyMustClockIn;
    @ApiModelProperty("下班打卡必须打卡标记: true 必须、false 不必须")
    private Boolean offDutyMustClockIn;
    @ApiModelProperty("工作时长(单位分钟)-计算项")
    private Integer workTotalTime;
    @ApiModelProperty("班次工作时间跨夜标记: true 跨夜、false 不跨夜-计算项")
    private Boolean isNight = false;

    public Integer doGetOnDutyStartTimeForView() {
        int onDutyStartTime = this.onDutyStartTime;
        if (onDutyStartTime >= 0) {
            return onDutyStartTime;
        }
        onDutyStartTime += 1440;
        return onDutyStartTime;
    }

    public Integer doGetRealStartTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.startTimeBelong)) {
            return startTime + 1440;
        }
        return startTime;
    }

    public Integer doGetRealEndTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.endTimeBelong)
                || this.endTime <= this.startTime) {
            return endTime + 1440;
        }
        return endTime;
    }

    public Integer doGetRealOnDutyStartTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.onDutyStartTimeBelong)) {
            return onDutyStartTime + 1440;
        }
        return onDutyStartTime;
    }

    public Integer doGetRealOnDutyEndTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.onDutyEndTimeBelong)
                || this.onDutyEndTime < this.onDutyStartTime) {
            return onDutyEndTime + 1440;
        }
        return onDutyEndTime;
    }

    public Integer doGetRealOffDutyStartTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.offDutyStartTimeBelong)) {
            return offDutyStartTime + 1440;
        }
        return offDutyStartTime;
    }

    public Integer doGetRealOffDutyEndTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.offDutyEndTimeBelong)
                || this.offDutyEndTime < this.offDutyStartTime) {
            return offDutyEndTime + 1440;
        }
        return offDutyEndTime;
    }

    public boolean doCheckIsNight() {
        if (Optional.ofNullable(this.isNight).isPresent()) {
            return this.isNight;
        } else {
            return ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.offDutyEndTimeBelong);
        }
    }

    public boolean doGetOnDutyMustClockIn() {
        return Optional.ofNullable(this.onDutyMustClockIn).orElse(false);
    }

    public boolean doGetOffDutyMustClockIn() {
        return Optional.ofNullable(this.offDutyMustClockIn).orElse(false);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MultiWorkTimeBaseDto that = (MultiWorkTimeBaseDto) o;
        return Objects.equals(this.startTime, that.startTime)
                && Objects.equals(this.endTime, that.endTime)
                && Objects.equals(doGetRealOnDutyStartTime(), that.doGetRealOnDutyStartTime())
                && Objects.equals(doGetRealOnDutyEndTime(), that.doGetRealOnDutyEndTime())
                && Objects.equals(doGetRealOffDutyStartTime(), that.doGetRealOffDutyStartTime())
                && Objects.equals(doGetRealOffDutyEndTime(), that.doGetRealOffDutyEndTime());
    }

    @Override
    public int hashCode() {
        return Objects.hash(startTime, endTime, doGetRealOnDutyStartTime(), doGetRealOnDutyEndTime(), doGetRealOffDutyStartTime(), doGetRealOffDutyEndTime());
    }
}
